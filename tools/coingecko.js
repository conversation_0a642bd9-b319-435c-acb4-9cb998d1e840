import dotenv from "dotenv";
dotenv.config();

import axios from "axios";
import { HttpsProxyAgent } from "https-proxy-agent";
import { TokenMetadata } from "../src/models/TokenMetadata.js";

// https://api.geckoterminal.com/api/v2/simple/networks/solana/token_price/4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump,EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm?include_market_cap=true&mcap_fdv_fallback=true&include_24hr_vol=true&include_24hr_price_change=true&include_total_reserve_in_usd=true

// Proxy configuration matching the working curl format
const proxyUrl = `http://${process.env.DATA_IMPULSE_USERNAME || "d36adf6a4d155703e205"}:${process.env.DATA_IMPULSE_PASSWORD || "e7f34c5b037bbb5c"}@${process.env.DATA_IMPULSE_HOST || "gw.dataimpulse.com"}:${parseInt(process.env.DATA_IMPULSE_PORT) || 823}`;

// Create proxy agent
const proxyAgent = new HttpsProxyAgent(proxyUrl);

const geckov2 = axios.create({
  baseURL: `https://api.geckoterminal.com/api/v2`,
  timeout: 10000,
  httpsAgent: proxyAgent,
  proxy: false, // Disable axios built-in proxy since we're using httpsAgent
});

// Retry parameters
const MAX_RETRIES = 3;
const INITIAL_BACKOFF_MS = 50;
const BACKOFF_FACTOR = 2;

// get token pices (max 30 addresses)
export async function getTokenPrice(tokenAddress, vsCurrency = "usd") {
  if (!Array.isArray(tokenAddress)) {
    tokenAddress = [tokenAddress];
  }
  if (tokenAddress.length > 30) {
    throw new Error("Too many tokens to fetch");
  }

  let attempt = 0;
  let backoff = INITIAL_BACKOFF_MS;
  while (attempt < MAX_RETRIES) {
    try {
      const response = await geckov2.get(
        `/simple/networks/solana/token_price/${tokenAddress
          .map((t) => t.trim())
          .join(",")}?include_market_cap=true&mcap_fdv_fallback=true&include_24hr_vol=true&include_24hr_price_change=true&include_total_reserve_in_usd=true`
      );
      const prices = response.data.data.attributes.token_prices;
      const marketCaps = response.data.data.attributes.market_cap_usd;
      const volumes = response.data.data.attributes.h24_volume_usd;
      const priceChanges = response.data.data.attributes.h24_price_change_percentage;
      const reserves = response.data.data.attributes.total_reserve_in_usd;
      // Sort the result to match the order of tokenAddress input
      const result = tokenAddress.map((address) => ({
        tokenAddress: address.trim(),
        price: prices[address.trim()],
        market_cap: marketCaps[address.trim()],
        "24h_volume": volumes[address.trim()],
        "24h_price_change_percentage": priceChanges[address.trim()],
        total_reserve_usd: reserves[address.trim()],
      }));
      return result;
    } catch (error) {
      const status = error?.response?.status;
      // Only retry on network errors or 5xx errors
      if (
        (!status || (status >= 500 && status < 600)) &&
        attempt < MAX_RETRIES - 1
      ) {
        console.warn(
          `Retrying getTokenPrice (attempt ${attempt + 1}) after error:`,
          error.message || error
        );
        await new Promise((resolve) => setTimeout(resolve, backoff));
        backoff *= BACKOFF_FACTOR;
        attempt++;
        continue;
      } else {
        console.error("Error fetching token price from Gecko:", error);
        return null;
      }
    }
  }
  // If all retries failed
  console.error("Error fetching token price from Gecko: Max retries reached");
  return null;
}

// Enhanced getTokenMetadata with caching (Redis -> DB -> API)
export async function getTokenMetadata(tokenAddress) {
  if (!Array.isArray(tokenAddress)) {
    tokenAddress = [tokenAddress];
  }
  if (tokenAddress.length > 30) {
    throw new Error("Too many tokens to fetch");
  }

  try {
    // Step 1: Check cache and database first
    const cacheResult = await TokenMetadata.findByAddresses(tokenAddress);
    const foundTokens = cacheResult.found.map(token => token.toApiFormat());
    const missingAddresses = cacheResult.missing;

    // Step 2: If all tokens found in cache/DB, return immediately
    if (missingAddresses.length === 0) {
      // Sort results to match input order and preserve original address case in response
      return tokenAddress.map(addr => {
        const token = foundTokens.find(token => token.tokenAddress.toLowerCase() === addr.toLowerCase());
        if (token) {
          return { ...token, tokenAddress: addr };
        }
        return undefined;
      }).filter(Boolean);
    }

    // Step 3: Fetch missing tokens from API
    const apiTokens = await fetchTokenMetadataFromAPI(missingAddresses);

    if (!apiTokens) {
      // If API fails, return only cached/DB results
      return foundTokens;
    }

    // Step 4: Combine results and maintain order
    const allTokens = [...foundTokens, ...apiTokens];
    const orderedResults = tokenAddress.map(addr => {
      const token = allTokens.find(token => token.tokenAddress.toLowerCase() === addr.toLowerCase());
      if (token) {
        return { ...token, tokenAddress: addr };
      }
      return undefined;
    }).filter(Boolean);

    // Step 5: Background update cache and database (non-blocking)
    if (apiTokens.length > 0) {
      // Don't await this - let it run in background to minimize response time
      updateCacheAndDatabase(apiTokens).catch(error =>
        console.error('Background cache update failed:', error)
      );
    }

    return orderedResults;

  } catch (error) {
    console.error('Error in getTokenMetadata:', error);
    return null;
  }
}

// Internal function to fetch from GeckoTerminal API
async function fetchTokenMetadataFromAPI(tokenAddresses) {
  if (!tokenAddresses || tokenAddresses.length === 0) {
    return [];
  }

  let attempt = 0;
  let backoff = INITIAL_BACKOFF_MS;

  while (attempt < MAX_RETRIES) {
    try {
      const response = await geckov2.get(
        `/networks/solana/tokens/multi/${tokenAddresses
          .map((t) => t.trim())
          .join(",")}`
      );

      return response?.data?.data?.map((t) => ({
        tokenAddress: t.attributes.address,
        name: t.attributes.name,
        symbol: t.attributes.symbol,
        decimals: t.attributes.decimals,
        logo: t.attributes.image_url,
      })) || [];

    } catch (error) {
      const status = error?.response?.status;
      // Only retry on network errors or 5xx errors
      if (
        (!status || (status >= 500 && status < 600)) &&
        attempt < MAX_RETRIES - 1
      ) {
        console.warn(
          `Retrying fetchTokenMetadataFromAPI (attempt ${attempt + 1}) after error:`,
          error.message || error
        );
        await new Promise((resolve) => setTimeout(resolve, backoff));
        backoff *= BACKOFF_FACTOR;
        attempt++;
        continue;
      } else {
        console.error("Error fetching token metadata from Gecko:", error);
        return null;
      }
    }
  }

  console.error("Error fetching token metadata from Gecko: Max retries reached");
  return null;
}

// Background function to update cache and database
async function updateCacheAndDatabase(tokenDataArray) {
  try {
    if (!tokenDataArray || tokenDataArray.length === 0) {
      return;
    }

    // Update database (this also updates Redis cache via the model)
    await TokenMetadata.upsertMany(tokenDataArray);

    console.log(`Background update completed for ${tokenDataArray.length} tokens`);
  } catch (error) {
    console.error('Error in background cache/database update:', error);
  }
}