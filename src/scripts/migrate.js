import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';
import { query, testConnection } from '../config/database.js';
import bcrypt from 'bcryptjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigrations() {
    try {
        console.log('🚀 Starting database migration...');
        
        // Test database connection
        const connected = await testConnection();
        if (!connected) {
            throw new Error('Database connection failed');
        }
        
        const sqlDir = path.join(__dirname, '../../SQL');
        
        // Read and execute SQL files in order
        const sqlFiles = [
            '01_create_tables.sql',
            '02_create_indexes.sql',
            '03_insert_default_data.sql',
            '04_create_functions.sql',
            '05_add_tier_enable_disable.sql',
            '10_add_smart_money_endpoints.sql'
        ];
        
        for (const file of sqlFiles) {
            const filePath = path.join(sqlDir, file);
            
            if (!fs.existsSync(filePath)) {
                console.warn(`⚠️  SQL file not found: ${file}`);
                continue;
            }
            
            console.log(`📄 Executing ${file}...`);
            
            const sql = fs.readFileSync(filePath, 'utf8');

            try {
                // Execute the entire SQL file at once
                await query(sql);
            } catch (error) {
                // Some errors are expected (like "already exists")
                if (error.message.includes('already exists')) {
                    console.log(`   ℹ️  Skipping: ${error.message}`);
                } else {
                    console.error(`   ❌ Error in ${file}: ${error.message}`);
                    throw error;
                }
            }
            
            console.log(`   ✅ ${file} completed`);
        }
        
        // Create demo user with proper password hash
        await createDemoUser();
        
        console.log('✅ Database migration completed successfully!');
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
}

async function createDemoUser() {
    try {
        console.log('👤 Creating demo user...');
        
        // Check if demo user already exists
        const existingUser = await query(
            'SELECT id FROM users WHERE email = $1',
            ['<EMAIL>']
        );
        
        if (existingUser.rows.length > 0) {
            console.log('   ℹ️  Demo user already exists, updating password...');
            
            // Update with proper password hash
            const passwordHash = await bcrypt.hash('demo123', 12);
            await query(
                'UPDATE users SET password_hash = $1 WHERE email = $2',
                [passwordHash, '<EMAIL>']
            );
            
            console.log('   ✅ Demo user password updated');
        } else {
            console.log('   📝 Creating new demo user...');
            
            // Create new demo user
            const passwordHash = await bcrypt.hash('demo123', 12);
            const apiKey = generateApiKey();
            
            await query(
                `INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining)
                 VALUES ($1, $2, $3, $4, $5)`,
                ['<EMAIL>', passwordHash, apiKey, 1, 1000]
            );
            
            console.log('   ✅ Demo user created');
            console.log(`   📧 Email: <EMAIL>`);
            console.log(`   🔑 Password: demo123`);
            console.log(`   🗝️  API Key: ${apiKey}`);
        }
        
    } catch (error) {
        console.error('❌ Failed to create demo user:', error);
        throw error;
    }
}

function generateApiKey() {
    return crypto.randomBytes(32).toString('hex');
}

// Run migrations if this script is executed directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
    runMigrations()
        .then(() => {
            console.log('🎉 Migration script completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Migration script failed:', error);
            process.exit(1);
        });
}

export { runMigrations };
