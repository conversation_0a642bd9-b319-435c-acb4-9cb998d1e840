import { query } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function addKolFeedHistoryEndpoint() {
    try {
        console.log('🚀 Adding KOL feed history endpoint to access tiers...');

        // Read the SQL migration file
        const sqlPath = path.join(__dirname, '../../SQL/07_add_kol_feed_history_endpoint.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');

        // Execute the migration
        await query(sql);

        console.log('✅ KOL feed history endpoint added successfully!');

        // Verify the changes
        const result = await query(`
            SELECT name, allowed_endpoints 
            FROM access_tiers 
            WHERE '/api/v1/kol-feed/history' = ANY(allowed_endpoints)
            ORDER BY name
        `);

        console.log('\n📋 Tiers with KOL feed history endpoint access:');
        result.rows.forEach(tier => {
            console.log(`  - ${tier.name}`);
        });

    } catch (error) {
        console.error('❌ Error adding KOL feed history endpoint:', error);
        process.exit(1);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    addKolFeedHistoryEndpoint()
        .then(() => {
            console.log('\n🎉 Migration completed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        });
}

export { addKolFeedHistoryEndpoint };
