import { query } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function cleanupTestEndpoints() {
    try {
        console.log('🧹 Cleaning up test endpoints and streams...');

        // Read the SQL cleanup file
        const sqlPath = path.join(__dirname, '../../SQL/08_cleanup_test_endpoints.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');

        // Execute the cleanup
        const result = await query(sql);

        console.log('✅ Test endpoints and streams cleaned up successfully!');

        // Display the updated tier configuration
        if (result.rows && result.rows.length > 0) {
            console.log('\n📋 Updated Access Tiers Configuration:');
            console.log('┌─────────────┬─────────────────────────────────────┬──────────────┬───────────┬─────────┐');
            console.log('│ Tier        │ Description                         │ Credits/Month│ Price     │ Enabled │');
            console.log('├─────────────┼─────────────────────────────────────┼──────────────┼───────────┼─────────┤');
            
            result.rows.forEach(tier => {
                const name = tier.name.padEnd(11);
                const desc = tier.description.substring(0, 35).padEnd(35);
                const credits = tier.max_credits_per_month === -1 ? 'Unlimited'.padEnd(12) : tier.max_credits_per_month.toString().padEnd(12);
                const price = `$${tier.price_per_month}`.padEnd(9);
                const enabled = tier.is_enabled ? '✅' : '❌';
                
                console.log(`│ ${name} │ ${desc} │ ${credits} │ ${price} │ ${enabled}     │`);
            });
            
            console.log('└─────────────┴─────────────────────────────────────┴──────────────┴───────────┴─────────┘');

            console.log('\n📡 Available Streams:');
            result.rows.forEach(tier => {
                if (tier.allowed_streams && tier.allowed_streams.length > 0) {
                    console.log(`  ${tier.name}: ${tier.allowed_streams.join(', ')}`);
                }
            });

            console.log('\n🔗 Available Endpoints:');
            result.rows.forEach(tier => {
                if (tier.allowed_endpoints && tier.allowed_endpoints.length > 0) {
                    console.log(`  ${tier.name}: ${tier.allowed_endpoints.join(', ')}`);
                }
            });
        }

        console.log('\n🎯 Cleanup Summary:');
        console.log('  ❌ Removed: demo, data, analytics, search, submit, batch endpoints');
        console.log('  ❌ Removed: demo-stream, data-stream, analytics-stream, enterprise-stream');
        console.log('  ✅ Kept: KOL feed stream and history endpoint');
        console.log('  ✅ Kept: Status endpoint for health checks');

    } catch (error) {
        console.error('❌ Error cleaning up test endpoints:', error);
        process.exit(1);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    cleanupTestEndpoints()
        .then(() => {
            console.log('\n🎉 Cleanup completed successfully!');
            console.log('Your API now focuses exclusively on KOL feed functionality.');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Cleanup failed:', error);
            process.exit(1);
        });
}

export { cleanupTestEndpoints };
