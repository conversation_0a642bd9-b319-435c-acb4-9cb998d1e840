import { query, testConnection } from '../config/database.js';

async function addKolFeedStream() {
    try {
        console.log('🚀 Adding KOL feed stream...');
        
        // Test database connection
        const connected = await testConnection();
        if (!connected) {
            throw new Error('Database connection failed');
        }

        // Add KOL feed stream if it doesn't exist
        const checkStream = await query(
            'SELECT 1 FROM stream_definitions WHERE stream_name = $1',
            ['kol-feed']
        );

        if (checkStream.rows.length === 0) {
            // Insert KOL feed stream with basic tier access
            await query(`
                INSERT INTO stream_definitions (
                    stream_name,
                    description,
                    required_tier_id,
                    credits_per_message,
                    max_subscribers,
                    is_active,
                    metadata
                ) VALUES (
                    'kol-feed',
                    'Real-time KOL (Key Opinion Leader) trading activity feed',
                    2, -- basic tier
                    2, -- 2 credits per message
                    500, -- max subscribers
                    true,
                    '{"type": "event-driven", "source": "kol-feed", "format": "json"}'
                )
            `);

            // Add kol-feed to basic tier's allowed streams
            await query(`
                UPDATE access_tiers 
                SET allowed_streams = array_append(allowed_streams, 'kol-feed')
                WHERE name = 'basic'
            `);

            // Add kol-feed to premium tier's allowed streams
            await query(`
                UPDATE access_tiers 
                SET allowed_streams = array_append(allowed_streams, 'kol-feed')
                WHERE name = 'premium'
            `);

            // Add kol-feed to enterprise tier's allowed streams
            await query(`
                UPDATE access_tiers 
                SET allowed_streams = array_append(allowed_streams, 'kol-feed')
                WHERE name = 'enterprise'
            `);

            console.log('✅ KOL feed stream added successfully');
        } else {
            console.log('ℹ️ KOL feed stream already exists');
        }

    } catch (error) {
        console.error('❌ Error adding KOL feed stream:', error);
        process.exit(1);
    }
}

// Run the script
addKolFeedStream(); 