import { redis } from '../config/redis.js';
import { query } from '../config/database.js';

async function clearStreamCache() {
    try {
        console.log('🧹 Clearing Redis cache...');
        
        // Get all user and apikey cache keys
        const userKeys = await redis.keys('user:*');
        const apiKeyKeys = await redis.keys('apikey:*');
        const allKeys = [...userKeys, ...apiKeyKeys];
        
        if (allKeys.length > 0) {
            await redis.del(...allKeys);
            console.log(`✅ Cleared ${allKeys.length} user/apikey cache entries`);
        } else {
            console.log('ℹ️ No user/apikey cache entries found');
        }
        
        // Update user's allowed streams in the database
        console.log('🔄 Updating user allowed streams...');
        
        // Get all active users
        const users = await query(
            `SELECT u.id, u.tier_id, at.allowed_streams
             FROM users u
             JOIN access_tiers at ON u.tier_id = at.id
             WHERE u.is_active = true AND at.is_enabled = true`
        );
        
        // Update each user's allowed streams
        for (const user of users.rows) {
            // Get the latest allowed streams for the user's tier
            const tierStreams = await query(
                'SELECT allowed_streams FROM access_tiers WHERE id = $1',
                [user.tier_id]
            );
            
            if (tierStreams.rows.length > 0) {
                const allowedStreams = tierStreams.rows[0].allowed_streams;
                
                // Update user's allowed streams in the database
                await query(
                    `UPDATE users 
                     SET allowed_streams = $1
                     WHERE id = $2`,
                    [allowedStreams, user.id]
                );
            }
        }
        
        console.log('✅ User streams updated');
        
    } catch (error) {
        console.error('❌ Error clearing cache:', error);
        process.exit(1);
    }
}

// Run the script
clearStreamCache(); 