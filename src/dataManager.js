import SmartMoney from "./workers/smartMoney.js";
class DataManager {
  constructor() {
    this.workers = {
      smartMoney: new SmartMoney(),
    };
    // this.initialize();
  }

  async initialize() {
    try {
      // Initialize all the data processing tools.
      await this.workers.smartMoney.init();
      console.log("✅ Data Manager initialized");
    } catch (error) {
      console.error("❌ Error initializing Data Manager:", error);
      throw error;
    }
  }

  async stop() {
    try {
      // Stop all the data processing tools.
      this.workers.smartMoney.stop();
      console.log("🛑 Data Manager stopped");
    } catch (error) {
      console.error("❌ Error stopping Data Manager:", error);
      throw error;
    }
  }
}

export default DataManager;
