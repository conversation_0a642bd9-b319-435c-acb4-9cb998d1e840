/**
 * Utility functions for converting object keys between different case formats
 */

/**
 * Convert camelCase string to snake_case
 * Only converts if the string appears to be camelCase (starts with lowercase, contains uppercase)
 * @param {string} str - The camelCase string to convert
 * @returns {string} - The snake_case string
 */
function camelToSnake(str) {
  // Only convert if it looks like camelCase (starts with lowercase, contains uppercase)
  // Don't convert things like token addresses or other all-caps strings
  if (!/^[a-z]/.test(str) || !/[A-Z]/.test(str)) {
    return str;
  }
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

/**
 * Recursively convert all object keys from camelCase to snake_case
 * Handles nested objects, arrays, and preserves non-object values
 * @param {any} obj - The object/array/value to convert
 * @returns {any} - The converted object with snake_case keys
 */
function convertKeysToSnakeCase(obj) {
  // Handle null, undefined, or primitive values
  if (obj === null || obj === undefined || typeof obj !== 'object') {
    return obj;
  }

  // Handle arrays - recursively convert each element
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToSnakeCase(item));
  }

  // Handle objects - convert keys and recursively convert values
  const converted = {};
  for (const [key, value] of Object.entries(obj)) {
    const snakeKey = camelToSnake(key);
    converted[snakeKey] = convertKeysToSnakeCase(value);
  }

  return converted;
}

/**
 * Convert snake_case string to camelCase
 * @param {string} str - The snake_case string to convert
 * @returns {string} - The camelCase string
 */
function snakeToCamel(str) {
  return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
}

/**
 * Recursively convert all object keys from snake_case to camelCase
 * Handles nested objects, arrays, and preserves non-object values
 * @param {any} obj - The object/array/value to convert
 * @returns {any} - The converted object with camelCase keys
 */
function convertKeysToCamelCase(obj) {
  // Handle null, undefined, or primitive values
  if (obj === null || obj === undefined || typeof obj !== 'object') {
    return obj;
  }

  // Handle arrays - recursively convert each element
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }

  // Handle objects - convert keys and recursively convert values
  const converted = {};
  for (const [key, value] of Object.entries(obj)) {
    const camelKey = snakeToCamel(key);
    converted[camelKey] = convertKeysToCamelCase(value);
  }

  return converted;
}

export {
  camelToSnake,
  snakeToCamel,
  convertKeysToSnakeCase,
  convertKeysToCamelCase
};
