import { MongoClient } from "mongodb";
import dotenv from "dotenv";

dotenv.config();


const dbPoolConfig = {
  min: 5,
  max: 20,
  acquireTimeoutMillis: 60000,
  createTimeoutMillis: 30000,
  idleTimeoutMillis: 30000,
  reapIntervalMillis: 1000,
  createRetryIntervalMillis: 200,
};

const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_DB = "stalkreact";

if (!MONGODB_URI) {
  throw new Error("Missing required environment variable: MONGODB_URI");
}

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */
let cached = global.mongo;

if (!cached) {
  cached = global.mongo = { conn: null, promise: null };
}

async function connectToDatabase() {
  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = {
      maxPoolSize: dbPoolConfig.max,
      minPoolSize: dbPoolConfig.min,
      maxIdleTimeMS: dbPoolConfig.idleTimeoutMillis,
      waitQueueTimeoutMS: dbPoolConfig.acquireTimeoutMillis,
      connectTimeoutMS: dbPoolConfig.createTimeoutMillis,
      serverSelectionTimeoutMS: 5000, // Increased from 5000 to 30000 ms
      socketTimeoutMS: 45000, // How long to wait for operations to complete
      retryWrites: true,
      retryReads: true,
    };

    cached.promise = MongoClient.connect(MONGODB_URI, opts).then((client) => {
      const db = client.db(MONGODB_DB);

      // Add connection event handlers
      client.on('open', () => {
        console.log('✅ MongoDB connection opened');
      });

      client.on('close', () => {
        console.log('🔌 MongoDB connection closed');
      });

      client.on('error', (error) => {
        console.error('❌ MongoDB connection error:', error.message);
      });

      client.on('timeout', () => {
        console.error('⏰ MongoDB connection timeout');
      });

      return {
        client,
        db,
      };
    });
  }
  cached.conn = await cached.promise;
  return cached.conn;
}

// Get database instance with error handling
async function getDb(dbName = MONGODB_DB) {
  try {
    const { client } = await connectToDatabase();

    // Verify the connection is alive
    await client.db(dbName).command({ ping: 1 });

    return client.db(dbName);
  } catch (error) {
    console.error("Database connection error:", error);

    // If there's a connection issue, clear the cached connection
    if (cached.conn) {
      try {
        await cached.conn.client.close();
      } catch (closeError) {
        console.error("Error closing connection:", closeError);
      }
      cached.conn = null;
      cached.promise = null;
    }

    throw new Error(`Unable to connect to database: ${error.message}`);
  }
}

// Test MongoDB connection
export const testMongoConnection = async () => {
  try {
    const { client } = await connectToDatabase();

    // Test the connection with a ping
    await client.db(MONGODB_DB).command({ ping: 1 });

    console.log('✅ MongoDB connected successfully');
    return true;
  } catch (err) {
    console.error('❌ MongoDB connection failed:', err.message);
    return false;
  }
};

// MongoDB helper functions
export const mongodb = {
  // Get a collection
  getCollection: async (collectionName, dbName = MONGODB_DB) => {
    try {
      const db = await getDb(dbName);
      return db.collection(collectionName);
    } catch (error) {
      console.error('MongoDB get collection error:', error);
      throw error;
    }
  },

  // Insert one document
  insertOne: async (collectionName, document, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.insertOne(document);
      return result;
    } catch (error) {
      console.error('MongoDB insert error:', error);
      throw error;
    }
  },

  // Insert many documents
  insertMany: async (collectionName, documents, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.insertMany(documents);
      return result;
    } catch (error) {
      console.error('MongoDB insert many error:', error);
      throw error;
    }
  },

  // Find one document
  findOne: async (collectionName, query, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.findOne(query, options);
      return result;
    } catch (error) {
      console.error('MongoDB find one error:', error);
      throw error;
    }
  },

  // Find many documents
  find: async (collectionName, query = {}, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.find(query, options).toArray();
      return result;
    } catch (error) {
      console.error('MongoDB find error:', error);
      throw error;
    }
  },

  // Update one document
  updateOne: async (collectionName, filter, update, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.updateOne(filter, update, options);
      return result;
    } catch (error) {
      console.error('MongoDB update one error:', error);
      throw error;
    }
  },

  // Update many documents
  updateMany: async (collectionName, filter, update, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.updateMany(filter, update, options);
      return result;
    } catch (error) {
      console.error('MongoDB update many error:', error);
      throw error;
    }
  },

  // Delete one document
  deleteOne: async (collectionName, filter, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.deleteOne(filter);
      return result;
    } catch (error) {
      console.error('MongoDB delete one error:', error);
      throw error;
    }
  },

  // Delete many documents
  deleteMany: async (collectionName, filter, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.deleteMany(filter);
      return result;
    } catch (error) {
      console.error('MongoDB delete many error:', error);
      throw error;
    }
  },

  // Count documents
  countDocuments: async (collectionName, query = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.countDocuments(query);
      return result;
    } catch (error) {
      console.error('MongoDB count error:', error);
      throw error;
    }
  },

  // Create index
  createIndex: async (collectionName, indexSpec, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.createIndex(indexSpec, options);
      return result;
    } catch (error) {
      console.error('MongoDB create index error:', error);
      throw error;
    }
  },

  // Aggregate
  aggregate: async (collectionName, pipeline, options = {}, dbName = MONGODB_DB) => {
    try {
      const collection = await mongodb.getCollection(collectionName, dbName);
      const result = await collection.aggregate(pipeline, options).toArray();
      return result;
    } catch (error) {
      console.error('MongoDB aggregate error:', error);
      throw error;
    }
  }
};

// Track if MongoDB has been closed
let mongoClosed = false;

// Graceful shutdown function
async function closeDatabase() {
  if (mongoClosed) {
    console.log('MongoDB connection already closed, skipping...');
    return;
  }

  if (cached.conn) {
    try {
      await cached.conn.client.close();
      cached.conn = null;
      cached.promise = null;
      mongoClosed = true;
      console.log('MongoDB connection closed');
    } catch (error) {
      console.error('Error closing MongoDB connection:', error.message);
      mongoClosed = true; // Mark as closed even if there was an error
    }
  } else {
    mongoClosed = true;
    console.log('MongoDB connection already closed');
  }
}

export { connectToDatabase, getDb, closeDatabase };
