import dotenv from 'dotenv';
import Redis from 'ioredis';
import assert from 'assert';

dotenv.config();

const redis = new Redis(process.env.REDIS_CENTRAL_URL);

// Add Redis connection event handlers for observability
redis.on('connect', () => {
  console.log('✅ RedisCentral connected successfully');
});

redis.on('error', (err) => {
  console.error('❌ RedisCentral connection error:', err.message);
});

redis.on('ready', () => {
  console.log('✅ RedisCentral ready for operations');
});

redis.on('close', () => {
  console.log('🛑 RedisCentral connection closed');
});

redis.on('end', () => {
  console.log('🛑 RedisCentral connection ended');
});

/**
 * Subscribes to a Redis channel with robust error handling and input validation.
 * @param {string} channel - The Redis channel to subscribe to.
 * @param {function} callback - The callback to invoke on message.
 * @returns {Promise<boolean>} - Returns true if subscribed, false otherwise.
 */
const subscribeCentral = async (channel, callback) => {
  try {
    // Input validation
    assert(typeof channel === 'string' && channel.length > 0, 'Channel must be a non-empty string');
    assert(typeof callback === 'function', 'Callback must be a function');

    await redis.subscribe(channel);
    redis.on('message', (receivedChannel, message) => {
      if (receivedChannel === channel) {
        try {
          const parsedMessage = JSON.parse(message);
          callback(parsedMessage);
        } catch (parseError) {
          console.error('[RedisCentral] Failed to parse message as JSON:', {
            channel: receivedChannel,
            message,
            error: parseError?.message || parseError,
            time: new Date().toISOString(),
          });
          callback(message); // Fallback to raw message
        }
      } else {
        console.warn(`[RedisCentral] Received message on unexpected channel: ${receivedChannel} (expected: ${channel})`);
      }
    });
    return true;
  } catch (error) {
    console.error('[RedisCentral Subscribe Error]', {
      channel,
      error: error?.message || error,
      stack: error?.stack,
      time: new Date().toISOString(),
    });
    return false;
  }
};

/**
 * Gracefully closes the Redis Central connection.
 * @returns {Promise<void>} - Promise that resolves when connection is closed.
 */
const closeCentralConnection = async () => {
  try {
    if (redis.status === 'ready' || redis.status === 'connecting') {
      console.log('🛑 Closing RedisCentral connection...');
      await redis.quit();
      console.log('✅ RedisCentral connection closed gracefully');
    } else {
      console.log('ℹ️ RedisCentral connection already closed');
    }
  } catch (error) {
    console.error('❌ Error closing RedisCentral connection:', error.message);
    // Force disconnect if graceful quit fails
    try {
      redis.disconnect();
      console.log('✅ RedisCentral connection force disconnected');
    } catch (forceError) {
      console.error('❌ Error force disconnecting RedisCentral:', forceError.message);
    }
  }
};

/**
 * Gets the current Redis Central connection status.
 * @returns {string} - Current connection status.
 */
const getCentralConnectionStatus = () => {
  return redis.status;
};

/**
 * Checks if Redis Central connection is ready.
 * @returns {boolean} - True if connection is ready, false otherwise.
 */
const isCentralConnectionReady = () => {
  return redis.status === 'ready';
};

export {
  subscribeCentral,
  closeCentralConnection,
  getCentralConnectionStatus,
  isCentralConnectionReady,
  redis as redisCentral
};