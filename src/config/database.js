import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;

// Database configuration
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || 'api_engine',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,

    // CRITICAL FIX: Increased connection pool for high concurrency
    max: parseInt(process.env.DB_MAX_CONNECTIONS) || 100, // Increased from 20
    min: parseInt(process.env.DB_MIN_CONNECTIONS) || 10,  // Add minimum connections

    // Optimized timeouts for better performance
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 5000, // Increased from 2000
    acquireTimeoutMillis: 60000,   // Add acquire timeout

    // Add connection validation and rotation
    allowExitOnIdle: true,
    maxUses: 7500, // Rotate connections after 7500 uses
};

// Create connection pool
const pool = new Pool(dbConfig);

// CRITICAL FIX: Add connection pool monitoring
pool.on('connect', () => {
    if (process.env.NODE_ENV === 'development') {
        console.log(`✅ Database connection established. Pool size: ${pool.totalCount}, Idle: ${pool.idleCount}`);
    }
});

pool.on('acquire', () => {
    if (process.env.NODE_ENV === 'development' && process.env.DEBUG_DATABASE === 'true') {
        console.log(`📊 Database connection acquired. Pool size: ${pool.totalCount}, Idle: ${pool.idleCount}`);
    }
});

pool.on('remove', () => {
    if (process.env.NODE_ENV === 'development') {
        console.log(`🗑️ Database connection removed. Pool size: ${pool.totalCount}, Idle: ${pool.idleCount}`);
    }
});

// Handle pool errors
pool.on('error', (err) => {
    console.error('❌ Unexpected error on idle client', err);
    // Don't exit process immediately in production
    if (process.env.NODE_ENV !== 'production') {
        process.exit(-1);
    }
});

// Test database connection
export const testConnection = async () => {
    try {
        const client = await pool.connect();
        const result = await client.query('SELECT NOW()');
        client.release();
        console.log('✅ Database connected successfully at:', result.rows[0].now);
        return true;
    } catch (err) {
        console.error('❌ Database connection failed:', err.message);
        return false;
    }
};

// Execute query with error handling
export const query = async (text, params) => {
    const start = Date.now();
    try {
        const result = await pool.query(text, params);
        const duration = Date.now() - start;
        
        // Log query execution in development mode for debugging
        if (process.env.NODE_ENV === 'development' && process.env.DEBUG_DATABASE === 'true') {
            console.log('Executed query', { text: text.substring(0, 100), duration, rows: result.rowCount });
        }
        
        return result;
    } catch (error) {
        console.error('Database query error:', error);
        throw error;
    }
};

// Get a client from the pool for transactions
export const getClient = async () => {
    return await pool.connect();
};

// Close the pool
export const closePool = async () => {
    // Check if pool is already ended by checking if it's still available
    if (!pool || pool.ended) {
        console.log('Database pool already closed, skipping...');
        return;
    }

    try {
        await pool.end();
        console.log('Database pool closed');
    } catch (error) {
        if (error.message.includes('Called end on pool more than once') ||
            error.message.includes('Pool was ended')) {
            console.log('Database pool already closed');
        } else {
            console.error('Error closing database pool:', error.message);
            throw error;
        }
    }
};

export default pool;
