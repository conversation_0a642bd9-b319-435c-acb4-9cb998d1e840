// CRITICAL FIX: Comprehensive input validation middleware
import validator from 'validator';

/**
 * Sanitize string input to prevent XSS attacks
 * @param {string} str - Input string to sanitize
 * @param {number} maxLength - Maximum allowed length
 * @returns {string} - Sanitized string
 */
export const sanitizeString = (str, maxLength = 255) => {
    if (typeof str !== 'string') return '';
    return validator.escape(str.trim().substring(0, maxLength));
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} - True if valid email
 */
export const validateEmail = (email) => {
    return validator.isEmail(email) && email.length <= 255;
};

/**
 * Validate API key format
 * @param {string} apiKey - API key to validate
 * @returns {boolean} - True if valid API key format
 */
export const validateApiKey = (apiKey) => {
    return typeof apiKey === 'string' && 
           apiKey.length >= 32 && 
           apiKey.length <= 64 && 
           /^[a-zA-Z0-9_-]+$/.test(apiKey);
};

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {object} - Validation result with isValid and errors
 */
export const validatePassword = (password) => {
    const errors = [];
    
    if (!password || typeof password !== 'string') {
        errors.push('Password is required');
        return { isValid: false, errors };
    }
    
    if (password.length < 8) {
        errors.push('Password must be at least 8 characters long');
    }
    
    if (password.length > 128) {
        errors.push('Password must be no more than 128 characters long');
    }
    
    if (!/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[0-9]/.test(password)) {
        errors.push('Password must contain at least one number');
    }
    
    return { isValid: errors.length === 0, errors };
};

/**
 * General input validation middleware
 * @param {object} schema - Validation schema
 * @returns {function} - Express middleware function
 */
export const validateInput = (schema) => {
    return (req, res, next) => {
        const errors = [];
        
        // Validate body parameters
        if (schema.body) {
            for (const [field, rules] of Object.entries(schema.body)) {
                const value = req.body[field];
                
                // Required field validation
                if (rules.required && (value === undefined || value === null || value === '')) {
                    errors.push(`${field} is required`);
                    continue;
                }
                
                // Skip validation for optional empty fields
                if (value === undefined || value === null || value === '') {
                    continue;
                }
                
                // Type validation
                if (rules.type === 'email' && !validateEmail(value)) {
                    errors.push(`${field} must be a valid email address`);
                }
                
                if (rules.type === 'string' && typeof value !== 'string') {
                    errors.push(`${field} must be a string`);
                }
                
                if (rules.type === 'number' && (typeof value !== 'number' || isNaN(value))) {
                    errors.push(`${field} must be a valid number`);
                }
                
                if (rules.type === 'boolean' && typeof value !== 'boolean') {
                    errors.push(`${field} must be a boolean`);
                }
                
                if (rules.type === 'array' && !Array.isArray(value)) {
                    errors.push(`${field} must be an array`);
                }
                
                // Length validation for strings
                if (typeof value === 'string') {
                    if (rules.minLength && value.length < rules.minLength) {
                        errors.push(`${field} must be at least ${rules.minLength} characters long`);
                    }
                    
                    if (rules.maxLength && value.length > rules.maxLength) {
                        errors.push(`${field} must be no more than ${rules.maxLength} characters long`);
                    }
                }
                
                // Number range validation
                if (typeof value === 'number') {
                    if (rules.min !== undefined && value < rules.min) {
                        errors.push(`${field} must be at least ${rules.min}`);
                    }
                    
                    if (rules.max !== undefined && value > rules.max) {
                        errors.push(`${field} must be no more than ${rules.max}`);
                    }
                }
                
                // Custom validation
                if (rules.custom && !rules.custom(value)) {
                    errors.push(rules.customMessage || `${field} is invalid`);
                }
                
                // Password validation
                if (rules.type === 'password') {
                    const passwordValidation = validatePassword(value);
                    if (!passwordValidation.isValid) {
                        errors.push(...passwordValidation.errors);
                    }
                }
                
                // Sanitize string values
                if (rules.type === 'string' && rules.sanitize !== false) {
                    req.body[field] = sanitizeString(value, rules.maxLength);
                }
            }
        }
        
        // Validate query parameters
        if (schema.query) {
            for (const [field, rules] of Object.entries(schema.query)) {
                const value = req.query[field];
                
                if (rules.required && !value) {
                    errors.push(`Query parameter ${field} is required`);
                    continue;
                }
                
                if (value && rules.type === 'number') {
                    const numValue = parseInt(value);
                    if (isNaN(numValue)) {
                        errors.push(`Query parameter ${field} must be a valid number`);
                    } else {
                        req.query[field] = numValue;
                    }
                }
                
                if (value && rules.type === 'string' && rules.sanitize !== false) {
                    req.query[field] = sanitizeString(value, rules.maxLength || 255);
                }
            }
        }
        
        // Validate URL parameters
        if (schema.params) {
            for (const [field, rules] of Object.entries(schema.params)) {
                const value = req.params[field];
                
                if (rules.required && !value) {
                    errors.push(`URL parameter ${field} is required`);
                    continue;
                }
                
                if (value && rules.type === 'uuid' && !validator.isUUID(value)) {
                    errors.push(`URL parameter ${field} must be a valid UUID`);
                }
                
                if (value && rules.type === 'number') {
                    const numValue = parseInt(value);
                    if (isNaN(numValue)) {
                        errors.push(`URL parameter ${field} must be a valid number`);
                    } else {
                        req.params[field] = numValue;
                    }
                }
            }
        }
        
        if (errors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors,
                timestamp: new Date().toISOString()
            });
        }
        
        next();
    };
};

/**
 * Common validation schemas
 */
export const schemas = {
    userRegistration: {
        body: {
            email: { required: true, type: 'email' },
            password: { required: true, type: 'password' },
            tier_id: { 
                type: 'number', 
                min: 1, 
                max: 10,
                custom: (val) => Number.isInteger(val),
                customMessage: 'tier_id must be a valid integer between 1 and 10'
            }
        }
    },
    
    userLogin: {
        body: {
            email: { required: true, type: 'email' },
            password: { required: true, type: 'string', minLength: 1, maxLength: 128 }
        }
    },
    
    adminCreate: {
        body: {
            name: { required: true, type: 'string', maxLength: 100 },
            email: { required: true, type: 'email' },
            permissions: { 
                type: 'array',
                custom: (val) => Array.isArray(val) && val.every(p => typeof p === 'string'),
                customMessage: 'permissions must be an array of strings'
            }
        }
    },
    
    tierUpdate: {
        body: {
            name: { type: 'string', maxLength: 50 },
            description: { type: 'string', maxLength: 500 },
            max_credits_per_month: { type: 'number', min: -1 },
            max_requests_per_minute: { type: 'number', min: -1 },
            max_websocket_connections: { type: 'number', min: -1 },
            price_per_month: { type: 'number', min: 0 }
        },
        params: {
            tierId: { required: true, type: 'number', min: 1 }
        }
    },
    
    websocketSubscription: {
        body: {
            stream: { required: true, type: 'string', maxLength: 100 },
            parameters: { 
                type: 'object',
                custom: (val) => typeof val === 'object' && val !== null,
                customMessage: 'parameters must be a valid object'
            }
        }
    }
};

/**
 * Sanitize request payload for logging (remove sensitive data)
 * @param {object} payload - Request payload to sanitize
 * @returns {object} - Sanitized payload
 */
export const sanitizePayload = (payload) => {
    if (!payload || typeof payload !== 'object') return payload;
    
    const sensitiveFields = ['password', 'api_key', 'token', 'secret', 'key'];
    const sanitized = { ...payload };
    
    for (const field of sensitiveFields) {
        if (sanitized[field]) {
            sanitized[field] = '[REDACTED]';
        }
    }
    
    return sanitized;
};
