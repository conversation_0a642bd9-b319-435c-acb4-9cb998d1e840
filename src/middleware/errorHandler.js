/**
 * Comprehensive Error Handling Middleware
 * Sanitizes error responses and prevents sensitive information exposure
 */

// Error types that should be sanitized
const SENSITIVE_ERROR_TYPES = [
    'SyntaxError',
    'ValidationError', 
    'CastError',
    'MongoError',
    'SequelizeError',
    'TypeError',
    'ReferenceError'
];

// Common error patterns to sanitize
const SENSITIVE_PATTERNS = [
    /at\s+.*\(.*node_modules.*\)/gi,  // Node modules stack traces
    /at\s+.*\(.*\/.*\.js:\d+:\d+\)/gi, // File paths with line numbers
    /\/Users\/<USER>\//gi,                // User home directories
    /\/home\/<USER>\//gi,                 // Linux home directories
    /C:\\.*?\\/gi,                     // Windows paths
    /password/gi,                      // Password mentions
    /secret/gi,                        // Secret mentions
    /token/gi,                         // Token mentions (in error messages)
    /key/gi                            // Key mentions
];

/**
 * Sanitize error message to remove sensitive information
 */
function sanitizeErrorMessage(message) {
    if (!message || typeof message !== 'string') {
        return 'An error occurred while processing your request';
    }

    let sanitized = message;

    // Remove sensitive patterns
    SENSITIVE_PATTERNS.forEach(pattern => {
        sanitized = sanitized.replace(pattern, '[REDACTED]');
    });

    // Common JSON parsing error sanitization
    if (sanitized.includes('Unexpected token')) {
        return 'Invalid JSON format in request body';
    }

    if (sanitized.includes('is not valid JSON')) {
        return 'Invalid JSON format in request body';
    }

    if (sanitized.includes('Unexpected end of JSON input')) {
        return 'Incomplete JSON in request body';
    }

    // Database connection errors
    if (sanitized.includes('ECONNREFUSED') || sanitized.includes('connection refused')) {
        return 'Service temporarily unavailable';
    }

    if (sanitized.includes('timeout') || sanitized.includes('ETIMEDOUT')) {
        return 'Request timeout - please try again';
    }

    // Authentication errors
    if (sanitized.includes('jwt') || sanitized.includes('JWT')) {
        return 'Authentication token invalid';
    }

    // Rate limiting errors (keep these as they're user-facing)
    if (sanitized.includes('Too many requests')) {
        return sanitized;
    }

    // Validation errors (keep these as they're user-facing)
    if (sanitized.includes('Validation failed') || sanitized.includes('required')) {
        return sanitized;
    }

    // If message is too long or contains suspicious content, use generic message
    if (sanitized.length > 200 || sanitized.includes('[REDACTED]')) {
        return 'An error occurred while processing your request';
    }

    return sanitized;
}

/**
 * Determine appropriate HTTP status code based on error
 */
function getErrorStatusCode(error) {
    // Explicit status code
    if (error.status || error.statusCode) {
        return error.status || error.statusCode;
    }

    // JSON parsing errors
    if (error.type === 'entity.parse.failed' || 
        error.message?.includes('JSON') || 
        error.name === 'SyntaxError') {
        return 400;
    }

    // Validation errors
    if (error.name === 'ValidationError' || 
        error.message?.includes('validation')) {
        return 400;
    }

    // Authentication errors
    if (error.message?.includes('token') || 
        error.message?.includes('auth')) {
        return 401;
    }

    // Permission errors
    if (error.message?.includes('permission') || 
        error.message?.includes('access')) {
        return 403;
    }

    // Not found errors
    if (error.message?.includes('not found')) {
        return 404;
    }

    // Rate limiting errors
    if (error.message?.includes('Too many requests')) {
        return 429;
    }

    // Default to 500 for unknown errors
    return 500;
}

/**
 * Create user-friendly error response
 */
function createErrorResponse(error, req) {
    const statusCode = getErrorStatusCode(error);
    const sanitizedMessage = sanitizeErrorMessage(error.message);

    const response = {
        success: false,
        error: sanitizedMessage,
        timestamp: new Date().toISOString(),
        path: req.originalUrl,
        method: req.method
    };

    // Add error code for specific error types
    if (statusCode === 400) {
        response.code = 'BAD_REQUEST';
    } else if (statusCode === 401) {
        response.code = 'UNAUTHORIZED';
    } else if (statusCode === 403) {
        response.code = 'FORBIDDEN';
    } else if (statusCode === 404) {
        response.code = 'NOT_FOUND';
    } else if (statusCode === 429) {
        response.code = 'RATE_LIMITED';
    } else if (statusCode >= 500) {
        response.code = 'INTERNAL_ERROR';
    }

    // Add helpful hints for common errors
    if (statusCode === 400 && sanitizedMessage.includes('JSON')) {
        response.hint = 'Please check your request body format and ensure it contains valid JSON';
    }

    if (statusCode === 401) {
        response.hint = 'Please check your authentication credentials and try again';
    }

    if (statusCode === 429) {
        response.hint = 'Please wait before making another request';
    }

    return { statusCode, response };
}

/**
 * Main error handling middleware
 */
export const errorHandler = (error, req, res, next) => {
    // Log error for debugging (sanitized for production)
    if (process.env.NODE_ENV === 'development') {
        console.error('🚨 Error Handler - Full Error:', {
            name: error.name,
            message: error.message,
            stack: error.stack,
            url: req.originalUrl,
            method: req.method,
            body: req.body,
            user: req.user?.email || 'anonymous'
        });
    } else {
        // Production logging (sanitized)
        console.error('🚨 Error Handler:', {
            name: error.name,
            message: sanitizeErrorMessage(error.message),
            url: req.originalUrl,
            method: req.method,
            user: req.user?.email || 'anonymous',
            timestamp: new Date().toISOString()
        });
    }

    // Create sanitized response
    const { statusCode, response } = createErrorResponse(error, req);

    // Send response
    res.status(statusCode).json(response);
};

/**
 * JSON parsing error handler middleware
 * Must be placed after express.json() middleware
 */
export const jsonErrorHandler = (error, req, res, next) => {
    if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
        return res.status(400).json({
            success: false,
            error: 'Invalid JSON format in request body',
            code: 'INVALID_JSON',
            hint: 'Please check your request body format and ensure it contains valid JSON',
            timestamp: new Date().toISOString(),
            path: req.originalUrl,
            method: req.method
        });
    }
    next(error);
};

/**
 * 404 Not Found handler
 */
export const notFoundHandler = (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        code: 'NOT_FOUND',
        hint: 'Please check the URL and HTTP method',
        path: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString(),
        available_endpoints: {
            authentication: '/auth',
            api: '/api/v1',
            websocket_info: '/ws-api',
            health: '/health',
            documentation: '/docs'
        }
    });
};

export default {
    errorHandler,
    jsonErrorHandler,
    notFoundHandler,
    sanitizeErrorMessage,
    createErrorResponse
};
