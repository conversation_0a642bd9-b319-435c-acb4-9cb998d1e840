import { pubsub, cache } from "../config/redis.js";
import { Kafka } from "kafkajs";
import { convertKeysToSnakeCase } from "../utils/caseConverter.js";

// Configuration constants
const HISTORY_RETENTION_COUNT = 1000; // Store last 1000 messages per stream

// Kafka SSL configuration for secure connection
const KAFKA_SSL = {
  ca: [process.env.KAFKA_CERT],
  key: [process.env.KAFKA_USER_KEY],
  cert: [process.env.KAFKA_USER_CERT],
  rejectUnauthorized: true,
};

// Stream configuration mapping for different Kafka topics
const STREAM_CONFIG = {
  "jupiter-amm-swaps": {
    historyKey: "jupiter_amm_swaps_history",
    displayName: "Jupiter AMM Swaps"
  },
  "pumpfun-amm-swaps": {
    historyKey: "pumpfun_amm_swaps_history",
    displayName: "Pump.fun AMM Swaps"
  },
  "jupiter-dca-orders": {
    historyKey: "jupiter_dca_orders_history",
    displayName: "Jupiter DCA Orders"
  }
};

/**
 * Transform raw Kafka stream data to standardized format
 * Converts camelCase keys to snake_case and adds topic-specific metadata
 * @param {string} topic - Kafka topic name
 * @param {Object} data - Raw message data from Kafka
 * @returns {Object} Transformed data in standardized format
 */
function transformKafkaStreamData(topic, data) {
  // Convert all camelCase keys to snake_case recursively
  const snakeCaseData = convertKeysToSnakeCase(data);

  const baseTransform = {
    timestamp: Date.now(),
    source: topic,
    ...snakeCaseData
  };

  // Add topic-specific transformations if needed
  switch (topic) {
    case "jupiter-amm-swaps":
      return {
        ...baseTransform,
        type: "jupiter_amm_swap"
      };
    case "pumpfun-amm-swaps":
      return {
        ...baseTransform,
        type: "pumpfun_amm_swap"
      };
    case "jupiter-dca-orders":
      return {
        ...baseTransform,
        type: "jupiter_dca_order"
      };
    default:
      return baseTransform;
  }
}

/**
 * Kafka Streams Worker Class
 *
 * Manages Kafka consumer connections and processes real-time stream data
 * from Jupiter AMM swaps, Pump.fun swaps, and DCA orders.
 * Publishes processed data to Redis pub/sub and maintains historical cache.
 */
export class KafkaStreams {
  constructor() {
    this.isRunning = false; // Worker state flag
    this.kafka = new Kafka({
      clientId: process.env.KAFKA_CLIENT_ID,
      brokers: [process.env.KAFKA_BROKERS],
      ssl: KAFKA_SSL,
      connectionTimeout: 30000,
      authenticationTimeout: 30000,
      retry: {
        initialRetryTime: 100,
        retries: 100,
        maxRetryTime: 30000,
        factor: 2,
      },
    });
  }

  /**
   * Initialize Kafka streams worker
   * Validates environment variables and starts connection
   * @throws {Error} If required environment variables are missing
   */
  async init() {
    if (!process.env.KAFKA_CLIENT_ID || !process.env.KAFKA_BROKERS) {
      throw new Error("Missing required Kafka environment variables");
    }
    this.connect();
  }

  /**
   * Stop the Kafka streams worker and disconnect consumer
   */
  stop() {
    this.isRunning = false;
    if (this.consumer) {
      this.consumer.disconnect();
      this.consumer = null;
    }
  }

  async connect() {
    if (this.isRunning) return;
    this.isRunning = true;

    try {
      this.consumer = this.kafka.consumer({
        groupId: `stalkapi-consumer-${Date.now()}`,
      });

      console.log("[KAFKA] Connecting to Kafka...");
      await this.consumer.connect();
      console.log("[KAFKA] Connected to Kafka successfully");

      // Subscribe to the different streams
      console.log("[KAFKA] Subscribing to topics...");
      await Promise.all([
        this.consumer.subscribe({
          topic: "jupiter-amm-swaps",
          fromBeginning: false, // Only process new messages
        }),
        this.consumer.subscribe({
          topic: "pumpfun-amm-swaps",
          fromBeginning: false, // Only process new messages
        }),
        this.consumer.subscribe({
          topic: "stalkchain-dca-swaps",
          fromBeginning: false, // Only process new messages
        }),
      ]);
      console.log("[KAFKA] Successfully subscribed to all topics");
    } catch (error) {
      console.error("[KAFKA] Error during connection:", error);
      this.isRunning = false;
      // Retry connection after delay
      if (this.isRunning !== false) { // Only retry if not explicitly stopped
        setTimeout(() => this.connect(), 5000);
      }
      return;
    }

    await this.consumer.run({
      eachMessage: async ({ topic, message }) => {
        try {
          // Check if worker is still running before processing
          if (!this.isRunning) {
            return;
          }

          const rawData = JSON.parse(message?.value?.toString());

          const topicMapping = {
            "jupiter-amm-swaps": "jupiter-amm-swaps",
            "pumpfun-amm-swaps": "pumpfun-amm-swaps",
            "stalkchain-dca-swaps": "jupiter-dca-orders",
          };

          const streamName = topicMapping[topic];
          if (!streamName) {
            console.warn(`⚠️ [KAFKA] Unknown topic: ${topic}`);
            return;
          }

          // Transform the data
          const transformedData = transformKafkaStreamData(streamName, rawData);

          // Publish to pub/sub for real-time subscribers
          await pubsub.publish("kafka_streams_internal", {
            stream: streamName,
            data: transformedData,
            timestamp: Date.now(),
          });

          // Store in Redis history (keep last 1000 messages per stream)
          const config = STREAM_CONFIG[streamName];
          if (config) {
            await cache.lpushAndTrim(config.historyKey, transformedData, HISTORY_RETENTION_COUNT);
          }

        } catch (error) {
          if (this.isRunning) {
            console.error(`❌ [KAFKA] Error processing message from topic ${topic}:`, error);
          }
        }
      },
    });
  }
}
