import { pubsub } from "../config/redis.js";
import { subscribeCentral } from "../config/redisCentral.js";

export class StalkchainCentral {
  constructor() {
    this.isRunning = false;
  }

  async init() {
    this.isRunning = true;
    this.connect();
  }

  connect() {
    subscribeCentral("fresh_wallet_feed", (message) => {
      try {
        // Check if worker is still running before processing
        if (!this.isRunning) {
          return;
        }

        // Publish the data to the internal Redis channel for StreamManager
        pubsub.publish("stalkchain_central_internal", {
          stream: "fresh-wallet-feed",
          data: this.formatData(message),
          timestamp: Date.now(),
        });

        console.log(
          `✅ [StalkchainCentral] Published fresh wallet feed data:`,
          {
            type: message.type,
            wallet: message.data?.wallet,
            timestamp: new Date().toISOString(),
          }
        );
      } catch (error) {
        if (this.isRunning) {
          console.error(
            "❌ [StalkchainCentral] Error processing message:",
            error
          );
        }
      }
    });

    console.log(
      "✅ [StalkchainCentral] Connected to fresh_wallet_feed channel"
    );
  }

  stop() {
    this.isRunning = false;
    console.log("🛑 [StalkchainCentral] Worker stopped");
  }

  formatData(data) {
    // TODO: Add any necessary data formatting here
    return {
      platform: data.data?.source,
      type: data.data?.type,
      timestamp: new Date(data.data?.trxDate),
      wallet: data.data?.wallet,
      wallet_age: new Date(data.data?.walletAge),
      wallet_first_activity: new Date(data.data?.walletAgeDetailed?.firstActivity),
      wallet_transactions_count: data.data?.walletAgeDetailed?.txCount,
      wallet_funding_mint: data.data?.walletFundingMint,
      wallet_funding_amount: data.data?.walletFunding,
      wallet_funding_source: data.data?.walletAgeDetailed?.source?.type,
      wallet_funding_source_wallet: data.data?.walletAgeDetailed?.source?.wallet,
      wallet_funding_source_name: data.data?.walletAgeDetailed?.source?.name || "Unknown",
      sol_amount: data.data?.solAmount,
      usd_amount: data.data?.usdAmount,
      token_in: {
        mint: data.data?.inputMint,
        symbol: data.data?.inputMintMetaData?.ticker,
        name: data.data?.inputMintMetaData?.name,
        decimals: data.data?.inputDecimals,
        logo: data.data?.inputMintMetaData?.icon,
        amount: Number(data.data?.inputAmount) / Math.pow(10, data.data?.inputDecimals),
        amount_string: (Number(data.data?.inputAmount) / Math.pow(10, data.data?.inputDecimals)).toString(),
        amount_usd: data.data?.usdAmount,
        price: data.data?.inputTokenPrice
          ? Number(data.data?.inputTokenPrice)
          : data.data?.usdAmount / Number(data.data?.inputAmount),
      },
      token_out: {
        mint: data.data?.outputMint,
        symbol: data.data?.outputMintMetaData?.ticker,
        name: data.data?.outputMintMetaData?.name,
        decimals: data.data?.outputDecimals,
        logo: data.data?.outputMintMetaData?.icon,
        amount: Number(data.data?.outputAmount) / Math.pow(10, data.data?.outputDecimals),
        amount_string: (Number(data.data?.outputAmount) / Math.pow(10, data.data?.outputDecimals)).toString(),
        amount_usd: data.data?.usdAmount,
        price: data.data?.outputTokenPrice
          ? Number(data.data?.outputTokenPrice)
          : data.data?.usdAmount / Number(data.data?.outputAmount),
      },
      signature: data.data?.signature,
      // debug: data
    };
  }
}

/* 
{
  "type": "jup_swap",
  "data": {
    "type": "buy",
    "source": "jupiter",
    "wallet": "9ou274RTHU7DbvN4DNPBD1LzgscWCjbiJAYpYd1A6fJn",
    "trxDate": "2025-06-10T09:54:02.000Z",
    "walletAge": "2025-06-10T09:52:58.000Z",
    "walletFunding": 59,
    "walletFundingMint": "So11111111111111111111111111111111111111112",
    "solAmount": 58.978213018,
    "usdAmount": 9352.73750627377,
    "signature": "59sPEQph83nyxqVw4GkNoABatcUhYkKxNqcZ5Ui9Ac58nUZp5Pi4mqXAWQdXMUAbjEHcaZvCBwJ7tMZS7CrVJyZT",
    "inputMint": "So11111111111111111111111111111111111111112",
    "inputMintMetaData": {
      "address": "So11111111111111111111111111111111111111112",
      "ticker": "SOL",
      "name": "Wrapped SOL",
      "decimals": 9,
      "icon": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png"
    },
    "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
    "outputMintMetaData": {
      "address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
      "ticker": "USDC",
      "name": "USD Coin",
      "decimals": 6,
      "icon": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png"
    },
    "inputAmount": "58978213018",
    "outputAmount": "9359821953",
    "inputDecimals": 9,
    "outputDecimals": 6,
    "inputTokenPrice": 158.57953348670193,
    "walletAgeDetailed": {
      "isFresh": true,
      "reason": "Wallet is fresh",
      "firstActivity": "2025-06-10T09:52:58.000Z",
      "txCount": 11,
      "funding": {
        "amount": "59",
        "mint": "So11111111111111111111111111111111111111112"
      },
      "source": {
        "wallet": "6ZAaesUyRcdGKLRUJNgNU8dis89cVBZteVB3YzcsURMa",
        "type": "wallet",
        "name": null
      },
      "transactions": {
        "main": "2qPw6nMeyG6edpYBbiEpTHpQY7KwivSwgA1vYJaLnZoeHwTLhicoe1iQAVmBVj4sscGFsF4orJnHfsaUdZYXZvZG",
        "related": []
      },
      "status": "success",
      "timestamp": 1749549257878
    }
  }
}
*/
