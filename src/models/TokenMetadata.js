import { query } from '../config/database.js';
import { cache } from '../config/redis.js';

export class TokenMetadata {
    constructor(data) {
        this.id = data.id;
        this.token_address = data.token_address;
        this.name = data.name;
        this.symbol = data.symbol;
        this.decimals = data.decimals;
        this.logo = data.logo;
        this.created_at = data.created_at;
        this.updated_at = data.updated_at;
    }

    // Convert to API response format
    toApiFormat() {
        return {
            tokenAddress: this.token_address,
            name: this.name,
            symbol: this.symbol,
            decimals: this.decimals,
            logo: this.logo
        };
    }

    // Find multiple tokens by addresses with Redis caching
    static async findByAddresses(addresses) {
        try {
            if (!Array.isArray(addresses)) {
                addresses = [addresses];
            }

            const results = [];
            const missingFromCache = [];

            // Check Redis cache first
            for (const address of addresses) {
                const cacheKey = `token_metadata:${address.toLowerCase()}`;
                const cachedData = await cache.get(cacheKey);
                
                if (cachedData) {
                    results.push(new TokenMetadata(cachedData));
                } else {
                    missingFromCache.push(address);
                }
            }

            // If some tokens are missing from cache, check database
            if (missingFromCache.length > 0) {
                const placeholders = missingFromCache.map((_, index) => `$${index + 1}`).join(',');
                const dbResult = await query(
                    `SELECT * FROM token_metadata WHERE LOWER(token_address) = ANY(ARRAY[${placeholders}])`,
                    missingFromCache.map(addr => addr.toLowerCase())
                );

                // Add found tokens to results and cache them
                for (const row of dbResult.rows) {
                    const tokenMetadata = new TokenMetadata(row);
                    results.push(tokenMetadata);
                    
                    // Cache in Redis with 24 hour TTL
                    const cacheKey = `token_metadata:${row.token_address.toLowerCase()}`;
                    await cache.set(cacheKey, row, 86400); // 24 hours
                }

                // Remove found tokens from missing list
                const foundAddresses = dbResult.rows.map(row => row.token_address.toLowerCase());
                const stillMissing = missingFromCache.filter(addr => 
                    !foundAddresses.includes(addr.toLowerCase())
                );

                return {
                    found: results,
                    missing: stillMissing
                };
            }

            return {
                found: results,
                missing: []
            };

        } catch (error) {
            console.error('Error finding token metadata by addresses:', error);
            return {
                found: [],
                missing: addresses
            };
        }
    }

    // Create or update multiple token metadata entries
    static async upsertMany(tokenDataArray) {
        try {
            if (!Array.isArray(tokenDataArray) || tokenDataArray.length === 0) {
                return [];
            }

            const results = [];

            for (const tokenData of tokenDataArray) {
                const { tokenAddress, name, symbol, decimals, logo } = tokenData;
                
                const result = await query(
                    `INSERT INTO token_metadata (token_address, name, symbol, decimals, logo)
                     VALUES ($1, $2, $3, $4, $5)
                     ON CONFLICT (token_address) 
                     DO UPDATE SET 
                         name = EXCLUDED.name,
                         symbol = EXCLUDED.symbol,
                         decimals = EXCLUDED.decimals,
                         logo = EXCLUDED.logo,
                         updated_at = CURRENT_TIMESTAMP
                     RETURNING *`,
                    [tokenAddress.toLowerCase(), name, symbol, decimals, logo]
                );

                if (result.rows.length > 0) {
                    const tokenMetadata = new TokenMetadata(result.rows[0]);
                    results.push(tokenMetadata);

                    // Update Redis cache with 24 hour TTL
                    const cacheKey = `token_metadata:${tokenAddress.toLowerCase()}`;
                    await cache.set(cacheKey, result.rows[0], 86400); // 24 hours
                }
            }

            return results;

        } catch (error) {
            console.error('Error upserting token metadata:', error);
            throw error;
        }
    }

    // Clear cache for specific token addresses
    static async clearCache(addresses) {
        try {
            if (!Array.isArray(addresses)) {
                addresses = [addresses];
            }

            for (const address of addresses) {
                const cacheKey = `token_metadata:${address.toLowerCase()}`;
                await cache.del(cacheKey);
            }

            return true;
        } catch (error) {
            console.error('Error clearing token metadata cache:', error);
            return false;
        }
    }

    // Get cache statistics
    static async getCacheStats() {
        try {
            const pattern = 'token_metadata:*';
            // Use the redis instance from cache config
            const { redis } = await import('../config/redis.js');
            const keys = await redis.keys(pattern);

            return {
                total_cached_tokens: keys.length,
                cache_pattern: pattern
            };
        } catch (error) {
            console.error('Error getting cache stats:', error);
            return {
                total_cached_tokens: 0,
                cache_pattern: 'token_metadata:*'
            };
        }
    }
}

export default TokenMetadata;
