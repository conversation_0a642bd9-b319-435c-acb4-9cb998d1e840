import { query } from '../config/database.js';
import { cache } from '../config/redis.js';

export class Admin {
    constructor(data) {
        this.id = data.id;
        this.name = data.name;
        this.email = data.email;
        this.api_key = data.api_key;
        this.permissions = data.permissions || [];
        this.is_active = data.is_active;
        this.created_at = data.created_at;
        this.updated_at = data.updated_at;
        this.last_used = data.last_used;
    }

    /**
     * Find admin by API key
     * @param {string} apiKey - Admin API key
     * @returns {Admin|null} - Admin instance or null
     */
    static async findByApiKey(apiKey) {
        if (!apiKey) {
            return null;
        }

        try {
            // Check cache first
            const cacheKey = `admin:apikey:${apiKey}`;
            const cached = await cache.get(cacheKey);
            
            if (cached) {
                return new Admin(JSON.parse(cached));
            }

            // Query database
            const result = await query(
                `SELECT * FROM admin_users WHERE api_key = $1 AND is_active = true`,
                [apiKey]
            );

            if (result.rows.length === 0) {
                return null;
            }

            const admin = new Admin(result.rows[0]);

            // Cache for 10 minutes
            await cache.set(cacheKey, JSON.stringify(admin), 'EX', 600);

            // Update last_used timestamp
            await query(
                'UPDATE admin_users SET last_used = CURRENT_TIMESTAMP WHERE id = $1',
                [admin.id]
            );

            return admin;

        } catch (error) {
            console.error('Error finding admin by API key:', error);
            return null;
        }
    }

    /**
     * Find admin by ID
     * @param {string} id - Admin ID
     * @returns {Admin|null} - Admin instance or null
     */
    static async findById(id) {
        if (!id) {
            return null;
        }

        try {
            // Check cache first
            const cacheKey = `admin:id:${id}`;
            const cached = await cache.get(cacheKey);
            
            if (cached) {
                return new Admin(JSON.parse(cached));
            }

            // Query database
            const result = await query(
                'SELECT * FROM admin_users WHERE id = $1 AND is_active = true',
                [id]
            );

            if (result.rows.length === 0) {
                return null;
            }

            const admin = new Admin(result.rows[0]);

            // Cache for 10 minutes
            await cache.set(cacheKey, JSON.stringify(admin), 'EX', 600);

            return admin;

        } catch (error) {
            console.error('Error finding admin by ID:', error);
            return null;
        }
    }

    /**
     * Check if admin has specific permission
     * @param {string} permission - Permission to check (e.g., 'tiers:write')
     * @returns {boolean} - True if admin has permission
     */
    hasPermission(permission) {
        if (!this.is_active) {
            return false;
        }

        // System admin has all permissions
        if (this.permissions.includes('system:admin')) {
            return true;
        }

        // Check specific permission
        return this.permissions.includes(permission);
    }

    /**
     * Check if admin has any of the specified permissions
     * @param {string[]} permissions - Array of permissions to check
     * @returns {boolean} - True if admin has any of the permissions
     */
    hasAnyPermission(permissions) {
        if (!this.is_active) {
            return false;
        }

        // System admin has all permissions
        if (this.permissions.includes('system:admin')) {
            return true;
        }

        // Check if admin has any of the specified permissions
        return permissions.some(permission => this.permissions.includes(permission));
    }

    /**
     * Get all admins
     * @returns {Admin[]} - Array of admin instances
     */
    static async getAll() {
        try {
            const result = await query(
                'SELECT * FROM admin_users ORDER BY created_at DESC'
            );

            return result.rows.map(row => new Admin(row));

        } catch (error) {
            console.error('Error getting all admins:', error);
            return [];
        }
    }

    /**
     * Create new admin
     * @param {Object} adminData - Admin data
     * @returns {Admin|null} - Created admin instance or null
     */
    static async create(adminData) {
        const { name, email, api_key, permissions = [] } = adminData;

        try {
            const result = await query(
                `INSERT INTO admin_users (name, email, api_key, permissions)
                 VALUES ($1, $2, $3, $4)
                 RETURNING *`,
                [name, email, api_key, permissions]
            );

            const admin = new Admin(result.rows[0]);

            // Clear cache
            await this.clearCache(admin.api_key, admin.id);

            return admin;

        } catch (error) {
            console.error('Error creating admin:', error);
            return null;
        }
    }

    /**
     * Update admin
     * @param {string} id - Admin ID
     * @param {Object} updateData - Data to update
     * @returns {Admin|null} - Updated admin instance or null
     */
    static async update(id, updateData) {
        const allowedFields = ['name', 'email', 'permissions', 'is_active'];
        const updates = [];
        const values = [];
        let paramCount = 1;

        for (const [key, value] of Object.entries(updateData)) {
            if (allowedFields.includes(key)) {
                updates.push(`${key} = $${paramCount}`);
                values.push(value);
                paramCount++;
            }
        }

        if (updates.length === 0) {
            return null;
        }

        updates.push(`updated_at = CURRENT_TIMESTAMP`);
        values.push(id);

        try {
            const result = await query(
                `UPDATE admin_users SET ${updates.join(', ')} WHERE id = $${paramCount} RETURNING *`,
                values
            );

            if (result.rows.length === 0) {
                return null;
            }

            const admin = new Admin(result.rows[0]);

            // Clear cache
            await this.clearCache(admin.api_key, admin.id);

            return admin;

        } catch (error) {
            console.error('Error updating admin:', error);
            return null;
        }
    }

    /**
     * Delete admin (soft delete by setting is_active to false)
     * @param {string} id - Admin ID
     * @returns {boolean} - True if deleted successfully
     */
    static async delete(id) {
        try {
            const result = await query(
                'UPDATE admin_users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING api_key',
                [id]
            );

            if (result.rows.length > 0) {
                // Clear cache
                await this.clearCache(result.rows[0].api_key, id);
                return true;
            }

            return false;

        } catch (error) {
            console.error('Error deleting admin:', error);
            return false;
        }
    }

    /**
     * Clear admin cache
     * @param {string} apiKey - Admin API key
     * @param {string} id - Admin ID
     */
    static async clearCache(apiKey, id) {
        try {
            const keys = [];
            
            if (apiKey) {
                keys.push(`admin:apikey:${apiKey}`);
            }
            
            if (id) {
                keys.push(`admin:id:${id}`);
            }

            if (keys.length > 0) {
                await cache.del(...keys);
            }

        } catch (error) {
            console.error('Error clearing admin cache:', error);
        }
    }

    /**
     * Generate secure admin API key
     * @returns {string} - Generated API key
     */
    static generateApiKey() {
        const crypto = require('crypto');
        const prefix = 'admin_';
        const randomBytes = crypto.randomBytes(32).toString('hex');
        return `${prefix}${randomBytes}`;
    }
}
