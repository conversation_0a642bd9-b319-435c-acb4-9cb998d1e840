import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import { getScalarConfig, getScalarHTML } from '../docs/scalar-config.js';
import { loadOpenAPISpec } from '../docs/openapi-loader.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = express.Router();

// Serve Scalar static assets
router.use('/assets', express.static(path.join(__dirname, '../../node_modules/@scalar/api-reference/dist')));

// Serve Scalar API documentation
router.get('/', (req, res) => {
    try {
        // Get the base URL for the API
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        
        // Get Scalar configuration
        const config = getScalarConfig(baseUrl);
        
        // Generate HTML with Scalar
        const html = getScalarHTML(config);

        res.setHeader('Content-Type', 'text/html');
        res.send(html);

    } catch (error) {
        console.error('Error serving documentation:', error);
        res.status(500).json({
            error: 'Failed to load documentation',
            message: error.message
        });
    }
});

// Serve the OpenAPI spec as JSON
router.get('/openapi.json', (req, res) => {
    try {
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        const spec = loadOpenAPISpec(baseUrl);

        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.json(spec);

    } catch (error) {
        console.error('Error serving OpenAPI spec:', error);
        res.status(500).json({
            error: 'Failed to load OpenAPI specification',
            message: error.message
        });
    }
});

// API documentation info endpoint
router.get('/info', (req, res) => {
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    
    res.json({
        title: 'StalkAPI Documentation',
        description: 'Real-time KOL trading data and analytics API documentation',
        version: '1.0.0',
        documentation_url: `${baseUrl}/docs`,
        openapi_spec: {
            json: `${baseUrl}/docs/openapi.json`
        },
        powered_by: 'Scalar API Reference',
        last_updated: new Date().toISOString()
    });
});

export default router;
