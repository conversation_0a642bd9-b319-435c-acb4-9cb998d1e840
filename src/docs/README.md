# StalkAPI Documentation

This folder contains all files related to the self-hosted Scalar API documentation.

## Files

- **`openapi.json`** - The OpenAPI 3.0 specification in JSON format
- **`scalar-config.js`** - Scalar configuration and HTML template
- **`openapi-loader.js`** - Utility to load and modify the OpenAPI spec

## Structure

```
src/docs/
├── README.md           # This file
├── openapi.json        # OpenAPI specification (auto-generated from root openapi.yaml)
├── scalar-config.js    # Scalar configuration and HTML template
└── openapi-loader.js   # OpenAPI spec loader utility
```

## Usage

The documentation is served at `/docs` and uses <PERSON>ala<PERSON> for the interactive interface.

### Endpoints

- `GET /docs` - Interactive Scalar documentation interface
- `GET /docs/openapi.json` - OpenAPI specification in JSON format
- `GET /docs/info` - Documentation metadata

## Updating the OpenAPI Spec

To update the OpenAPI specification:

1. Edit the root `openapi.yaml` file
2. Run the conversion script to update the JSON version:

```bash
node -e "
const fs = require('fs');
const yaml = require('js-yaml');
const yamlContent = fs.readFileSync('openapi.yaml', 'utf8');
const jsonContent = yaml.load(yamlContent);
fs.writeFileSync('src/docs/openapi.json', JSON.stringify(jsonContent, null, 2));
console.log('✅ Updated src/docs/openapi.json');
"
```

## Configuration

The Scalar configuration can be customized in `scalar-config.js`:

- Theme colors and styling
- Layout options
- Metadata for SEO
- Custom CSS

## Self-Hosted Benefits

- No external dependencies for documentation hosting
- Full control over styling and branding
- No caching issues like with ReadMe.com
- Integrated with the main application
