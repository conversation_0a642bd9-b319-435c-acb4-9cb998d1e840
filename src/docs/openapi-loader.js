import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Load and return the OpenAPI specification as JSON
 * @param {string} baseUrl - The base URL to update servers in the spec
 * @returns {Object} The OpenAPI specification object
 */
export const loadOpenAPISpec = (baseUrl) => {
    try {
        const specPath = path.join(__dirname, 'openapi.json');
        const specContent = fs.readFileSync(specPath, 'utf8');
        const spec = JSON.parse(specContent);
        
        // Update servers in the spec with the current base URL
        spec.servers = [
            {
                url: baseUrl,
                description: process.env.NODE_ENV === 'production' ? 'Production server' : 'Development server'
            }
        ];
        
        return spec;
    } catch (error) {
        console.error('Error loading OpenAPI spec:', error);
        throw new Error('Failed to load OpenAPI specification');
    }
};

/**
 * Get the raw OpenAPI spec without modifications
 * @returns {Object} The raw OpenAPI specification object
 */
export const getRawOpenAPISpec = () => {
    try {
        const specPath = path.join(__dirname, 'openapi.json');
        const specContent = fs.readFileSync(specPath, 'utf8');
        return JSON.parse(specContent);
    } catch (error) {
        console.error('Error loading raw OpenAPI spec:', error);
        throw new Error('Failed to load OpenAPI specification');
    }
};
