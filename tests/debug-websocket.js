#!/usr/bin/env node

/**
 * Debug WebSocket Connection
 * Simple test to see what's happening with WebSocket connections
 */

import { WebSocket } from 'ws';

const API_KEY = 'test_free_api_key_12345';
const WS_URL = `ws://localhost:3001/ws?apiKey=${API_KEY}`;

console.log('🧪 Debug WebSocket Connection for Free tier');
console.log('Expected: 1 connection limit');

async function testConnection(connectionNumber) {
    return new Promise((resolve) => {
        console.log(`\n🔌 Attempting connection ${connectionNumber}...`);

        const ws = new WebSocket(WS_URL);
        let hasOpened = false;
        let hasResolved = false;

        const timeout = setTimeout(() => {
            if (!hasResolved) {
                console.log(`⏰ Connection ${connectionNumber}: Timeout`);
                ws.terminate();
                hasResolved = true;
                resolve({ success: false, reason: 'timeout' });
            }
        }, 5000); // Increased timeout to 5 seconds

        ws.on('open', () => {
            console.log(`🔗 Connection ${connectionNumber}: Opened (waiting for auth result...)`);
            hasOpened = true;
            // Don't resolve immediately - wait for either a message or close event
        });

        ws.on('message', (data) => {
            if (!hasResolved) {
                try {
                    const message = JSON.parse(data.toString());
                    if (message.type === 'connected') {
                        console.log(`✅ Connection ${connectionNumber}: Successfully authenticated`);
                        clearTimeout(timeout);
                        hasResolved = true;
                        resolve({ success: true, ws });
                    }
                } catch (error) {
                    console.log(`❓ Connection ${connectionNumber}: Received non-JSON message`);
                }
            }
        });

        ws.on('close', (code, reason) => {
            if (!hasResolved) {
                console.log(`❌ Connection ${connectionNumber}: Closed with code ${code}, reason: ${reason}`);
                clearTimeout(timeout);
                hasResolved = true;

                if (code === 1013) {
                    resolve({ success: false, code, reason: 'connection_limit_exceeded' });
                } else if (code === 1008) {
                    resolve({ success: false, code, reason: 'authentication_failed' });
                } else {
                    resolve({ success: false, code, reason: reason.toString() });
                }
            }
        });

        ws.on('error', (error) => {
            if (!hasResolved) {
                console.log(`❌ Connection ${connectionNumber}: Error - ${error.message}`);
                clearTimeout(timeout);
                hasResolved = true;
                resolve({ success: false, reason: error.message });
            }
        });
    });
}

async function runTest() {
    const connections = [];
    
    // Try to open 3 connections sequentially
    for (let i = 1; i <= 3; i++) {
        const result = await testConnection(i);
        
        if (result.success) {
            connections.push(result.ws);
            console.log(`   ✅ Connection ${i} successfully authenticated (total: ${connections.length})`);
        } else {
            console.log(`   ❌ Connection ${i} failed: ${result.reason || result.code}`);
            if (result.reason === 'connection_limit_exceeded') {
                console.log('   🎯 Connection rejected due to limit - this is correct!');
            } else if (result.reason === 'authentication_failed') {
                console.log('   🔒 Connection rejected due to authentication failure');
            }
        }
        
        // Small delay between connections
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log(`\n📊 Final Results:`);
    console.log(`   Successful connections: ${connections.length}`);
    console.log(`   Expected limit: 1`);
    
    if (connections.length === 1) {
        console.log('   ✅ Connection limit appears to be working correctly!');
    } else if (connections.length > 1) {
        console.log('   ❌ Too many connections allowed - limit not working');
    } else {
        console.log('   ❌ No connections succeeded - something is wrong');
    }
    
    // Clean up
    console.log('\n🧹 Cleaning up connections...');
    connections.forEach((ws, index) => {
        if (ws.readyState === WebSocket.OPEN) {
            console.log(`   Closing connection ${index + 1}`);
            ws.close();
        }
    });
    
    setTimeout(() => {
        process.exit(0);
    }, 1000);
}

runTest().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
});
