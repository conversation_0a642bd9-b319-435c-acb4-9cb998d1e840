{"type": "message", "data": {"token": {"name": "<PERSON>", "symbol": "Sings", "mint": "UFMkgRGeetSeffFjXNza1C2PUgeENGmQyc4Hebepump", "uri": "https://ipfs.io/ipfs/QmXZRM53tHVD3W9MQHKuN4Vnidg3yEAiKeoLW4ZJN5HKtm", "decimals": 6, "hasFileMetaData": true, "createdOn": "https://pump.fun"}, "pools": [{"liquidity": {"quote": 60, "usd": 9242.************}, "price": {"quote": 2.7958993476234855e-08, "usd": 4.306************e-06}, "tokenSupply": 1000000000, "lpBurn": 100, "tokenAddress": "UFMkgRGeetSeffFjXNza1C2PUgeENGmQyc4Hebepump", "marketCap": {"quote": 27.958993476234856, "usd": 4306.************}, "decimals": 6, "security": {"freezeAuthority": null, "mintAuthority": null}, "quoteToken": "So11111111111111111111111111111111111111112", "market": "pumpfun", "curvePercentage": 0, "curve": "ALzDjamUAJqFw6pVLfLCg8ss6niUYpAyn34bZ2KcdtUk", "deployer": "FByakcVGLVGhiujgBdZpcmWzenTrzwVe1vXmsWojmst9", "openTime": 0, "createdAt": 1749085579734, "poolId": "UFMkgRGeetSeffFjXNza1C2PUgeENGmQyc4Hebepump"}], "events": {"30m": {"priceChangePercentage": 0}, "1h": {"priceChangePercentage": 0}, "4h": {"priceChangePercentage": 0}, "24h": {"priceChangePercentage": 0}}, "risk": {"rugged": false, "risks": [{"name": "Bonding curve not complete", "description": "No PumpSwap liquidity pool, bonding curve not complete", "level": "warning", "score": 4000}], "score": 3}}, "room": "latest"}