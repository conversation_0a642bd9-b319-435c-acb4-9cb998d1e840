import { WebSocket } from 'ws';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🔍 Debugging SolanaTracker WebSocket Connection\n');

const WSS_URL = process.env.SOLANA_TRACKER_WSS_URL;
const API_KEY = process.env.SOLANA_TRACKER_WSS_KEY;

console.log('Environment variables:');
console.log(`  WSS_URL: ${WSS_URL}`);
console.log(`  API_KEY: ${API_KEY ? API_KEY.substring(0, 8) + '...' : 'NOT SET'}`);
console.log();

// Test different URL formats
const urlFormats = [
    `${WSS_URL}/${API_KEY}`, // Based on your example - this should be the correct format
    `${WSS_URL}?apiKey=${API_KEY}`,
    `${WSS_URL}?api-key=${API_KEY}`,
    `${WSS_URL}?x-api-key=${API_KEY}`,
    `${WSS_URL}?key=${API_KEY}`,
    `${WSS_URL}?token=${API_KEY}`,
    WSS_URL // No query params, maybe headers?
];

async function testConnection(url, index, useHeaders = false) {
    return new Promise((resolve) => {
        console.log(`\n🧪 Test ${index + 1}: ${useHeaders ? 'Headers auth' : 'Query param auth'}`);
        console.log(`   URL: ${url}`);
        
        const options = {};
        if (useHeaders) {
            options.headers = {
                'x-api-key': API_KEY,
                'Authorization': `Bearer ${API_KEY}`,
                'api-key': API_KEY
            };
            console.log(`   Headers: x-api-key, Authorization, api-key`);
        }
        
        const ws = new WebSocket(url, options);
        
        const timeout = setTimeout(() => {
            console.log('   ⏰ Timeout - closing connection');
            ws.close();
            resolve({ success: false, error: 'Timeout' });
        }, 10000);
        
        ws.on('open', () => {
            clearTimeout(timeout);
            console.log('   ✅ Connection opened successfully!');
            
            // Try to join a room
            const joinMessage = { type: 'join', room: 'latest' };
            console.log('   📤 Sending join message:', JSON.stringify(joinMessage));
            ws.send(JSON.stringify(joinMessage));
            
            // Wait a bit for response
            setTimeout(() => {
                ws.close();
                resolve({ success: true, url, headers: useHeaders });
            }, 3000);
        });
        
        ws.on('message', (data) => {
            console.log('   📨 Received message:', data.toString());
        });
        
        ws.on('error', (error) => {
            clearTimeout(timeout);
            console.log('   ❌ Connection error:', error.message);
            resolve({ success: false, error: error.message, url, headers: useHeaders });
        });
        
        ws.on('close', (code, reason) => {
            clearTimeout(timeout);
            console.log(`   🔌 Connection closed: ${code} - ${reason}`);
            if (code !== 1000) {
                resolve({ success: false, error: `Closed with code ${code}`, url, headers: useHeaders });
            }
        });
    });
}

async function runConnectionTests() {
    console.log('🚀 Starting connection tests...\n');
    
    const results = [];
    
    // Test query parameter formats
    for (let i = 0; i < urlFormats.length - 1; i++) {
        const result = await testConnection(urlFormats[i], i, false);
        results.push(result);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait between tests
    }
    
    // Test with headers
    const result = await testConnection(urlFormats[urlFormats.length - 1], urlFormats.length - 1, true);
    results.push(result);
    
    // Summary
    console.log('\n📋 Test Results Summary:');
    console.log('=' .repeat(60));
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    if (successful.length > 0) {
        console.log('\n✅ Successful connections:');
        successful.forEach((result, index) => {
            console.log(`  ${index + 1}. ${result.url} ${result.headers ? '(with headers)' : ''}`);
        });
    }
    
    if (failed.length > 0) {
        console.log('\n❌ Failed connections:');
        failed.forEach((result, index) => {
            console.log(`  ${index + 1}. ${result.url} ${result.headers ? '(with headers)' : ''}`);
            console.log(`      Error: ${result.error}`);
        });
    }
    
    if (successful.length === 0) {
        console.log('\n🤔 No successful connections. Possible issues:');
        console.log('  1. API key might be invalid or expired');
        console.log('  2. WebSocket URL might be incorrect');
        console.log('  3. Account might not have WebSocket access');
        console.log('  4. Different authentication method required');
        console.log('\nTry checking:');
        console.log('  - SolanaTracker account dashboard');
        console.log('  - API key permissions');
        console.log('  - Subscription plan (WebSocket requires Premium+)');
    }
    
    return successful.length > 0 ? successful[0] : null;
}

// Test with curl equivalent
async function testWithCurl() {
    console.log('\n🌐 Testing with curl equivalent...');
    
    try {
        // Test the REST API first to validate credentials
        const response = await fetch('https://data.solanatracker.io/tokens/latest', {
            headers: {
                'x-api-key': API_KEY
            }
        });
        
        console.log(`REST API test: ${response.status} ${response.statusText}`);
        
        if (response.ok) {
            console.log('✅ REST API credentials are valid');
            const data = await response.json();
            console.log(`Sample data: ${JSON.stringify(data).substring(0, 200)}...`);
        } else {
            console.log('❌ REST API credentials failed');
            const errorText = await response.text();
            console.log(`Error: ${errorText}`);
        }
    } catch (error) {
        console.log('❌ REST API test failed:', error.message);
    }
}

// Main execution
async function main() {
    if (!WSS_URL || !API_KEY) {
        console.error('❌ Missing environment variables. Please set:');
        console.error('  - SOLANA_TRACKER_WSS_URL');
        console.error('  - SOLANA_TRACKER_WSS_KEY');
        process.exit(1);
    }
    
    await testWithCurl();
    const workingConnection = await runConnectionTests();
    
    if (workingConnection) {
        console.log('\n🎉 Found working connection configuration!');
        console.log('Update your worker to use:');
        console.log(`  URL: ${workingConnection.url}`);
        console.log(`  Headers: ${workingConnection.headers ? 'Yes' : 'No'}`);
    } else {
        console.log('\n😞 No working connection found. Please check your credentials and subscription.');
    }
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n👋 Connection test interrupted');
    process.exit(0);
});

main().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
});
