{"type": "message", "data": {"token": {"name": "Pastanek", "symbol": "Pastanek", "mint": "BQYNDCKFVy5pjSRutjJpTr6JUSMZ4zE7Q3VVAQA5pump", "uri": "https://ipfs.io/ipfs/bafkreibbpi3sf4vfngipnb5jskle6b3yt24lffjvp3jqrdif3invrf3dti", "decimals": 6, "hasFileMetaData": true, "createdOn": "https://pump.fun"}, "pools": [{"liquidity": {"quote": 60, "usd": 9242.************}, "price": {"quote": 2.7958993476234855e-08, "usd": 4.306************e-06}, "tokenSupply": 1000000000, "lpBurn": 100, "tokenAddress": "BQYNDCKFVy5pjSRutjJpTr6JUSMZ4zE7Q3VVAQA5pump", "marketCap": {"quote": 27.958993476234856, "usd": 4306.************}, "decimals": 6, "security": {"freezeAuthority": null, "mintAuthority": null}, "quoteToken": "So11111111111111111111111111111111111111112", "market": "pumpfun", "curvePercentage": 0, "curve": "F2UDmzR2NkqPScgijuygZpb4saURnsUsfxRAYrEESiae", "deployer": "FjeaKDFAgexEPZbAFHwE4cL1MFvn1o6EL1qehcmVStCX", "openTime": 0, "createdAt": 1749085594339, "poolId": "BQYNDCKFVy5pjSRutjJpTr6JUSMZ4zE7Q3VVAQA5pump"}], "events": {"30m": {"priceChangePercentage": 0}, "1h": {"priceChangePercentage": 0}, "4h": {"priceChangePercentage": 0}, "24h": {"priceChangePercentage": 0}}, "risk": {"rugged": false, "risks": [{"name": "Bonding curve not complete", "description": "No PumpSwap liquidity pool, bonding curve not complete", "level": "warning", "score": 4000}], "score": 3}}, "room": "latest"}