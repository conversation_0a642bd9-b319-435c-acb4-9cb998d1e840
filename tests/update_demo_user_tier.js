import { query } from '../src/config/database.js';
import dotenv from 'dotenv';

dotenv.config();

const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG';

async function updateDemoUserTier() {
    try {
        console.log('🔧 Updating demo user to Enterprise tier...\n');
        
        // Update user to Enterprise tier (tier 4)
        const updateResult = await query(`
            UPDATE users 
            SET tier_id = 4, credits_remaining = -1
            WHERE api_key = $1
            RETURNING email, tier_id
        `, [API_KEY]);
        
        if (updateResult.rows.length === 0) {
            console.log('❌ No user found with that API key');
            return;
        }
        
        console.log('✅ Demo user updated to Enterprise tier');
        
        // Verify the update
        const verifyResult = await query(`
            SELECT u.email, u.tier_id, at.name as tier_name, at.allowed_streams, u.credits_remaining
            FROM users u
            JOIN access_tiers at ON u.tier_id = at.id
            WHERE u.api_key = $1
        `, [API_KEY]);
        
        const user = verifyResult.rows[0];
        console.log('\n👤 Updated Demo User Information:');
        console.log(`   Email: ${user.email}`);
        console.log(`   Tier ID: ${user.tier_id}`);
        console.log(`   Tier Name: ${user.tier_name}`);
        console.log(`   Allowed Streams: ${user.allowed_streams}`);
        console.log(`   Credits Remaining: ${user.credits_remaining}`);
        
        console.log('\n🎉 Demo user now has access to all SolanaTracker streams!');
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        process.exit(0);
    }
}

updateDemoUserTier();
