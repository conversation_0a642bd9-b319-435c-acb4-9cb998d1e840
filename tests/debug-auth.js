#!/usr/bin/env node

/**
 * Debug Authentication Test
 * Tests what user information is being passed to the rate limiter
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3001';
const API_KEY = 'test_free_api_key_12345';

async function debugAuth() {
    console.log('🔍 Debug Authentication Test');
    console.log('============================');
    
    try {
        console.log('Testing with Free tier API key:', API_KEY);
        
        const response = await axios.get(`${BASE_URL}/api/v1/status`, {
            headers: { 'X-API-Key': API_KEY },
            timeout: 5000
        });
        
        console.log('\n📊 Response Status:', response.status);
        console.log('📊 Response Data:', response.data);
        
        console.log('\n🔍 Rate Limit Headers:');
        Object.keys(response.headers).forEach(key => {
            if (key.toLowerCase().includes('ratelimit') || key.toLowerCase().includes('rate-limit')) {
                console.log(`   ${key}: ${response.headers[key]}`);
            }
        });
        
        // Test authentication directly
        console.log('\n🔐 Testing authentication endpoint...');
        try {
            const authResponse = await axios.get(`${BASE_URL}/api/v1/user/profile`, {
                headers: { 'X-API-Key': API_KEY },
                timeout: 5000
            });
            console.log('✅ Auth test successful:', authResponse.data);
        } catch (authError) {
            if (authError.response) {
                console.log('❌ Auth test failed:', authError.response.status, authError.response.data);
            } else {
                console.log('❌ Auth test error:', authError.message);
            }
        }
        
    } catch (error) {
        if (error.response) {
            console.log('❌ Request failed:', error.response.status, error.response.data);
            console.log('Rate limit headers:', error.response.headers);
        } else {
            console.log('❌ Request error:', error.message);
        }
    }
}

debugAuth();
