import { WebSocket } from 'ws';
import dotenv from 'dotenv';

dotenv.config();

console.log('🧪 Testing Memory Leak Fix - Client Disconnect Cleanup\n');

const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG';
const WS_URL = `ws://localhost:3001/ws?apiKey=${API_KEY}`;

async function testMemoryLeakFix() {
    console.log('🔍 Testing automatic cleanup on client disconnect...\n');
    
    const results = [];
    
    // Test 1: Subscribe and disconnect without unsubscribing
    console.log('📋 Test 1: Subscribe to stream and disconnect without unsubscribing');
    const test1Result = await testSubscribeAndDisconnect('tokens-graduating');
    results.push({ test: 'Subscribe and Disconnect', ...test1Result });
    
    // Wait a moment between tests
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test 2: Subscribe with parameters and disconnect
    console.log('\n📋 Test 2: Subscribe with parameters and disconnect without unsubscribing');
    const test2Result = await testParameterizedSubscribeAndDisconnect();
    results.push({ test: 'Parameterized Subscribe and Disconnect', ...test2Result });
    
    // Wait a moment between tests
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test 3: Multiple subscriptions and disconnect
    console.log('\n📋 Test 3: Multiple subscriptions and disconnect without unsubscribing');
    const test3Result = await testMultipleSubscriptionsAndDisconnect();
    results.push({ test: 'Multiple Subscriptions and Disconnect', ...test3Result });
    
    return results;
}

async function testSubscribeAndDisconnect(streamName) {
    return new Promise((resolve) => {
        console.log(`🔗 Connecting and subscribing to ${streamName}...`);
        
        const ws = new WebSocket(WS_URL);
        let subscriptionConfirmed = false;
        let connectionId = null;
        
        const timeout = setTimeout(() => {
            console.log('⏰ Test timeout - forcefully closing connection');
            ws.terminate();
            resolve({
                subscriptionConfirmed,
                connectionId,
                status: 'timeout'
            });
        }, 10000); // 10 seconds
        
        ws.on('open', () => {
            console.log('✅ WebSocket connected');
            
            setTimeout(() => {
                const subscribeMessage = {
                    type: 'subscribe',
                    payload: {
                        stream: streamName
                    }
                };
                
                console.log(`📡 Subscribing to ${streamName}...`);
                ws.send(JSON.stringify(subscribeMessage));
            }, 1000);
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                
                if (message.type === 'connected') {
                    connectionId = message.connectionId;
                    console.log(`🔗 Connection established (ID: ${connectionId})`);
                } else if (message.type === 'subscribed') {
                    subscriptionConfirmed = true;
                    console.log(`✅ Subscription confirmed for ${streamName}`);
                    
                    // Wait a moment then disconnect WITHOUT unsubscribing
                    setTimeout(() => {
                        console.log('🔌 Disconnecting WITHOUT unsubscribing (simulating memory leak scenario)...');
                        ws.close();
                    }, 2000);
                } else if (message.type === 'error') {
                    console.log(`❌ ERROR: ${message.error}`);
                    clearTimeout(timeout);
                    resolve({
                        subscriptionConfirmed,
                        connectionId,
                        status: 'error',
                        error: message.error
                    });
                }
            } catch (error) {
                console.error('❌ Error parsing message:', error);
            }
        });
        
        ws.on('close', (code, reason) => {
            console.log(`🔌 WebSocket closed (code: ${code}, reason: ${reason || 'none'})`);
            clearTimeout(timeout);
            resolve({
                subscriptionConfirmed,
                connectionId,
                status: 'disconnected'
            });
        });
        
        ws.on('error', (error) => {
            console.error('❌ WebSocket error:', error.message);
            clearTimeout(timeout);
            resolve({
                subscriptionConfirmed,
                connectionId,
                status: 'connection_error',
                error: error.message
            });
        });
    });
}

async function testParameterizedSubscribeAndDisconnect() {
    return new Promise((resolve) => {
        console.log('🔗 Connecting and subscribing to parameterized graduating stream...');
        
        const ws = new WebSocket(WS_URL);
        let subscriptionConfirmed = false;
        let connectionId = null;
        
        const timeout = setTimeout(() => {
            console.log('⏰ Test timeout - forcefully closing connection');
            ws.terminate();
            resolve({
                subscriptionConfirmed,
                connectionId,
                status: 'timeout'
            });
        }, 10000);
        
        ws.on('open', () => {
            console.log('✅ WebSocket connected');
            
            setTimeout(() => {
                const subscribeMessage = {
                    type: 'subscribe',
                    payload: {
                        stream: 'tokens-graduating',
                        parameters: {
                            marketCapThreshold: 200
                        }
                    }
                };
                
                console.log('📡 Subscribing to tokens-graduating with marketCapThreshold: 200...');
                ws.send(JSON.stringify(subscribeMessage));
            }, 1000);
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                
                if (message.type === 'connected') {
                    connectionId = message.connectionId;
                    console.log(`🔗 Connection established (ID: ${connectionId})`);
                } else if (message.type === 'subscribed') {
                    subscriptionConfirmed = true;
                    console.log('✅ Parameterized subscription confirmed');
                    
                    // Wait a moment then disconnect WITHOUT unsubscribing
                    setTimeout(() => {
                        console.log('🔌 Disconnecting WITHOUT unsubscribing (parameterized stream)...');
                        ws.close();
                    }, 2000);
                } else if (message.type === 'error') {
                    console.log(`❌ ERROR: ${message.error}`);
                    clearTimeout(timeout);
                    resolve({
                        subscriptionConfirmed,
                        connectionId,
                        status: 'error',
                        error: message.error
                    });
                }
            } catch (error) {
                console.error('❌ Error parsing message:', error);
            }
        });
        
        ws.on('close', (code, reason) => {
            console.log(`🔌 WebSocket closed (code: ${code}, reason: ${reason || 'none'})`);
            clearTimeout(timeout);
            resolve({
                subscriptionConfirmed,
                connectionId,
                status: 'disconnected'
            });
        });
        
        ws.on('error', (error) => {
            console.error('❌ WebSocket error:', error.message);
            clearTimeout(timeout);
            resolve({
                subscriptionConfirmed,
                connectionId,
                status: 'connection_error',
                error: error.message
            });
        });
    });
}

async function testMultipleSubscriptionsAndDisconnect() {
    return new Promise((resolve) => {
        console.log('🔗 Connecting and subscribing to multiple streams...');
        
        const ws = new WebSocket(WS_URL);
        let subscriptionsConfirmed = 0;
        let connectionId = null;
        const targetSubscriptions = 3;
        const streams = ['tokens-launched', 'tokens-graduating', 'tokens-graduated'];
        
        const timeout = setTimeout(() => {
            console.log('⏰ Test timeout - forcefully closing connection');
            ws.terminate();
            resolve({
                subscriptionsConfirmed,
                targetSubscriptions,
                connectionId,
                status: 'timeout'
            });
        }, 15000);
        
        ws.on('open', () => {
            console.log('✅ WebSocket connected');
            
            // Subscribe to multiple streams
            streams.forEach((stream, index) => {
                setTimeout(() => {
                    const subscribeMessage = {
                        type: 'subscribe',
                        payload: { stream }
                    };
                    
                    console.log(`📡 Subscribing to ${stream}...`);
                    ws.send(JSON.stringify(subscribeMessage));
                }, (index + 1) * 1000);
            });
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                
                if (message.type === 'connected') {
                    connectionId = message.connectionId;
                    console.log(`🔗 Connection established (ID: ${connectionId})`);
                } else if (message.type === 'subscribed') {
                    subscriptionsConfirmed++;
                    console.log(`✅ Subscription confirmed for ${message.stream} (${subscriptionsConfirmed}/${targetSubscriptions})`);
                    
                    // If all subscriptions confirmed, disconnect without unsubscribing
                    if (subscriptionsConfirmed === targetSubscriptions) {
                        setTimeout(() => {
                            console.log('🔌 Disconnecting WITHOUT unsubscribing (multiple streams)...');
                            ws.close();
                        }, 2000);
                    }
                } else if (message.type === 'error') {
                    console.log(`❌ ERROR: ${message.error}`);
                    clearTimeout(timeout);
                    resolve({
                        subscriptionsConfirmed,
                        targetSubscriptions,
                        connectionId,
                        status: 'error',
                        error: message.error
                    });
                }
            } catch (error) {
                console.error('❌ Error parsing message:', error);
            }
        });
        
        ws.on('close', (code, reason) => {
            console.log(`🔌 WebSocket closed (code: ${code}, reason: ${reason || 'none'})`);
            clearTimeout(timeout);
            resolve({
                subscriptionsConfirmed,
                targetSubscriptions,
                connectionId,
                status: 'disconnected'
            });
        });
        
        ws.on('error', (error) => {
            console.error('❌ WebSocket error:', error.message);
            clearTimeout(timeout);
            resolve({
                subscriptionsConfirmed,
                targetSubscriptions,
                connectionId,
                status: 'connection_error',
                error: error.message
            });
        });
    });
}

async function main() {
    console.log('🚀 Starting memory leak fix test...\n');
    
    const results = await testMemoryLeakFix();
    
    console.log('\n' + '='.repeat(60));
    console.log('📋 MEMORY LEAK FIX TEST RESULTS');
    console.log('='.repeat(60));
    
    results.forEach((result, index) => {
        console.log(`\n${index + 1}. ${result.test}:`);
        console.log(`   Status: ${result.status}`);
        console.log(`   Subscription confirmed: ${result.subscriptionConfirmed ? '✅ YES' : '❌ NO'}`);
        console.log(`   Connection ID: ${result.connectionId || 'N/A'}`);
        
        if (result.subscriptionsConfirmed !== undefined) {
            console.log(`   Subscriptions: ${result.subscriptionsConfirmed}/${result.targetSubscriptions}`);
        }
        
        if (result.error) {
            console.log(`   Error: ${result.error}`);
        }
    });
    
    console.log('\n💡 Analysis:');
    const allSuccessful = results.every(r => r.status === 'disconnected' && r.subscriptionConfirmed);
    
    if (allSuccessful) {
        console.log('🎉 All tests completed successfully!');
        console.log('✅ Clients were able to subscribe and disconnect');
        console.log('✅ Server should have automatically published unsubscription events');
        console.log('✅ SolanaTracker connections should be cleaned up automatically');
        console.log('\n🔍 Check server logs for unsubscription cleanup messages like:');
        console.log('   "🧹 Published unsubscription event for stream \'...\' due to client disconnect"');
        console.log('   "✅ [StreamManager] Auto-unsubscribed from SolanaTracker room \'...\'"');
    } else {
        console.log('❌ Some tests failed - check individual results above');
    }
    
    console.log('\n🔗 Memory Leak Prevention Features:');
    console.log('• Automatic unsubscription on client disconnect');
    console.log('• Redis pub/sub cleanup events');
    console.log('• Periodic maintenance scheduler (5 minutes)');
    console.log('• Orphaned connection detection and cleanup');
    
    console.log('='.repeat(60));
}

main().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
});
