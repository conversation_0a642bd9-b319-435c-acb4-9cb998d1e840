#!/usr/bin/env node

// Setup verification script
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';

const execAsync = promisify(exec);

console.log('🔍 Checking system requirements for API Engine...\n');

async function checkCommand(command, name) {
    try {
        await execAsync(command);
        console.log(`✅ ${name} is installed`);
        return true;
    } catch (error) {
        console.log(`❌ ${name} is not installed or not in PATH`);
        return false;
    }
}

async function checkPort(port, skipMessage = false) {
    try {
        await execAsync(`lsof -i :${port}`);
        if (!skipMessage) {
            console.log(`⚠️  Port ${port} is already in use`);
        }
        return false;
    } catch (error) {
        if (!skipMessage) {
            console.log(`✅ Port ${port} is available`);
        }
        return true;
    }
}

async function checkPostgreSQL() {
    try {
        const { stdout } = await execAsync('pg_isready');
        if (stdout.includes('accepting connections')) {
            console.log('✅ PostgreSQL is running');
            return true;
        } else {
            console.log('❌ PostgreSQL is not accepting connections');
            return false;
        }
    } catch (error) {
        console.log('❌ PostgreSQL is not running or pg_isready not found');
        return false;
    }
}

async function checkRedis() {
    try {
        const { stdout } = await execAsync('redis-cli ping');
        if (stdout.trim() === 'PONG') {
            console.log('✅ Redis is running');
            return true;
        } else {
            console.log('❌ Redis is not responding');
            return false;
        }
    } catch (error) {
        console.log('❌ Redis is not running or redis-cli not found');
        return false;
    }
}

async function checkEnvironment() {
    if (fs.existsSync('.env')) {
        console.log('✅ .env file exists');
        return true;
    } else {
        console.log('❌ .env file not found');
        return false;
    }
}

async function main() {
    let allGood = true;
    
    // Check Node.js version
    console.log('📋 System Requirements:');
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion >= 18) {
        console.log(`✅ Node.js ${nodeVersion} (>= 18 required)`);
    } else {
        console.log(`❌ Node.js ${nodeVersion} (>= 18 required)`);
        allGood = false;
    }
    
    // Check package manager
    const hasPnpm = await checkCommand('pnpm --version', 'pnpm');
    const hasNpm = await checkCommand('npm --version', 'npm');
    
    if (!hasPnpm && !hasNpm) {
        console.log('❌ No package manager found');
        allGood = false;
    }
    
    console.log('\n🗄️  Database Requirements:');
    
    // Check PostgreSQL
    const hasPostgres = await checkCommand('psql --version', 'PostgreSQL client');
    const postgresRunning = await checkPostgreSQL();
    
    if (!hasPostgres || !postgresRunning) {
        allGood = false;
    }
    
    // Check Redis
    const hasRedis = await checkCommand('redis-cli --version', 'Redis client');
    const redisRunning = await checkRedis();
    
    if (!hasRedis || !redisRunning) {
        allGood = false;
    }
    
    console.log('\n🔧 Configuration:');
    
    // Check environment file
    const hasEnv = await checkEnvironment();
    if (!hasEnv) {
        allGood = false;
    }
    
    // Check ports
    console.log('\n🌐 Port Availability:');
    const port3000Available = await checkPort(3000);

    // For Redis port, we only care if Redis is NOT running but port is in use
    const port6379Available = await checkPort(6379, true); // Skip default message
    if (!port6379Available && redisRunning) {
        console.log('✅ Port 6379 is in use by Redis (expected)');
    } else if (!port6379Available && !redisRunning) {
        console.log('⚠️  Port 6379 is in use but Redis is not running');
        allGood = false;
    } else if (port6379Available && !redisRunning) {
        console.log('✅ Port 6379 is available for Redis');
    } else if (port6379Available && redisRunning) {
        console.log('⚠️  Redis is running but not on port 6379');
    }

    if (!port3000Available) {
        allGood = false;
    }
    
    console.log('\n' + '='.repeat(60));
    
    if (allGood) {
        console.log('🎉 All requirements met! You can proceed with setup.');
        console.log('\nNext steps:');
        console.log('1. npm run db:migrate    # Set up database');
        console.log('2. npm run dev           # Start development server');
        console.log('3. npm run test:api      # Test the API');
    } else {
        console.log('❌ Some requirements are missing. Please fix the issues above.');
        console.log('\n📚 Installation guides:');
        
        if (!hasPostgres || !postgresRunning) {
            console.log('\n🐘 PostgreSQL Setup:');
            console.log('macOS: brew install postgresql && brew services start postgresql');
            console.log('Ubuntu: sudo apt install postgresql postgresql-contrib');
            console.log('Windows: Download from https://www.postgresql.org/download/');
            console.log('\nCreate database: createdb -U postgres api_engine');
        }
        
        if (!hasRedis || !redisRunning) {
            console.log('\n🔴 Redis Setup:');
            console.log('macOS: brew install redis && brew services start redis');
            console.log('Ubuntu: sudo apt install redis-server');
            console.log('Windows: Download from https://redis.io/download');
            console.log('\nStart Redis: redis-server');
        }
        
        if (!hasEnv) {
            console.log('\n⚙️  Environment Setup:');
            console.log('Copy .env.example to .env and update database credentials');
        }
        
        console.log('\n📖 For detailed setup instructions, see SETUP_GUIDE.md');
    }
    
    console.log('\n🔍 System Information:');
    console.log(`Node.js: ${process.version}`);
    console.log(`Platform: ${process.platform}`);
    console.log(`Architecture: ${process.arch}`);
    console.log(`Working Directory: ${process.cwd()}`);
}

main().catch(console.error);
