import { query } from '../src/config/database.js';
import dotenv from 'dotenv';

dotenv.config();

const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG';

async function checkDemoUserTier() {
    try {
        console.log('🔍 Checking demo user tier and access...\n');
        
        const result = await query(`
            SELECT u.email, u.tier_id, at.name as tier_name, at.allowed_streams
            FROM users u
            JOIN access_tiers at ON u.tier_id = at.id
            WHERE u.api_key = $1
        `, [API_KEY]);
        
        if (result.rows.length === 0) {
            console.log('❌ No user found with that API key');
            return;
        }
        
        const user = result.rows[0];
        console.log('👤 Demo User Information:');
        console.log(`   Email: ${user.email}`);
        console.log(`   Tier ID: ${user.tier_id}`);
        console.log(`   Tier Name: ${user.tier_name}`);
        console.log(`   Allowed Streams: ${user.allowed_streams}`);
        
        // Check if user has access to tokens-launched
        const streamAccess = await query(`
            SELECT stream_name, required_tier_id, credits_per_message
            FROM stream_definitions
            WHERE stream_name = 'tokens-launched'
        `);
        
        if (streamAccess.rows.length > 0) {
            const stream = streamAccess.rows[0];
            console.log('\n📡 tokens-launched Stream Requirements:');
            console.log(`   Required Tier ID: ${stream.required_tier_id}`);
            console.log(`   Credits per Message: ${stream.credits_per_message}`);
            
            const hasAccess = user.tier_id >= stream.required_tier_id || user.allowed_streams.includes('*') || user.allowed_streams.includes('tokens-launched');
            console.log(`   User Has Access: ${hasAccess ? '✅ YES' : '❌ NO'}`);
            
            if (!hasAccess) {
                console.log('\n🔧 Solution: Update demo user to Enterprise tier or add tokens-launched to their allowed streams');
            }
        }
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        process.exit(0);
    }
}

checkDemoUserTier();
