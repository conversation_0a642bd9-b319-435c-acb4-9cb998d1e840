import { query } from '../src/config/database.js';
import dotenv from 'dotenv';

dotenv.config();

const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG';

async function debugUserAccess() {
    try {
        console.log('🔍 Debugging user access for tokens-launched stream...\n');
        
        // Get user details with raw allowed_streams value
        const userResult = await query(`
            SELECT u.id, u.email, u.tier_id, u.allowed_streams, at.name as tier_name, at.allowed_streams as tier_allowed_streams
            FROM users u
            JOIN access_tiers at ON u.tier_id = at.id
            WHERE u.api_key = $1
        `, [API_KEY]);
        
        if (userResult.rows.length === 0) {
            console.log('❌ No user found with that API key');
            return;
        }
        
        const user = userResult.rows[0];
        console.log('👤 User Details:');
        console.log(`   ID: ${user.id}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Tier ID: ${user.tier_id}`);
        console.log(`   Tier Name: ${user.tier_name}`);
        console.log(`   User allowed_streams: ${JSON.stringify(user.allowed_streams)}`);
        console.log(`   Tier allowed_streams: ${JSON.stringify(user.tier_allowed_streams)}`);
        
        // Check stream requirements
        const streamResult = await query(`
            SELECT stream_name, required_tier_id, credits_per_message, is_active
            FROM stream_definitions
            WHERE stream_name = 'tokens-launched'
        `);
        
        if (streamResult.rows.length > 0) {
            const stream = streamResult.rows[0];
            console.log('\n📡 Stream Details:');
            console.log(`   Name: ${stream.stream_name}`);
            console.log(`   Required Tier ID: ${stream.required_tier_id}`);
            console.log(`   Credits per Message: ${stream.credits_per_message}`);
            console.log(`   Is Active: ${stream.is_active}`);
            
            // Test access logic
            console.log('\n🔍 Access Logic Test:');
            
            // Check if user allowed_streams includes '*'
            const hasWildcard = user.allowed_streams && user.allowed_streams.includes('*');
            console.log(`   User has wildcard (*): ${hasWildcard}`);
            
            // Check if user allowed_streams includes specific stream
            const hasSpecificStream = user.allowed_streams && user.allowed_streams.includes('tokens-launched');
            console.log(`   User has specific stream: ${hasSpecificStream}`);
            
            // Check tier requirement
            const meetsTierRequirement = user.tier_id >= stream.required_tier_id;
            console.log(`   Meets tier requirement: ${meetsTierRequirement} (user tier ${user.tier_id} >= required ${stream.required_tier_id})`);
            
            // Final access decision
            const shouldHaveAccess = hasWildcard || hasSpecificStream;
            console.log(`   Should have access: ${shouldHaveAccess}`);
            
            if (!shouldHaveAccess) {
                console.log('\n🔧 Issue found: User does not have wildcard or specific stream access');
                console.log('   This suggests the allowed_streams field is not set correctly');
            }
        }
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        process.exit(0);
    }
}

debugUserAccess();
