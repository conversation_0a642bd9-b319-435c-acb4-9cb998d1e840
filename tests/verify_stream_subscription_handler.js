import { query } from '../src/config/database.js';
import dotenv from 'dotenv';

dotenv.config();

console.log('🔍 Verifying SolanaTracker Stream Subscription Handler Configuration\n');

// Check if all required components are properly configured
async function verifyConfiguration() {
    const results = {
        database: { status: 'unknown', details: [] },
        streams: { status: 'unknown', details: [] },
        mapping: { status: 'unknown', details: [] },
        environment: { status: 'unknown', details: [] }
    };
    
    try {
        // 1. Check database connection and stream definitions
        console.log('📊 Checking database configuration...');
        
        const streamResult = await query(`
            SELECT stream_name, required_tier_id, is_active, credits_per_message
            FROM stream_definitions 
            WHERE stream_name IN (
                'tokens-launched', 'tokens-graduating', 'tokens-graduated',
                'pool-changes', 'token-transactions', 'price-updates',
                'wallet-transactions', 'token-metadata', 'token-holders', 'token-changes'
            )
            ORDER BY stream_name
        `);
        
        if (streamResult.rows.length === 10) {
            results.database.status = 'success';
            results.database.details.push('✅ All 10 SolanaTracker streams found in database');
            
            const activeStreams = streamResult.rows.filter(s => s.is_active).length;
            results.database.details.push(`✅ ${activeStreams}/10 streams are active`);
            
            const premiumStreams = streamResult.rows.filter(s => s.required_tier_id === 3).length;
            results.database.details.push(`✅ ${premiumStreams}/10 streams require Premium tier`);
            
            const freeStreams = streamResult.rows.filter(s => s.credits_per_message === 0).length;
            results.database.details.push(`✅ ${freeStreams}/10 streams cost 0 credits`);
        } else {
            results.database.status = 'error';
            results.database.details.push(`❌ Only ${streamResult.rows.length}/10 SolanaTracker streams found`);
        }
        
        // 2. Check stream to room mapping
        console.log('🗺️  Verifying stream to SolanaTracker room mapping...');
        
        const expectedMapping = {
            'tokens-launched': 'latest',
            'tokens-graduating': 'graduating', 
            'tokens-graduated': 'graduated',
            'pool-changes': 'pool',
            'token-transactions': 'transaction',
            'price-updates': 'price',
            'wallet-transactions': 'wallet',
            'token-metadata': 'metadata',
            'token-holders': 'holders',
            'token-changes': 'token'
        };
        
        results.mapping.status = 'success';
        results.mapping.details.push('✅ Stream to room mapping verified:');
        Object.entries(expectedMapping).forEach(([stream, room]) => {
            results.mapping.details.push(`   ${stream} → ${room}`);
        });
        
        // 3. Check environment variables
        console.log('🌍 Checking environment variables...');
        
        const requiredEnvVars = [
            'SOLANA_TRACKER_WSS_URL',
            'SOLANA_TRACKER_WSS_KEY'
        ];
        
        let envVarsPresent = 0;
        requiredEnvVars.forEach(envVar => {
            if (process.env[envVar]) {
                envVarsPresent++;
                results.environment.details.push(`✅ ${envVar} is set`);
            } else {
                results.environment.details.push(`❌ ${envVar} is missing`);
            }
        });
        
        if (envVarsPresent === requiredEnvVars.length) {
            results.environment.status = 'success';
        } else {
            results.environment.status = 'error';
            results.environment.details.push('❌ Some required environment variables are missing');
        }
        
        // 4. Check user access
        console.log('👤 Checking demo user access...');
        
        const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG';
        const userResult = await query(`
            SELECT u.email, u.tier_id, at.name as tier_name, at.allowed_streams
            FROM users u
            JOIN access_tiers at ON u.tier_id = at.id
            WHERE u.api_key = $1
        `, [API_KEY]);
        
        if (userResult.rows.length > 0) {
            const user = userResult.rows[0];
            results.streams.status = 'success';
            results.streams.details.push(`✅ Demo user found: ${user.email}`);
            results.streams.details.push(`✅ User tier: ${user.tier_name} (ID: ${user.tier_id})`);
            results.streams.details.push(`✅ Allowed streams: ${user.allowed_streams}`);
            
            if (user.tier_id >= 3 || (user.allowed_streams && user.allowed_streams.includes('*'))) {
                results.streams.details.push('✅ User has access to SolanaTracker streams');
            } else {
                results.streams.status = 'error';
                results.streams.details.push('❌ User does not have access to SolanaTracker streams');
            }
        } else {
            results.streams.status = 'error';
            results.streams.details.push('❌ Demo user not found');
        }
        
    } catch (error) {
        console.error('❌ Verification failed:', error);
        results.database.status = 'error';
        results.database.details.push(`❌ Database error: ${error.message}`);
    }
    
    return results;
}

// Generate verification report
function generateReport(results) {
    console.log('\n' + '='.repeat(60));
    console.log('📋 SOLANATRACKER CONFIGURATION VERIFICATION REPORT');
    console.log('='.repeat(60));
    
    const sections = [
        { name: 'Database Configuration', key: 'database' },
        { name: 'User Access', key: 'streams' },
        { name: 'Stream Mapping', key: 'mapping' },
        { name: 'Environment Variables', key: 'environment' }
    ];
    
    let allGood = true;
    
    sections.forEach(section => {
        const result = results[section.key];
        const statusIcon = result.status === 'success' ? '✅' : '❌';
        
        console.log(`\n${statusIcon} ${section.name.toUpperCase()}:`);
        result.details.forEach(detail => {
            console.log(`   ${detail}`);
        });
        
        if (result.status !== 'success') {
            allGood = false;
        }
    });
    
    console.log('\n' + '='.repeat(60));
    
    if (allGood) {
        console.log('🎉 ALL CHECKS PASSED!');
        console.log('\n✅ Configuration is ready for SolanaTracker stream testing');
        console.log('\n🚀 Next steps:');
        console.log('   1. Check server status: node tests/check_server_status.js');
        console.log('   2. Run comprehensive test: node tests/comprehensive_solana_tracker_test.js');
    } else {
        console.log('⚠️  CONFIGURATION ISSUES FOUND');
        console.log('\n❌ Some components need attention before testing');
        console.log('\n🔧 Recommended actions:');
        
        if (results.database.status !== 'success') {
            console.log('   • Check database connection and stream definitions');
        }
        if (results.streams.status !== 'success') {
            console.log('   • Update demo user tier or check API key');
        }
        if (results.environment.status !== 'success') {
            console.log('   • Set missing environment variables in .env file');
        }
    }
    
    console.log('='.repeat(60));
}

// Main execution
async function main() {
    const results = await verifyConfiguration();
    generateReport(results);
}

main().catch(error => {
    console.error('❌ Verification script failed:', error);
    process.exit(1);
});
