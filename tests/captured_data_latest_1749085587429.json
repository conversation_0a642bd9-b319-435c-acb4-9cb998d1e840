{"type": "message", "data": {"token": {"name": "fruitcake", "symbol": "fruitcake", "mint": "BSidPXrbtoBtrj7BsmC9tYCPNUh5Pr26cj6pJzTGpump", "uri": "https://ipfs.io/ipfs/bafkreiarqkko4fg4et5za7lslkojt34jjw6ncxubihxtcsxfyylinefwom", "decimals": 6, "hasFileMetaData": true, "createdOn": "https://pump.fun"}, "pools": [{"liquidity": {"quote": 60, "usd": 9242.************}, "price": {"quote": 2.7958993476234855e-08, "usd": 4.306************e-06}, "tokenSupply": 1000000000, "lpBurn": 100, "tokenAddress": "BSidPXrbtoBtrj7BsmC9tYCPNUh5Pr26cj6pJzTGpump", "marketCap": {"quote": 27.958993476234856, "usd": 4306.************}, "decimals": 6, "security": {"freezeAuthority": null, "mintAuthority": null}, "quoteToken": "So11111111111111111111111111111111111111112", "market": "pumpfun", "curvePercentage": 0, "curve": "2NxyE8PfAC6m3C4B9Mww43ZV8aPJ3oqo8yjpwT617Ud2", "deployer": "2K48CkZQN4eqqo4UY789399M9eipsEgZoCguEe6KfndA", "openTime": 0, "createdAt": 1749085587424, "poolId": "BSidPXrbtoBtrj7BsmC9tYCPNUh5Pr26cj6pJzTGpump"}], "events": {"30m": {"priceChangePercentage": 0}, "1h": {"priceChangePercentage": 0}, "4h": {"priceChangePercentage": 0}, "24h": {"priceChangePercentage": 0}}, "risk": {"rugged": false, "risks": [{"name": "Bonding curve not complete", "description": "No PumpSwap liquidity pool, bonding curve not complete", "level": "warning", "score": 4000}], "score": 3}}, "room": "latest"}