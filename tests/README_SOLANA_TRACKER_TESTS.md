# SolanaTracker WebSocket Stream Test Suite

This comprehensive test suite verifies that all 10 SolanaTracker WebSocket streams are working correctly and delivering real-time data to subscribed clients.

## 🎯 Test Coverage

The test suite covers all SolanaTracker streams with intelligent timeout strategies:

### High-Frequency Streams (60s timeout)
- **tokens-launched** → SolanaTracker 'latest' room
- **pool-changes** → SolanaTracker 'pool' room  
- **price-updates** → SolanaTracker 'price' room

### Medium-Frequency Streams (3min timeout)
- **tokens-graduating** → SolanaTracker 'graduating' room
- **token-transactions** → SolanaTracker 'transaction' room

### Low-Frequency Streams (5min timeout)
- **tokens-graduated** → SolanaTracker 'graduated' room
- **wallet-transactions** → SolanaTracker 'wallet' room
- **token-metadata** → SolanaTracker 'metadata' room
- **token-holders** → SolanaTracker 'holders' room
- **token-changes** → SolanaTracker 'token' room

## 🚀 Quick Start

### Option 1: Run Complete Test Suite (Recommended)
```bash
node tests/run_solana_tracker_tests.js
```

This runs all tests in sequence:
1. Configuration verification
2. Server status check
3. Comprehensive stream testing

### Option 2: Run Individual Tests

#### Check Configuration
```bash
node tests/verify_stream_subscription_handler.js
```

#### Check Server Status
```bash
node tests/check_server_status.js
```

#### Run Stream Tests Only
```bash
node tests/comprehensive_solana_tracker_test.js
```

## 📋 Test Verification Points

Each test verifies:

✅ **WebSocket Connection** - Establishes successfully  
✅ **Authentication** - Enterprise API key accepted  
✅ **Subscription** - Request accepted without access denied errors  
✅ **Worker Subscription** - SolanaTracker worker auto-subscribes to corresponding room  
✅ **Data Flow** - Stream data flows: SolanaTracker → StreamManager → WebSocket client  
✅ **Message Format** - Data matches expected SolanaTracker structure  

## 📊 Test Results

### Success Criteria
- **Subscription Confirmed**: Stream subscription accepted
- **Data Received**: At least one real-time message received within timeout
- **Proper Format**: Message structure matches SolanaTracker API format

### Timeout Handling
- **High-frequency streams**: Expected to receive data within 60 seconds
- **Medium-frequency streams**: May take up to 3 minutes
- **Low-frequency streams**: May take up to 5 minutes (normal behavior)

### Result Files
Detailed test results are saved to:
```
test-results/solana-tracker-test-[timestamp].json
```

## 🔧 Troubleshooting

### Common Issues & Solutions

#### 1. "Access denied to stream" errors
**Cause**: WebSocket access control not updated  
**Solution**: Restart the server to apply recent fixes
```bash
# Stop current server (Ctrl+C)
pnpm start
```

#### 2. All streams timeout
**Cause**: SolanaTracker worker not connecting  
**Solutions**:
- Check environment variables in `.env`:
  ```
  SOLANA_TRACKER_WSS_URL=wss://datastream.solanatracker.io
  SOLANA_TRACKER_WSS_KEY=your_api_key_here
  ```
- Verify SolanaTracker API credentials
- Check server logs for connection errors

#### 3. Some streams work, others don't
**Cause**: Partial SolanaTracker room availability  
**Solution**: This is normal - some rooms may have less activity

#### 4. Demo user access issues
**Cause**: User tier insufficient  
**Solution**: Update demo user to Enterprise tier:
```bash
node update_demo_user_tier.js
```

## 📈 Understanding Results

### Success Rate Interpretation
- **90-100%**: Excellent - All systems working
- **70-89%**: Good - Minor issues with low-frequency streams
- **50-69%**: Fair - Some configuration issues
- **<50%**: Poor - Major problems requiring attention

### Stream-Specific Notes

#### tokens-launched (latest room)
- **Highest activity** - Should receive data quickly
- **If fails**: Check SolanaTracker API connectivity

#### tokens-graduating/graduated
- **Lower activity** - May timeout during quiet periods
- **Normal behavior**: Timeouts are expected

#### pool-changes, price-updates
- **High activity** - Should receive data within 60s
- **If fails**: Check SolanaTracker room subscriptions

#### Metadata/holders/wallet streams
- **Very low activity** - Timeouts are normal
- **Success indicates**: Full system integration working

## 🔍 Debug Mode

For detailed debugging, check server logs while running tests:

1. Start server with verbose logging
2. Run tests in another terminal
3. Monitor logs for:
   - WebSocket connections
   - Subscription events
   - SolanaTracker worker activity
   - Stream data flow

## 📝 Test Reports

The comprehensive test generates:

1. **Console Output**: Real-time progress and results
2. **JSON Report**: Detailed results saved to file
3. **Recommendations**: Specific actions based on results
4. **Error Analysis**: Detailed error information

## 🎯 Expected Outcomes

### Ideal Results
- All high-frequency streams receive data
- Most medium-frequency streams receive data  
- Some low-frequency streams may timeout (normal)
- No access denied or connection errors

### Acceptable Results
- 70%+ success rate
- High-frequency streams working
- No authentication errors
- Timeouts only on low-frequency streams

### Problematic Results
- Multiple access denied errors
- High-frequency streams timing out
- Connection failures
- <50% success rate

## 🚨 Emergency Procedures

If tests reveal critical issues:

1. **Immediate**: Restart server to apply fixes
2. **Check**: Environment variables and API keys
3. **Verify**: Database configuration and user permissions
4. **Monitor**: Server logs for specific error messages
5. **Escalate**: If SolanaTracker API issues persist

## 📞 Support

For test suite issues or questions:
1. Check server logs for detailed error messages
2. Verify all environment variables are set
3. Ensure demo user has Enterprise tier access
4. Review SolanaTracker API documentation for room availability
