# Rate Limiting Test Suite

This directory contains comprehensive tests for verifying that rate limiting is working correctly across all user tiers and admin users.

## Overview

The rate limiting system enforces different limits based on user tiers:

| Tier | Requests/Minute | WebSocket Connections | Credits/Month |
|------|-----------------|----------------------|---------------|
| **Free** | 10 | 1 | 1,000 |
| **Basic** | 60 | 3 | 1,000,000 |
| **Premium** | 300 | 5 | 5,000,000 |
| **Enterprise** | 1,000 | 10 | Unlimited |
| **Admin** | Unlimited | Unlimited | N/A |

## Test Files

### `test-rate-limiting.js`
Main test script that performs comprehensive rate limiting tests:

- **API Rate Limiting**: Tests HTTP request limits per minute for each tier
- **WebSocket Connection Limits**: Tests maximum concurrent WebSocket connections
- **Admin Bypass**: Verifies that admin users have unlimited access
- **Burst Testing**: Attempts to exceed limits to verify enforcement

### `setup-test-users.js`
<PERSON><PERSON>t to create test users for each tier with known API keys.

### `run-rate-limit-tests.sh`
Bash script that orchestrates the complete test process:
1. Checks if API server is running
2. Sets up test users
3. Runs rate limiting tests
4. Provides summary

## Setup Files

### `scripts/create-test-users.sql`
SQL script that:
- Enables the free tier for testing
- Creates test users for each tier
- Displays test user information

## Running the Tests

### Quick Test (Manual Setup Required)
```bash
# Make sure test users exist in database
pnpm setup:test-users

# Run rate limiting tests
pnpm test:rate-limiting
```

### Full Automated Test
```bash
# Complete test suite with automatic setup
pnpm test:rate-limiting:full
```

### Individual Components
```bash
# Setup test users only
pnpm setup:test-users

# Run tests only (assumes users exist)
pnpm test:rate-limiting
```

## Test User API Keys

After running `pnpm setup:test-users`, you'll have these test users:

- **Free Tier**: `test_free_api_key_12345`
- **Basic Tier**: `test_basic_api_key_12345`
- **Premium Tier**: `test_premium_api_key_12345`
- **Enterprise Tier**: `test_enterprise_api_key_12345`
- **Admin**: `admin_api_key_super_secure_change_in_production`

## What the Tests Verify

### API Rate Limiting
1. **Normal Usage**: Verifies users can make requests within their tier limits
2. **Burst Protection**: Attempts to exceed limits and verifies 429 responses
3. **Admin Bypass**: Confirms admin users are not rate limited
4. **Tier Enforcement**: Each tier gets its correct limit

### WebSocket Connection Limits
1. **Connection Limits**: Tests maximum concurrent connections per tier
2. **Connection Rejection**: Verifies excess connections are rejected
3. **Admin Unlimited**: Confirms admin users can open many connections
4. **Proper Cleanup**: Ensures connections are properly tracked

### Expected Results

✅ **Passing Tests Should Show**:
- Free tier: Rate limited at ~10 requests/minute, 1 WebSocket connection
- Basic tier: Rate limited at ~60 requests/minute, 3 WebSocket connections
- Premium tier: Rate limited at ~300 requests/minute, 5 WebSocket connections
- Enterprise tier: Rate limited at ~1000 requests/minute, 10 WebSocket connections
- Admin users: No rate limiting, unlimited connections

❌ **Failing Tests Indicate**:
- Rate limiting is not properly configured
- Tier limits are not being enforced
- Admin bypass is not working
- WebSocket connection tracking has issues

## Troubleshooting

### Common Issues

1. **"API server is not running"**
   ```bash
   # Start the development server
   pnpm dev
   ```

2. **"Invalid API key" errors**
   ```bash
   # Recreate test users
   pnpm setup:test-users
   ```

3. **Rate limiting not working**
   - Check that rate limiting is enabled in `src/routes/api.js`
   - Verify Redis is running and accessible
   - Check environment variables for rate limiting configuration

4. **WebSocket connection issues**
   - Ensure WebSocket server is running
   - Check that test users have correct tier assignments
   - Verify WebSocket connection tracking in Redis

### Manual Testing

You can also test rate limiting manually:

```bash
# Test API rate limiting
for i in {1..15}; do
  curl -H "X-API-Key: test_free_api_key_12345" \
       http://localhost:3001/api/v1/status
  echo "Request $i completed"
done

# Test WebSocket connections (requires wscat)
wscat -c "ws://localhost:3001/ws?apiKey=test_free_api_key_12345"
```

## Configuration

Rate limiting behavior can be adjusted in:

- `src/middleware/rateLimiter.js` - Rate limiting logic
- `SQL/03_insert_default_data.sql` - Tier definitions
- Environment variables for default limits

## Integration with CI/CD

These tests can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Test Rate Limiting
  run: |
    npm run dev &
    sleep 10
    npm run test:rate-limiting:full
```

The tests provide detailed output and exit codes suitable for automated testing environments.
