import { WebSocket } from 'ws';
import dotenv from 'dotenv';

dotenv.config();

console.log('🧪 Testing Parameterized SolanaTracker Graduating Stream\n');

const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG';
const WS_URL = `ws://localhost:3001/ws?apiKey=${API_KEY}`;

async function testParameterizedGraduating() {
    return new Promise((resolve) => {
        console.log('🔍 Testing tokens-graduating with marketCapThreshold parameter...');
        
        const ws = new WebSocket(WS_URL);
        let subscriptionConfirmed = false;
        let messageReceived = false;
        let connectionId = null;
        
        const timeout = setTimeout(() => {
            console.log('⏰ Test completed (3 minute timeout)');
            ws.close();
            
            resolve({
                subscriptionConfirmed,
                messageReceived,
                status: messageReceived ? 'success' : 'timeout'
            });
        }, 180000); // 3 minutes
        
        ws.on('open', () => {
            console.log('✅ WebSocket connected');
            
            setTimeout(() => {
                const subscribeMessage = {
                    type: 'subscribe',
                    payload: {
                        stream: 'tokens-graduating',
                        parameters: {
                            marketCapThreshold: 175
                        }
                    }
                };
                
                console.log('📡 Subscribing with parameters:', JSON.stringify(subscribeMessage, null, 2));
                ws.send(JSON.stringify(subscribeMessage));
            }, 1000);
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                
                if (message.type === 'connected') {
                    connectionId = message.connectionId;
                    console.log(`🔗 Connection established (ID: ${connectionId})`);
                } else if (message.type === 'subscribed') {
                    subscriptionConfirmed = true;
                    console.log('✅ Subscription confirmed for tokens-graduating with parameters');
                    console.log('📋 Subscription details:', JSON.stringify(message, null, 2));
                    console.log('⏳ Waiting for stream data...');
                } else if (message.type === 'stream_data' && message.stream === 'tokens-graduating') {
                    messageReceived = true;
                    
                    console.log('🎉 SUCCESS! Received parameterized graduating data');
                    console.log('📨 Message data:', JSON.stringify(message.data, null, 2));
                    
                    // Check if market cap threshold is included
                    if (message.data.marketCapThreshold) {
                        console.log('✅ Market cap threshold parameter confirmed:', message.data.marketCapThreshold);
                    } else {
                        console.log('⚠️  Market cap threshold not found in response');
                    }
                    
                    clearTimeout(timeout);
                    ws.close();
                    
                    resolve({
                        subscriptionConfirmed,
                        messageReceived,
                        status: 'success',
                        data: message.data
                    });
                } else if (message.type === 'error') {
                    console.log(`❌ ERROR: ${message.error}`);
                    
                    clearTimeout(timeout);
                    ws.close();
                    
                    resolve({
                        subscriptionConfirmed,
                        messageReceived,
                        status: 'error',
                        error: message.error
                    });
                }
            } catch (error) {
                console.error('❌ Error parsing message:', error);
            }
        });
        
        ws.on('error', (error) => {
            console.error('❌ WebSocket error:', error.message);
            clearTimeout(timeout);
            resolve({
                subscriptionConfirmed,
                messageReceived,
                status: 'connection_error',
                error: error.message
            });
        });
        
        ws.on('close', (code, reason) => {
            console.log(`🔌 WebSocket closed (code: ${code}, reason: ${reason || 'none'})`);
        });
    });
}

async function main() {
    console.log('🚀 Starting parameterized graduating stream test...');
    console.log('📊 Testing subscription with marketCapThreshold: 175 SOL\n');
    
    const result = await testParameterizedGraduating();
    
    console.log('\n' + '='.repeat(60));
    console.log('📋 PARAMETERIZED GRADUATING STREAM TEST RESULTS');
    console.log('='.repeat(60));
    
    console.log(`Status: ${result.status}`);
    console.log(`Subscription confirmed: ${result.subscriptionConfirmed ? '✅ YES' : '❌ NO'}`);
    console.log(`Data received: ${result.messageReceived ? '✅ YES' : '❌ NO'}`);
    
    if (result.error) {
        console.log(`Error: ${result.error}`);
    }
    
    if (result.data && result.data.marketCapThreshold) {
        console.log('\n🎯 Parameter Verification:');
        console.log(`✅ Market cap threshold included: ${result.data.marketCapThreshold.amount} ${result.data.marketCapThreshold.currency.toUpperCase()}`);
    }
    
    console.log('\n💡 Analysis:');
    if (result.status === 'success') {
        console.log('🎉 Parameterized graduating stream is working correctly!');
        console.log('✅ Parameters are properly passed to SolanaTracker API');
        console.log('✅ Market cap threshold is included in response data');
    } else if (result.status === 'timeout' && result.subscriptionConfirmed) {
        console.log('⏰ Subscription successful but no data received (normal for low-activity periods)');
        console.log('✅ Parameter handling is working correctly');
    } else if (result.status === 'error') {
        console.log('❌ Error occurred - check implementation');
    } else {
        console.log('❌ Connection or subscription failed');
    }
    
    console.log('\n🔗 Related Documentation:');
    console.log('• SolanaTracker Guide: docs/SOLANA_TRACKER_GUIDE.md');
    console.log('• WebSocket Reference: docs/SOLANA_TRACKER_WEBSOCKET_REFERENCE.md');
    console.log('• Official SolanaTracker Docs: https://docs.solanatracker.io/public-data-api/websocket');
    
    console.log('='.repeat(60));
}

main().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
});
