import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🧪 SolanaTracker WebSocket Stream Test Runner\n');

// Utility function to run a Node.js script and capture output
function runScript(scriptPath, description) {
    return new Promise((resolve, reject) => {
        console.log(`🔄 ${description}...`);
        
        const child = spawn('node', [scriptPath], {
            stdio: 'inherit',
            cwd: join(__dirname, '..')
        });
        
        child.on('close', (code) => {
            if (code === 0) {
                console.log(`✅ ${description} completed successfully\n`);
                resolve(true);
            } else {
                console.log(`❌ ${description} failed with exit code ${code}\n`);
                resolve(false);
            }
        });
        
        child.on('error', (error) => {
            console.error(`❌ Error running ${description}:`, error.message);
            reject(error);
        });
    });
}

// Main test orchestration
async function runTestSuite() {
    console.log('🚀 Starting comprehensive SolanaTracker testing process...\n');
    
    const testSteps = [
        {
            script: 'tests/verify_stream_subscription_handler.js',
            description: 'Configuration Verification',
            required: true
        },
        {
            script: 'tests/check_server_status.js', 
            description: 'Server Status Check',
            required: true
        },
        {
            script: 'tests/comprehensive_solana_tracker_test.js',
            description: 'Comprehensive Stream Testing',
            required: false
        }
    ];
    
    let allPassed = true;
    
    for (const step of testSteps) {
        try {
            const success = await runScript(step.script, step.description);
            
            if (!success) {
                allPassed = false;
                
                if (step.required) {
                    console.log(`❌ Required step "${step.description}" failed. Stopping test suite.`);
                    break;
                } else {
                    console.log(`⚠️  Optional step "${step.description}" failed. Continuing...`);
                }
            }
            
            // Brief pause between steps
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.error(`❌ Failed to run ${step.description}:`, error.message);
            allPassed = false;
            
            if (step.required) {
                break;
            }
        }
    }
    
    // Final summary
    console.log('='.repeat(80));
    console.log('📋 TEST SUITE SUMMARY');
    console.log('='.repeat(80));
    
    if (allPassed) {
        console.log('🎉 All tests completed successfully!');
        console.log('\n✅ SolanaTracker WebSocket streams are ready for production use');
        console.log('\n📊 Check the test-results/ directory for detailed reports');
    } else {
        console.log('⚠️  Some tests failed or encountered issues');
        console.log('\n🔧 Review the output above for specific recommendations');
        console.log('\n💡 Common solutions:');
        console.log('   • Restart the server to apply WebSocket access control fixes');
        console.log('   • Check SolanaTracker API connectivity and credentials');
        console.log('   • Verify database configuration and user permissions');
    }
    
    console.log('='.repeat(80));
}

// Handle script interruption
process.on('SIGINT', () => {
    console.log('\n\n👋 Test suite interrupted by user');
    process.exit(0);
});

// Run the test suite
runTestSuite().catch(error => {
    console.error('❌ Test suite runner failed:', error);
    process.exit(1);
});
