#!/usr/bin/env node

/**
 * Quick Rate Limiting Test
 * Simple test to verify rate limiting is working
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3001';
const API_KEY = 'test_free_api_key_12345';

async function quickTest() {
    console.log('🧪 Quick Rate Limiting Test');
    console.log('============================');
    
    try {
        console.log('Testing with Free tier API key:', API_KEY);
        console.log('Expected limit: 10 requests/minute\n');
        
        let successCount = 0;
        let rateLimitHit = false;
        
        for (let i = 1; i <= 15; i++) {
            try {
                const response = await axios.get(`${BASE_URL}/api/v1/status`, {
                    headers: { 'X-API-Key': API_KEY },
                    timeout: 5000
                });
                
                if (response.status === 200) {
                    successCount++;
                    console.log(`✅ Request ${i}: Success (${response.data.status})`);
                    
                    // Check rate limit headers
                    const limit = response.headers['ratelimit-limit'];
                    const remaining = response.headers['ratelimit-remaining'];
                    const reset = response.headers['ratelimit-reset'];
                    
                    console.log(`   Rate Limit Info: ${remaining}/${limit} remaining, resets in ${reset}s`);
                } else {
                    console.log(`⚠️ Request ${i}: Unexpected status ${response.status}`);
                }
                
            } catch (error) {
                if (error.response?.status === 429) {
                    rateLimitHit = true;
                    console.log(`🚫 Request ${i}: Rate limited (429)`);
                    console.log(`   Rate limit headers:`, error.response.headers);
                    break;
                } else {
                    console.log(`❌ Request ${i}: Error - ${error.message}`);
                }
            }
            
            // Small delay between requests
            await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        console.log('\n📊 Results:');
        console.log(`   Successful requests: ${successCount}`);
        console.log(`   Rate limit triggered: ${rateLimitHit ? 'Yes' : 'No'}`);
        
        if (rateLimitHit && successCount <= 12) {
            console.log('✅ Rate limiting appears to be working!');
        } else if (!rateLimitHit && successCount >= 15) {
            console.log('❌ Rate limiting is NOT working - all requests succeeded');
        } else {
            console.log('⚠️ Unclear results - may need further investigation');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

quickTest();
