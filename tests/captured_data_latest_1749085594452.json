{"type": "message", "data": {"token": {"name": "SolGraph", "symbol": "SGFUN", "mint": "********************************************", "uri": "https://ipfs-forward.solanatracker.io/ipfs/QmRGKH65CUxDL2Phz7vrPWzHF6sV5vokG4NEX65jSxsNni", "decimals": 6, "isMutable": false, "description": "SolGraph.fun - transform simple SOL transfers into untraceable multi-wallet networks", "image": "https://ipfs.io/ipfs/QmaM4pnNWQcxWXzAw64tFJEuKqPZjVkLAmcDRj8KzmmkxJ", "showName": true, "createdOn": "https://pump.fun", "twitter": "https://x.com/solgraphfun", "website": "https://solgraph.fun/", "hasFileMetaData": true}, "pools": [{"liquidity": {"quote": 84.990565162, "usd": 26184.8596593787}, "price": {"quote": 8.584926253806574e-08, "usd": 1.3224708455200899e-05}, "tokenSupply": 1000000000, "lpBurn": 0, "tokenAddress": "********************************************", "marketCap": {"quote": 85.84926253806574, "usd": 13224.************}, "decimals": 6, "security": {"freezeAuthority": null, "mintAuthority": null}, "quoteToken": "So11111111111111111111111111111111111111112", "market": "pumpfun-amm", "deployer": null, "poolId": "CVqJAZhd88E9J5PJd6gyM6H98ymQ3Apf6n1rw4ZHLYut", "createdAt": 1749085594411}], "events": {"30m": {"priceChangePercentage": 0}, "1h": {"priceChangePercentage": 0}, "4h": {"priceChangePercentage": 0}, "24h": {"priceChangePercentage": 0}}, "risk": {"rugged": false, "risks": [{"name": "LP Burned", "description": "Allows the owner to remove liquidity at any time.", "value": "0%", "level": "danger", "score": 4000}], "score": 3}}, "room": "latest"}