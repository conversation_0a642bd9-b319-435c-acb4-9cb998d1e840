import { WebSocket } from 'ws';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🧪 Testing tokens-launched stream subscription fix\n');

const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG'; // Demo user API key (Enterprise tier)
const WS_URL = `ws://localhost:3001/ws?apiKey=${API_KEY}`;

async function testTokensLaunchedStream() {
    return new Promise((resolve, reject) => {
        console.log('🔌 Connecting to WebSocket...');
        const ws = new WebSocket(WS_URL);
        
        let messageCount = 0;
        const maxMessages = 3; // Collect a few messages to verify
        const timeout = 30000; // 30 seconds timeout
        
        const timer = setTimeout(() => {
            console.log('⏰ Test timeout reached');
            ws.close();
            if (messageCount > 0) {
                resolve(`✅ Success! Received ${messageCount} messages`);
            } else {
                reject('❌ No messages received within timeout period');
            }
        }, timeout);
        
        ws.on('open', () => {
            console.log('✅ WebSocket connected');

            // Wait a moment for the connection to be fully established
            setTimeout(() => {
                // Subscribe to tokens-launched stream
                const subscribeMessage = {
                    type: 'subscribe',
                    payload: {
                        stream: 'tokens-launched'
                    }
                };

                console.log('📡 Subscribing to tokens-launched stream...');
                console.log('📤 Sending message:', JSON.stringify(subscribeMessage));
                ws.send(JSON.stringify(subscribeMessage));
            }, 1000);
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                console.log('📨 Received message:', message);

                if (message.type === 'subscribed') {
                    console.log('✅ Successfully subscribed to tokens-launched');
                    console.log('⏳ Waiting for stream data...');
                } else if (message.type === 'stream_data' && message.stream === 'tokens-launched') {
                    messageCount++;
                    console.log(`📨 Message ${messageCount}: ${message.data.token?.symbol || 'Unknown'} - ${message.data.token?.name || 'No name'}`);

                    if (messageCount >= maxMessages) {
                        clearTimeout(timer);
                        ws.close();
                        resolve(`✅ Success! Received ${messageCount} messages from tokens-launched stream`);
                    }
                } else if (message.type === 'error') {
                    clearTimeout(timer);
                    ws.close();
                    reject(`❌ WebSocket error: ${message.message}`);
                }
            } catch (error) {
                console.error('❌ Error parsing message:', error);
                console.error('Raw data:', data.toString());
            }
        });
        
        ws.on('error', (error) => {
            clearTimeout(timer);
            reject(`❌ WebSocket connection error: ${error.message}`);
        });
        
        ws.on('close', (code, reason) => {
            console.log(`🔌 WebSocket connection closed (code: ${code}, reason: ${reason})`);
        });
    });
}

// Run the test
async function main() {
    try {
        const result = await testTokensLaunchedStream();
        console.log('\n' + result);
        console.log('\n🎉 tokens-launched stream is working correctly!');
        console.log('The fix successfully resolved the subscription issue.');
    } catch (error) {
        console.error('\n❌ Test failed:', error);
        console.log('\nThe tokens-launched stream may still have issues.');
    }
}

main();
