import { WebSocket } from 'ws';
import dotenv from 'dotenv';

dotenv.config();

console.log('🔍 Checking server status and WebSocket access control...\n');

const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG';
const WS_URL = `ws://localhost:3001/ws?apiKey=${API_KEY}`;

async function checkServerStatus() {
    return new Promise((resolve) => {
        console.log('🔌 Testing WebSocket connection...');
        
        const ws = new WebSocket(WS_URL);
        let connectionEstablished = false;
        let subscriptionTested = false;
        
        const timeout = setTimeout(() => {
            if (!connectionEstablished) {
                console.log('❌ Server appears to be down - WebSocket connection failed');
                resolve({
                    serverRunning: false,
                    accessControlFixed: false,
                    recommendation: 'Start the server with: pnpm start'
                });
            } else {
                console.log('⏰ Subscription test timed out');
                ws.close();
                resolve({
                    serverRunning: true,
                    accessControlFixed: false,
                    recommendation: 'Server is running but access control may need restart'
                });
            }
        }, 10000);
        
        ws.on('open', () => {
            connectionEstablished = true;
            console.log('✅ WebSocket connection established');
            
            // Test subscription to tokens-launched
            setTimeout(() => {
                console.log('📡 Testing subscription to tokens-launched...');
                const subscribeMessage = {
                    type: 'subscribe',
                    payload: {
                        stream: 'tokens-launched'
                    }
                };
                ws.send(JSON.stringify(subscribeMessage));
            }, 1000);
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                
                if (message.type === 'connected') {
                    console.log(`🔗 Connection ID: ${message.connectionId}`);
                } else if (message.type === 'subscribed') {
                    subscriptionTested = true;
                    console.log('✅ Subscription successful - access control is working!');
                    
                    clearTimeout(timeout);
                    ws.close();
                    resolve({
                        serverRunning: true,
                        accessControlFixed: true,
                        recommendation: 'Server is ready for comprehensive testing'
                    });
                } else if (message.type === 'error') {
                    subscriptionTested = true;
                    console.log(`❌ Subscription error: ${message.error}`);
                    
                    clearTimeout(timeout);
                    ws.close();
                    resolve({
                        serverRunning: true,
                        accessControlFixed: false,
                        error: message.error,
                        recommendation: 'Restart server to apply WebSocket access control fixes'
                    });
                }
            } catch (error) {
                console.error('❌ Error parsing message:', error);
            }
        });
        
        ws.on('error', (error) => {
            console.error('❌ WebSocket error:', error.message);
            clearTimeout(timeout);
            resolve({
                serverRunning: false,
                accessControlFixed: false,
                error: error.message,
                recommendation: 'Check if server is running and accessible'
            });
        });
        
        ws.on('close', (code, reason) => {
            console.log(`🔌 WebSocket closed (code: ${code}, reason: ${reason || 'none'})`);
        });
    });
}

async function main() {
    const status = await checkServerStatus();
    
    console.log('\n📋 SERVER STATUS REPORT:');
    console.log(`   Server running: ${status.serverRunning ? '✅ YES' : '❌ NO'}`);
    console.log(`   Access control fixed: ${status.accessControlFixed ? '✅ YES' : '❌ NO'}`);
    
    if (status.error) {
        console.log(`   Error: ${status.error}`);
    }
    
    console.log(`\n💡 Recommendation: ${status.recommendation}`);
    
    if (status.serverRunning && status.accessControlFixed) {
        console.log('\n🚀 Server is ready! You can now run the comprehensive test suite:');
        console.log('   node tests/comprehensive_solana_tracker_test.js');
    } else {
        console.log('\n⚠️  Server needs attention before running comprehensive tests.');
        
        if (!status.serverRunning) {
            console.log('\n🔧 To start the server:');
            console.log('   pnpm start');
        } else if (!status.accessControlFixed) {
            console.log('\n🔧 To restart the server with fixes:');
            console.log('   1. Stop the current server (Ctrl+C)');
            console.log('   2. Start it again: pnpm start');
        }
    }
}

main().catch(error => {
    console.error('❌ Status check failed:', error);
    process.exit(1);
});
