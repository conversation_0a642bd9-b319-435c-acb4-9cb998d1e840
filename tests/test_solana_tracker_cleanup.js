#!/usr/bin/env node

/**
 * Test script to verify SolanaTracker stream cleanup functionality
 * Tests both explicit unsubscription and bulk cleanup on disconnect
 */

import { SolanaTracker } from '../src/workers/solanaTracker.js';
import { config } from 'dotenv';

// Load environment variables
config();

async function testCleanupFunctionality() {
    console.log('🧪 Testing SolanaTracker Stream Cleanup Functionality\n');
    
    const solanaTracker = new SolanaTracker();
    
    try {
        // Initialize the worker
        await solanaTracker.init();
        console.log('✅ SolanaTracker worker initialized\n');
        
        // Test 1: Subscribe to multiple rooms with different subscribers
        console.log('📋 Test 1: Subscribe to multiple rooms');
        const rooms = ['latest', 'graduating', 'graduated'];
        const subscribers = ['user1', 'user2', 'user3'];
        
        for (const room of rooms) {
            for (const subscriber of subscribers) {
                try {
                    const result = await solanaTracker.subscribe(room, subscriber);
                    console.log(`✅ Subscribed ${subscriber} to ${room}: ${result.subscriberCount} total subscribers`);
                } catch (error) {
                    console.error(`❌ Failed to subscribe ${subscriber} to ${room}:`, error.message);
                }
            }
        }
        
        // Wait a moment for connections to establish
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Test 2: Check connection pool status
        console.log('\n📊 Test 2: Connection pool status');
        const stats = solanaTracker.getStats();
        console.log('Active connections:', stats.activeConnections);
        console.log('Total subscribers:', stats.totalSubscribers);
        console.log('Rooms with subscribers:', stats.roomsWithSubscribers);
        
        // Test 3: Individual unsubscription
        console.log('\n🔄 Test 3: Individual unsubscription');
        try {
            const result = await solanaTracker.unsubscribe('latest', 'user1');
            console.log(`✅ Unsubscribed user1 from latest: ${result.subscriberCount} remaining subscribers`);
        } catch (error) {
            console.error('❌ Failed individual unsubscription:', error.message);
        }
        
        // Test 4: Bulk unsubscription (simulating disconnect)
        console.log('\n🧹 Test 4: Bulk unsubscription (disconnect simulation)');
        try {
            const result = await solanaTracker.bulkUnsubscribe('user2');
            console.log(`✅ Bulk unsubscription completed for user2:`);
            console.log(`   - Total rooms processed: ${result.totalRooms}`);
            console.log(`   - Successful cleanups: ${result.results.filter(r => r.success).length}`);
            console.log(`   - Failed cleanups: ${result.results.filter(r => !r.success).length}`);
            
            if (result.results.some(r => !r.success)) {
                console.log('❌ Failed cleanups:');
                result.results.filter(r => !r.success).forEach(r => {
                    console.log(`   - Room ${r.room}: ${r.error}`);
                });
            }
        } catch (error) {
            console.error('❌ Failed bulk unsubscription:', error.message);
        }
        
        // Test 5: Check final status
        console.log('\n📊 Test 5: Final connection pool status');
        const finalStats = solanaTracker.getStats();
        console.log('Active connections:', finalStats.activeConnections);
        console.log('Total subscribers:', finalStats.totalSubscribers);
        console.log('Rooms with subscribers:', finalStats.roomsWithSubscribers);
        
        // Test 6: Cleanup orphaned connections
        console.log('\n🧹 Test 6: Cleanup orphaned connections');
        solanaTracker.cleanupOrphanedConnections();
        
        // Test 7: Verify all connections are properly closed
        console.log('\n🔍 Test 7: Verify connection cleanup');
        const connectionPool = solanaTracker.connectionPool;
        const activeRooms = connectionPool.getActiveRooms();
        console.log(`Active rooms after cleanup: ${activeRooms.length}`);
        
        if (activeRooms.length > 0) {
            console.log('⚠️ Warning: Some rooms still have active connections:');
            activeRooms.forEach(room => {
                const subscriberCount = connectionPool.getSubscriberCount(room);
                console.log(`   - Room ${room}: ${subscriberCount} subscribers`);
            });
        } else {
            console.log('✅ All connections properly cleaned up');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        // Clean shutdown
        console.log('\n🛑 Shutting down SolanaTracker worker');
        solanaTracker.stop();
        console.log('✅ Test completed');
    }
}

// Run the test
testCleanupFunctionality().catch(console.error);
