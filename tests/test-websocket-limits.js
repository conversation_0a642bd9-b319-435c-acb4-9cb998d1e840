#!/usr/bin/env node

/**
 * Test WebSocket Connection Limits
 * Simple test to verify WebSocket connection limits are working
 */

import { WebSocket } from 'ws';

const WS_BASE_URL = 'ws://localhost:3001';

async function testWebSocketLimits() {
    console.log('🧪 Testing WebSocket Connection Limits');
    console.log('=====================================');
    
    const testUsers = [
        {
            name: 'Free',
            apiKey: 'test_free_api_key_12345',
            expectedLimit: 1
        },
        {
            name: 'Basic',
            apiKey: 'test_basic_api_key_12345',
            expectedLimit: 3
        }
    ];
    
    for (const user of testUsers) {
        console.log(`\n🔍 Testing ${user.name} tier (expected limit: ${user.expectedLimit})`);
        
        const connections = [];
        let successCount = 0;
        let rejectedCount = 0;
        
        // Try to open more connections than the limit
        const testConnections = user.expectedLimit + 2;
        
        for (let i = 1; i <= testConnections; i++) {
            try {
                const ws = new WebSocket(`${WS_BASE_URL}/ws?apiKey=${user.apiKey}`);
                
                const result = await new Promise((resolve) => {
                    const timeout = setTimeout(() => {
                        ws.terminate(); // Force close the connection
                        resolve({ success: false, reason: 'timeout' });
                    }, 2000);
                    
                    ws.on('open', () => {
                        clearTimeout(timeout);
                        resolve({ success: true, ws });
                    });
                    
                    ws.on('close', (code, reason) => {
                        clearTimeout(timeout);
                        resolve({ success: false, code, reason: reason.toString() });
                    });
                    
                    ws.on('error', (error) => {
                        clearTimeout(timeout);
                        resolve({ success: false, reason: error.message });
                    });
                });
                
                if (result.success) {
                    connections.push(result.ws);
                    successCount++;
                    console.log(`  ✅ Connection ${i}: Success`);
                } else {
                    rejectedCount++;
                    if (result.code === 1013) {
                        console.log(`  🚫 Connection ${i}: Rejected (limit exceeded) - ${result.reason}`);
                    } else {
                        console.log(`  ❌ Connection ${i}: Failed (${result.code}) - ${result.reason}`);
                    }
                }
                
            } catch (error) {
                rejectedCount++;
                console.log(`  ❌ Connection ${i}: Error - ${error.message}`);
            }
            
            // Small delay between connections
            await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        // Clean up connections
        connections.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        });
        
        // Analyze results
        console.log(`\n📊 Results for ${user.name} tier:`);
        console.log(`   Successful connections: ${successCount}`);
        console.log(`   Rejected connections: ${rejectedCount}`);
        console.log(`   Expected limit: ${user.expectedLimit}`);
        
        if (successCount <= user.expectedLimit && rejectedCount > 0) {
            console.log(`   ✅ Connection limiting appears to be working!`);
        } else if (successCount > user.expectedLimit) {
            console.log(`   ❌ Connection limiting NOT working - too many connections allowed`);
        } else {
            console.log(`   ⚠️ Unclear results - may need investigation`);
        }
        
        // Wait before testing next user
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n🎉 WebSocket connection limit testing completed!');
}

testWebSocketLimits().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
});
