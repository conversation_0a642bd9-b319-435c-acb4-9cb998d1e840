#!/usr/bin/env node

/**
 * Test script to verify tier changes properly sync with rate limiting
 */

import { testConnection, query } from '../src/config/database.js';
import { testRedisConnection, redis } from '../src/config/redis.js';
import { getFreshUserTierLimits } from '../src/middleware/rateLimiter.js';

console.log('🧪 Testing Tier & Rate Limit Synchronization\n');

/**
 * Test that tier changes properly invalidate rate limiting caches
 */
async function testTierRateLimitSync() {
    console.log('🔄 Testing Tier-Rate Limit Synchronization...');
    
    try {
        // Test database connection
        const dbConnected = await testConnection();
        if (!dbConnected) {
            throw new Error('Database connection failed');
        }
        
        // Test Redis connection
        const redisConnected = await testRedisConnection();
        if (!redisConnected) {
            throw new Error('Redis connection failed');
        }
        
        // Get available tiers
        const tiersResult = await query(
            'SELECT id, name, max_requests_per_minute FROM access_tiers WHERE is_enabled = true ORDER BY id LIMIT 3'
        );
        
        if (tiersResult.rows.length < 2) {
            console.log('⚠️  Need at least 2 enabled tiers for testing. Creating test data...');
            
            // Create test tiers if they don't exist
            await query(`
                INSERT INTO access_tiers (name, description, max_requests_per_minute, max_credits_per_month, is_enabled)
                VALUES 
                    ('Test Basic', 'Test basic tier', 100, 1000, true),
                    ('Test Premium', 'Test premium tier', 500, 5000, true)
                ON CONFLICT (name) DO UPDATE SET 
                    max_requests_per_minute = EXCLUDED.max_requests_per_minute,
                    is_enabled = true
            `);
            
            console.log('✅ Test tiers created');
        }
        
        // Get updated tiers
        const updatedTiersResult = await query(
            'SELECT id, name, max_requests_per_minute FROM access_tiers WHERE is_enabled = true ORDER BY id LIMIT 3'
        );
        
        const tiers = updatedTiersResult.rows;
        console.log(`📊 Available tiers: ${tiers.map(t => `${t.name}(${t.max_requests_per_minute})`).join(', ')}`);
        
        // Check if we have a test user
        let testUser = await query(
            'SELECT id, email, tier_id FROM users WHERE email = $1',
            ['<EMAIL>']
        );
        
        if (testUser.rows.length === 0) {
            console.log('👤 Creating test user...');
            
            // Create test user
            const { User } = await import('../src/models/User.js');
            const newUser = await User.create({
                email: '<EMAIL>',
                password: 'TestPassword123',
                tier_id: tiers[0].id
            });
            
            testUser = await query(
                'SELECT id, email, tier_id FROM users WHERE id = $1',
                [newUser.id]
            );
            
            console.log(`✅ Test user created with ID: ${testUser.rows[0].id}`);
        }
        
        const user = testUser.rows[0];
        console.log(`👤 Using test user: ${user.email} (ID: ${user.id})`);
        
        // Test 1: Verify fresh tier limits function
        console.log('\n📋 Test 1: Fresh tier limits retrieval');
        const freshLimits = await getFreshUserTierLimits(user.id);
        if (!freshLimits) {
            throw new Error('Failed to get fresh tier limits');
        }
        console.log(`✅ Fresh limits: ${freshLimits.max_requests_per_minute} requests/min`);
        
        // Test 2: Create rate limit cache entries
        console.log('\n📋 Test 2: Creating rate limit cache entries');
        const rateLimitKey = `rate_limit:user:${user.id}`;
        
        // Simulate rate limit usage
        await redis.setex(rateLimitKey, 60, '5'); // 5 requests used
        const currentCount = await redis.get(rateLimitKey);
        console.log(`✅ Rate limit cache created: ${currentCount} requests used`);
        
        // Test 3: Change user tier
        console.log('\n📋 Test 3: Changing user tier');
        const newTier = tiers.find(t => t.id !== user.tier_id) || tiers[1];
        
        console.log(`🔄 Changing user tier from ${user.tier_id} to ${newTier.id} (${newTier.name})`);
        
        // Update tier using the User model (which should clear caches)
        const { User } = await import('../src/models/User.js');
        const userInstance = await User.findById(user.id);
        if (!userInstance) {
            throw new Error('User not found');
        }
        
        await userInstance.update({ tier_id: newTier.id });
        console.log(`✅ User tier updated to ${newTier.name}`);
        
        // Test 4: Verify cache invalidation
        console.log('\n📋 Test 4: Verifying cache invalidation');
        
        // Check if rate limit cache was cleared
        const rateLimitAfterUpdate = await redis.get(rateLimitKey);
        if (rateLimitAfterUpdate === null) {
            console.log('✅ Rate limit cache properly cleared after tier change');
        } else {
            console.log(`⚠️  Rate limit cache still exists: ${rateLimitAfterUpdate}`);
        }
        
        // Check if user cache was cleared
        const userCacheKey = `user:${user.id}`;
        const userCacheAfterUpdate = await redis.get(userCacheKey);
        if (userCacheAfterUpdate === null) {
            console.log('✅ User cache properly cleared after tier change');
        } else {
            console.log(`⚠️  User cache still exists`);
        }
        
        // Test 5: Verify fresh limits reflect new tier
        console.log('\n📋 Test 5: Verifying fresh limits reflect new tier');
        const newFreshLimits = await getFreshUserTierLimits(user.id);
        if (!newFreshLimits) {
            throw new Error('Failed to get fresh tier limits after update');
        }
        
        if (newFreshLimits.max_requests_per_minute === newTier.max_requests_per_minute) {
            console.log(`✅ Fresh limits updated: ${newFreshLimits.max_requests_per_minute} requests/min`);
        } else {
            throw new Error(`Tier limits not updated: expected ${newTier.max_requests_per_minute}, got ${newFreshLimits.max_requests_per_minute}`);
        }
        
        // Cleanup: Restore original tier
        console.log('\n🧹 Cleanup: Restoring original tier');
        await userInstance.update({ tier_id: user.tier_id });
        console.log('✅ Original tier restored');
        
        return true;
        
    } catch (error) {
        console.error('❌ Tier-Rate Limit sync test failed:', error.message);
        return false;
    }
}

/**
 * Test admin tier change endpoint cache invalidation
 */
async function testAdminTierChangeCache() {
    console.log('\n🔧 Testing Admin Tier Change Cache Invalidation...');
    
    try {
        // This test would require setting up admin authentication
        // For now, we'll just verify the cache clearing function exists
        
        const { clearTierCaches } = await import('../src/routes/admin.js');
        if (typeof clearTierCaches === 'function') {
            console.log('✅ Admin cache clearing function exists');
        } else {
            console.log('ℹ️  Admin cache clearing function not directly accessible (expected)');
        }
        
        // Test the cache clearing pattern
        const testKeys = [
            'user:123',
            'apikey:test123',
            'rate_limit:user:123',
            'admin:test'
        ];
        
        // Set test keys
        for (const key of testKeys) {
            await redis.setex(key, 60, 'test_value');
        }
        
        console.log(`📝 Created ${testKeys.length} test cache keys`);
        
        // Simulate cache clearing (like admin route would do)
        const userKeys = await redis.keys('user:*');
        const apiKeys = await redis.keys('apikey:*');
        const rateLimitKeys = await redis.keys('rate_limit:*');
        const adminKeys = await redis.keys('admin:*');
        
        const allKeys = [...userKeys, ...apiKeys, ...rateLimitKeys, ...adminKeys];
        
        if (allKeys.length > 0) {
            await redis.del(...allKeys);
            console.log(`🧹 Cleared ${allKeys.length} cache keys`);
        }
        
        // Verify keys are gone
        const remainingKeys = await redis.keys('user:*');
        if (remainingKeys.length === 0) {
            console.log('✅ Cache clearing pattern works correctly');
        } else {
            console.log(`⚠️  ${remainingKeys.length} keys still remain`);
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Admin tier change cache test failed:', error.message);
        return false;
    }
}

/**
 * Run all tier synchronization tests
 */
async function runTierSyncTests() {
    const tests = [
        { name: 'Tier-Rate Limit Synchronization', fn: testTierRateLimitSync },
        { name: 'Admin Tier Change Cache Invalidation', fn: testAdminTierChangeCache }
    ];
    
    const results = [];
    
    for (const test of tests) {
        try {
            const result = await test.fn();
            results.push({ name: test.name, passed: result });
        } catch (error) {
            console.error(`❌ Test '${test.name}' threw error:`, error.message);
            results.push({ name: test.name, passed: false });
        }
    }
    
    // Summary
    console.log('\n📊 Tier Sync Test Results:');
    console.log('===========================');
    
    let passedCount = 0;
    for (const result of results) {
        const status = result.passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${result.name}`);
        if (result.passed) passedCount++;
    }
    
    console.log(`\n🎯 Overall: ${passedCount}/${results.length} tests passed`);
    
    if (passedCount === results.length) {
        console.log('🎉 All tier synchronization tests passed!');
        process.exit(0);
    } else {
        console.log('⚠️  Some tests failed. Please review the implementation.');
        process.exit(1);
    }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runTierSyncTests().catch(error => {
        console.error('❌ Tier sync test suite failed:', error);
        process.exit(1);
    });
}
