import { SolanaTracker } from '../src/workers/solanaTracker.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🧪 Testing SolanaTracker Worker (No Historical Storage)\n');

async function testWorkerWithoutHistory() {
    const solanaTracker = new SolanaTracker();
    
    try {
        console.log('🚀 Initializing SolanaTracker worker...');
        await solanaTracker.init();
        console.log('✅ Worker initialized successfully');
        
        // Test getting available room types (should not have historyKey)
        console.log('\n📋 Testing available room types...');
        const roomTypes = solanaTracker.getAvailableRoomTypes();
        console.log('Available room types:');
        roomTypes.forEach(room => {
            console.log(`  - ${room.type}: ${room.displayName} (requires params: ${room.requiresParams})`);
            if (room.historyKey) {
                throw new Error(`Room ${room.type} still has historyKey: ${room.historyKey}`);
            }
        });
        console.log('✅ No historyKey found in room types (correct)');
        
        // Test subscription
        console.log('\n📡 Testing subscription...');
        try {
            const subscribeResult = await solanaTracker.subscribe('latest', 'test-user');
            console.log('✅ Subscription successful:', subscribeResult);
            
            // Test unsubscription
            const unsubscribeResult = await solanaTracker.unsubscribe('latest', 'test-user');
            console.log('✅ Unsubscription successful:', unsubscribeResult);
            
        } catch (error) {
            console.log('⚠️  Subscription test failed (expected if no real API connection):', error.message);
        }
        
        // Test statistics
        console.log('\n📊 Testing worker statistics...');
        const stats = solanaTracker.getStats();
        console.log('Worker stats:', JSON.stringify(stats, null, 2));
        
        console.log('\n🎉 All tests passed! Worker functions correctly without historical storage.');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        throw error;
    } finally {
        console.log('\n🛑 Stopping worker...');
        solanaTracker.stop();
    }
}

// Run the test
testWorkerWithoutHistory().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
});
