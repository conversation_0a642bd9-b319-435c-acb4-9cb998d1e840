import { query, testConnection } from '../src/config/database.js';
import { StreamManager } from '../src/websocket/StreamManager.js';
import dotenv from 'dotenv';

dotenv.config();

console.log('🔐 SolanaTracker Access Control Testing Suite\n');

// Test data
const solanaStreams = [
    'tokens-launched', 'tokens-graduating', 'tokens-graduated',
    'pool-changes', 'token-transactions', 'price-updates', 
    'wallet-transactions', 'token-metadata', 'token-holders', 'token-changes'
];

const testUsers = {
    basic: {
        email: '<EMAIL>',
        tier_id: 2,
        tier_name: 'basic'
    },
    premium: {
        email: '<EMAIL>', 
        tier_id: 3,
        tier_name: 'premium'
    },
    enterprise: {
        email: '<EMAIL>',
        tier_id: 4,
        tier_name: 'enterprise'
    }
};

async function setupTestUsers() {
    console.log('👥 Setting up test users...');
    
    for (const [tierName, userData] of Object.entries(testUsers)) {
        try {
            // Check if user exists
            const existingUser = await query(
                'SELECT id FROM users WHERE email = $1',
                [userData.email]
            );
            
            if (existingUser.rows.length === 0) {
                // Create test user
                await query(`
                    INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining, is_active)
                    VALUES ($1, $2, $3, $4, $5, $6)
                `, [
                    userData.email,
                    'test-hash',
                    `test-api-key-${tierName}`,
                    userData.tier_id,
                    1000000, // Plenty of credits
                    true
                ]);
                console.log(`✅ Created ${tierName} test user: ${userData.email}`);
            } else {
                // Update existing user
                await query(`
                    UPDATE users 
                    SET tier_id = $1, credits_remaining = $2, is_active = $3
                    WHERE email = $4
                `, [userData.tier_id, 1000000, true, userData.email]);
                console.log(`✅ Updated ${tierName} test user: ${userData.email}`);
            }
        } catch (error) {
            console.error(`❌ Failed to setup ${tierName} user:`, error.message);
        }
    }
}

async function testDatabaseAccessControl() {
    console.log('\n🗄️  Testing Database Access Control...');
    
    // Test stream definitions
    console.log('\n📊 Verifying stream tier requirements:');
    const streamTiers = await query(`
        SELECT sd.stream_name, sd.required_tier_id, at.name as tier_name
        FROM stream_definitions sd
        LEFT JOIN access_tiers at ON sd.required_tier_id = at.id
        WHERE sd.stream_name = ANY($1)
        ORDER BY sd.stream_name;
    `, [solanaStreams]);
    
    let correctTierCount = 0;
    streamTiers.rows.forEach(stream => {
        const isCorrect = stream.required_tier_id === 3;
        const status = isCorrect ? '✅' : '❌';
        console.log(`  ${status} ${stream.stream_name}: requires ${stream.tier_name} (tier ${stream.required_tier_id})`);
        if (isCorrect) correctTierCount++;
    });
    
    console.log(`\nResult: ${correctTierCount}/10 streams correctly require Premium tier`);
    
    // Test tier allowed_streams
    console.log('\n🎯 Verifying tier access permissions:');
    const tierAccess = await query(`
        SELECT id, name, allowed_streams
        FROM access_tiers 
        WHERE id IN (2, 3, 4)
        ORDER BY id;
    `);
    
    tierAccess.rows.forEach(tier => {
        const allowedStreams = tier.allowed_streams || [];
        const hasAllStreams = allowedStreams.includes('*');
        const solanaStreamCount = hasAllStreams ? 10 : solanaStreams.filter(s => allowedStreams.includes(s)).length;
        
        let status = '❌';
        if (tier.id === 2 && solanaStreamCount === 0) status = '✅'; // Basic should have 0
        if (tier.id === 3 && solanaStreamCount === 10) status = '✅'; // Premium should have 10
        if (tier.id === 4 && (solanaStreamCount === 10 || hasAllStreams)) status = '✅'; // Enterprise should have all
        
        console.log(`  ${status} ${tier.name} (tier ${tier.id}): ${solanaStreamCount}/10 SolanaTracker streams`);
    });
}

async function testUserStreamAccess() {
    console.log('\n👤 Testing User Stream Access...');
    
    for (const [tierName, userData] of Object.entries(testUsers)) {
        console.log(`\n🔍 Testing ${tierName} user access (${userData.email}):`);
        
        // Get user's allowed streams
        const userAccess = await query(`
            SELECT u.email, u.tier_id, at.name as tier_name, at.allowed_streams
            FROM users u
            JOIN access_tiers at ON u.tier_id = at.id
            WHERE u.email = $1
        `, [userData.email]);
        
        if (userAccess.rows.length === 0) {
            console.log(`  ❌ User not found: ${userData.email}`);
            continue;
        }
        
        const user = userAccess.rows[0];
        const allowedStreams = user.allowed_streams || [];
        const hasAllStreams = allowedStreams.includes('*');
        
        console.log(`  📋 User: ${user.email} (${user.tier_name} tier)`);
        
        // Test each SolanaTracker stream
        let accessibleCount = 0;
        for (const streamName of solanaStreams) {
            const hasAccess = hasAllStreams || allowedStreams.includes(streamName);
            
            // Check if stream requires this user's tier or lower
            const streamRequirement = await query(`
                SELECT required_tier_id FROM stream_definitions WHERE stream_name = $1
            `, [streamName]);
            
            const requiredTier = streamRequirement.rows[0]?.required_tier_id || 999;
            const meetsRequirement = user.tier_id >= requiredTier;
            const shouldHaveAccess = hasAccess && meetsRequirement;
            
            if (shouldHaveAccess) accessibleCount++;
            
            const status = shouldHaveAccess ? '✅' : '❌';
            console.log(`    ${status} ${streamName}: ${hasAccess ? 'allowed' : 'denied'} (requires tier ${requiredTier})`);
        }
        
        // Verify expected access levels
        let expectedAccess = 0;
        if (tierName === 'premium' || tierName === 'enterprise') expectedAccess = 10;
        
        const accessStatus = accessibleCount === expectedAccess ? '✅' : '❌';
        console.log(`  ${accessStatus} Total accessible streams: ${accessibleCount}/10 (expected: ${expectedAccess})`);
    }
}

async function testCreditSystem() {
    console.log('\n💳 Testing Credit System for SolanaTracker Streams...');
    
    // Verify all SolanaTracker streams have 0 credit cost
    const streamCredits = await query(`
        SELECT stream_name, credits_per_message
        FROM stream_definitions
        WHERE stream_name = ANY($1)
        ORDER BY stream_name;
    `, [solanaStreams]);
    
    let freeStreamCount = 0;
    streamCredits.rows.forEach(stream => {
        const isFree = stream.credits_per_message === 0;
        const status = isFree ? '✅' : '❌';
        console.log(`  ${status} ${stream.stream_name}: ${stream.credits_per_message} credits`);
        if (isFree) freeStreamCount++;
    });
    
    console.log(`\nResult: ${freeStreamCount}/10 streams are correctly set to 0 credits`);
    
    // Test credit deduction simulation
    console.log('\n🧮 Simulating credit usage:');
    for (const [tierName, userData] of Object.entries(testUsers)) {
        if (tierName === 'basic') continue; // Basic users can't access these streams
        
        console.log(`  📊 ${tierName} user accessing SolanaTracker streams:`);
        
        // Get user's current credits
        const userCredits = await query(`
            SELECT credits_remaining FROM users WHERE email = $1
        `, [userData.email]);
        
        const currentCredits = userCredits.rows[0]?.credits_remaining || 0;
        console.log(`    💰 Current credits: ${currentCredits}`);
        
        // Simulate accessing each stream (should cost 0 credits)
        let totalCost = 0;
        for (const streamName of solanaStreams.slice(0, 3)) { // Test first 3 streams
            const streamCost = await query(`
                SELECT credits_per_message FROM stream_definitions WHERE stream_name = $1
            `, [streamName]);
            
            const cost = streamCost.rows[0]?.credits_per_message || 0;
            totalCost += cost;
            console.log(`    📡 ${streamName}: ${cost} credits`);
        }
        
        const status = totalCost === 0 ? '✅' : '❌';
        console.log(`    ${status} Total cost for 3 streams: ${totalCost} credits`);
    }
}

async function testStreamManagerIntegration() {
    console.log('\n🔌 Testing StreamManager Integration...');
    
    try {
        // Test that StreamManager recognizes SolanaTracker streams
        const streamManager = new StreamManager();
        
        // Check if streams are registered
        const registeredStreams = ['tokens-launched', 'tokens-graduating', 'tokens-graduated'];
        
        for (const streamName of registeredStreams) {
            const exists = streamManager.streamExists(streamName);
            const status = exists ? '✅' : '❌';
            console.log(`  ${status} ${streamName}: ${exists ? 'registered' : 'not registered'}`);
        }
        
        console.log('✅ StreamManager integration test completed');
        
    } catch (error) {
        console.log('⚠️  StreamManager integration test skipped:', error.message);
    }
}

async function runAccessControlTests() {
    try {
        console.log('🚀 Starting SolanaTracker Access Control Tests...\n');
        
        // Test database connection
        const connected = await testConnection();
        if (!connected) {
            throw new Error('Database connection failed');
        }
        
        // Run all tests
        await setupTestUsers();
        await testDatabaseAccessControl();
        await testUserStreamAccess();
        await testCreditSystem();
        await testStreamManagerIntegration();
        
        console.log('\n🎉 Access Control Testing Completed!');
        console.log('\n📋 Test Summary:');
        console.log('✅ Database tier configuration verified');
        console.log('✅ User access permissions tested');
        console.log('✅ Credit system validation completed');
        console.log('✅ StreamManager integration checked');
        console.log('\n🔐 SolanaTracker streams are properly secured for Premium+ users only');
        
    } catch (error) {
        console.error('❌ Access control testing failed:', error);
        process.exit(1);
    }
}

// Cleanup function
async function cleanup() {
    console.log('\n🧹 Cleaning up test users...');
    
    for (const userData of Object.values(testUsers)) {
        try {
            await query('DELETE FROM users WHERE email = $1', [userData.email]);
            console.log(`✅ Removed test user: ${userData.email}`);
        } catch (error) {
            console.log(`⚠️  Failed to remove test user ${userData.email}:`, error.message);
        }
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    console.log('\n👋 Test interrupted by user');
    await cleanup();
    process.exit(0);
});

// Run the tests
runAccessControlTests().then(async () => {
    await cleanup();
}).catch(async (error) => {
    console.error('❌ Test suite failed:', error);
    await cleanup();
    process.exit(1);
});
