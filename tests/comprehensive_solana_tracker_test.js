import { WebSocket } from 'ws';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

console.log('🧪 Comprehensive SolanaTracker WebSocket Stream Test Suite\n');

// Test configuration
const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG'; // Enterprise demo user
const WS_URL = `ws://localhost:3001/ws?apiKey=${API_KEY}`;

// Test addresses provided by user
const TEST_TOKEN_ADDRESS = 'BuXsYHrq8L3V8taLtq7AE4xsmKT8MhCGSaLjdUoLpump';
const TEST_WALLET_ADDRESS = 'H5KGTyn63J74PXdBsPw6ypLsiCiTPS8JRHSfpBE5MSY6';

// Stream configuration with expected frequency and timeouts
const STREAM_CONFIG = {
    // High-frequency streams (30-60 seconds timeout)
    'tokens-launched': {
        room: 'latest',
        frequency: 'high',
        timeout: 60000,
        description: 'Latest tokens and pools launched on Solana'
    },
    'pool-changes': {
        room: 'pool',
        frequency: 'high', 
        timeout: 60000,
        description: 'Real-time pool liquidity and price changes'
    },
    'price-updates': {
        room: 'price',
        frequency: 'high',
        timeout: 60000,
        description: 'Token price updates and market data'
    },
    
    // Medium-frequency streams (2-3 minutes timeout)
    'tokens-graduating': {
        room: 'graduating',
        frequency: 'medium',
        timeout: 180000,
        description: 'Tokens approaching graduation threshold'
    },
    'tokens-graduating-175': {
        room: 'graduating:sol:175',
        frequency: 'medium',
        timeout: 180000,
        description: 'Tokens approaching graduation with 175 SOL market cap threshold',
        parameters: { marketCapThreshold: 175 }
    },
    'token-transactions': {
        room: 'transaction',
        frequency: 'medium',
        timeout: 180000,
        description: 'Real-time token transaction data'
    },
    
    // Low-frequency streams (5 minutes timeout)
    'tokens-graduated': {
        room: 'graduated',
        frequency: 'low',
        timeout: 300000,
        description: 'Tokens that have graduated from bonding curve'
    },
    'wallet-transactions': {
        room: 'wallet',
        frequency: 'low',
        timeout: 300000,
        description: 'Wallet-specific transaction monitoring',
        parameters: { walletAddress: TEST_WALLET_ADDRESS }
    },
    'token-metadata': {
        room: 'metadata',
        frequency: 'low',
        timeout: 300000,
        description: 'Token metadata updates and changes',
        parameters: { tokenAddress: TEST_TOKEN_ADDRESS }
    },
    'token-holders': {
        room: 'holders',
        frequency: 'low',
        timeout: 300000,
        description: 'Token holder distribution and changes',
        parameters: { tokenAddress: TEST_TOKEN_ADDRESS }
    },
    'token-changes': {
        room: 'token',
        frequency: 'low',
        timeout: 300000,
        description: 'General token state and property changes',
        parameters: { tokenAddress: TEST_TOKEN_ADDRESS }
    }
};

// Test results storage
const testResults = {
    summary: {
        total: Object.keys(STREAM_CONFIG).length,
        successful: 0,
        failed: 0,
        timedOut: 0,
        startTime: new Date().toISOString()
    },
    streams: {},
    errors: [],
    recommendations: []
};

// Utility function to format timeout duration
function formatTimeout(ms) {
    if (ms < 60000) return `${ms / 1000}s`;
    return `${ms / 60000}m`;
}

// Test individual stream
async function testStream(streamName, config) {
    return new Promise((resolve) => {
        console.log(`\n🔍 Testing ${streamName} stream:`);
        console.log(`   📋 Description: ${config.description}`);
        console.log(`   🎯 SolanaTracker room: ${config.room}`);
        console.log(`   ⏱️  Frequency: ${config.frequency} (timeout: ${formatTimeout(config.timeout)})`);
        
        const startTime = Date.now();
        let subscriptionConfirmed = false;
        let messageReceived = false;
        let firstMessage = null;
        let connectionId = null;
        
        const ws = new WebSocket(WS_URL);
        
        const timer = setTimeout(() => {
            console.log(`   ⏰ Timeout reached for ${streamName}`);
            ws.close();
            
            const result = {
                status: 'timeout',
                subscriptionConfirmed,
                messageReceived,
                duration: Date.now() - startTime,
                error: `No messages received within ${formatTimeout(config.timeout)} timeout`,
                connectionId
            };
            
            testResults.streams[streamName] = result;
            testResults.summary.timedOut++;
            resolve(result);
        }, config.timeout);
        
        ws.on('open', () => {
            console.log(`   ✅ WebSocket connected for ${streamName}`);
            
            // Wait a moment for connection to stabilize
            setTimeout(() => {
                const subscribeMessage = {
                    type: 'subscribe',
                    payload: {
                        stream: streamName,
                        ...(config.parameters && config.parameters)
                    }
                };

                console.log(`   📡 Subscribing to ${streamName}...`);
                if (config.parameters) {
                    console.log(`   🔧 Parameters:`, config.parameters);
                }
                ws.send(JSON.stringify(subscribeMessage));
            }, 1000);
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                
                if (message.type === 'connected') {
                    connectionId = message.connectionId;
                    console.log(`   🔗 Connection established (ID: ${connectionId})`);
                } else if (message.type === 'subscribed') {
                    subscriptionConfirmed = true;
                    console.log(`   ✅ Subscription confirmed for ${streamName}`);
                    console.log(`   ⏳ Waiting for stream data...`);
                } else if (message.type === 'stream_data' && message.stream === streamName) {
                    messageReceived = true;
                    firstMessage = message.data;
                    
                    console.log(`   🎉 SUCCESS! Received data for ${streamName}`);
                    console.log(`   📨 Sample data:`, JSON.stringify(message.data, null, 2).substring(0, 200) + '...');
                    
                    clearTimeout(timer);
                    ws.close();
                    
                    const result = {
                        status: 'success',
                        subscriptionConfirmed,
                        messageReceived,
                        duration: Date.now() - startTime,
                        sampleData: firstMessage,
                        connectionId
                    };
                    
                    testResults.streams[streamName] = result;
                    testResults.summary.successful++;
                    resolve(result);
                } else if (message.type === 'error') {
                    console.log(`   ❌ ERROR: ${message.error}`);
                    
                    clearTimeout(timer);
                    ws.close();
                    
                    const result = {
                        status: 'error',
                        subscriptionConfirmed,
                        messageReceived,
                        duration: Date.now() - startTime,
                        error: message.error,
                        connectionId
                    };
                    
                    testResults.streams[streamName] = result;
                    testResults.summary.failed++;
                    testResults.errors.push(`${streamName}: ${message.error}`);
                    resolve(result);
                }
            } catch (error) {
                console.error(`   ❌ Error parsing message for ${streamName}:`, error);
            }
        });
        
        ws.on('error', (error) => {
            console.error(`   ❌ WebSocket error for ${streamName}:`, error.message);
            
            clearTimeout(timer);
            
            const result = {
                status: 'connection_error',
                subscriptionConfirmed,
                messageReceived,
                duration: Date.now() - startTime,
                error: error.message,
                connectionId
            };
            
            testResults.streams[streamName] = result;
            testResults.summary.failed++;
            testResults.errors.push(`${streamName}: Connection error - ${error.message}`);
            resolve(result);
        });
        
        ws.on('close', (code, reason) => {
            console.log(`   🔌 WebSocket closed for ${streamName} (code: ${code}, reason: ${reason || 'none'})`);
        });
    });
}

// Main test execution
async function runComprehensiveTest() {
    console.log('🚀 Starting comprehensive SolanaTracker stream testing...');
    console.log(`📊 Testing ${testResults.summary.total} streams with Enterprise API key`);
    console.log('⏱️  Timeout strategy: High-freq (60s), Medium-freq (3m), Low-freq (5m)\n');
    
    // Test streams sequentially to avoid overwhelming the system
    for (const [streamName, config] of Object.entries(STREAM_CONFIG)) {
        await testStream(streamName, config);
        
        // Brief pause between tests
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Generate final report
    generateReport();
}

// Generate comprehensive test report
function generateReport() {
    console.log('\n' + '='.repeat(80));
    console.log('📋 COMPREHENSIVE SOLANATRACKER STREAM TEST REPORT');
    console.log('='.repeat(80));
    
    testResults.summary.endTime = new Date().toISOString();
    testResults.summary.totalDuration = Date.now() - new Date(testResults.summary.startTime).getTime();
    
    // Summary statistics
    console.log('\n📊 SUMMARY STATISTICS:');
    console.log(`   Total streams tested: ${testResults.summary.total}`);
    console.log(`   ✅ Successful: ${testResults.summary.successful}`);
    console.log(`   ❌ Failed: ${testResults.summary.failed}`);
    console.log(`   ⏰ Timed out: ${testResults.summary.timedOut}`);
    console.log(`   🕐 Total test duration: ${formatTimeout(testResults.summary.totalDuration)}`);
    console.log(`   📈 Success rate: ${((testResults.summary.successful / testResults.summary.total) * 100).toFixed(1)}%`);
    
    // Detailed results by frequency category
    const categories = {
        high: [],
        medium: [],
        low: []
    };
    
    Object.entries(testResults.streams).forEach(([streamName, result]) => {
        const config = STREAM_CONFIG[streamName];
        categories[config.frequency].push({ streamName, result, config });
    });
    
    ['high', 'medium', 'low'].forEach(frequency => {
        const streams = categories[frequency];
        if (streams.length === 0) return;
        
        console.log(`\n🎯 ${frequency.toUpperCase()}-FREQUENCY STREAMS:`);
        streams.forEach(({ streamName, result, config }) => {
            const statusIcon = result.status === 'success' ? '✅' : 
                             result.status === 'timeout' ? '⏰' : '❌';
            const duration = formatTimeout(result.duration);
            
            console.log(`   ${statusIcon} ${streamName} (${config.room}) - ${result.status} (${duration})`);
            
            if (result.status === 'success') {
                console.log(`      📨 Received real-time data successfully`);
            } else if (result.error) {
                console.log(`      ❌ Error: ${result.error}`);
            }
        });
    });
    
    // Error analysis
    if (testResults.errors.length > 0) {
        console.log('\n❌ ERRORS ENCOUNTERED:');
        testResults.errors.forEach(error => {
            console.log(`   • ${error}`);
        });
    }
    
    // Generate recommendations
    generateRecommendations();
    
    if (testResults.recommendations.length > 0) {
        console.log('\n💡 RECOMMENDATIONS:');
        testResults.recommendations.forEach(rec => {
            console.log(`   • ${rec}`);
        });
    }
    
    // Save detailed results to file
    saveResultsToFile();
    
    console.log('\n' + '='.repeat(80));
    console.log('🎉 Test suite completed!');
    console.log('='.repeat(80));
}

// Generate recommendations based on test results
function generateRecommendations() {
    const { successful, failed, timedOut, total } = testResults.summary;
    
    if (successful === total) {
        testResults.recommendations.push('🎉 All streams are working perfectly! No action needed.');
    } else {
        if (failed > 0) {
            testResults.recommendations.push('🔧 Some streams have access or connection errors. Check WebSocket access control and server logs.');
        }
        
        if (timedOut > 0) {
            testResults.recommendations.push('⏰ Some streams timed out. This may be normal for low-frequency streams or indicate SolanaTracker connectivity issues.');
        }
        
        if (failed > total * 0.5) {
            testResults.recommendations.push('🚨 More than 50% of streams failed. Consider restarting the server to apply recent WebSocket fixes.');
        }
        
        if (timedOut === total) {
            testResults.recommendations.push('🔍 All streams timed out. Check SolanaTracker API connectivity and worker subscription logic.');
        }
    }
}

// Save results to JSON file
function saveResultsToFile() {
    const resultsDir = path.join(process.cwd(), 'test-results');
    if (!fs.existsSync(resultsDir)) {
        fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `solana-tracker-test-${timestamp}.json`;
    const filepath = path.join(resultsDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(testResults, null, 2));
    console.log(`\n💾 Detailed results saved to: ${filepath}`);
}

// Run the test suite
runComprehensiveTest().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
});
