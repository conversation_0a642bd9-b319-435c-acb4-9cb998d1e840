{"type": "message", "data": {"token": {"name": "<PERSON><PERSON>", "symbol": "<PERSON><PERSON>", "mint": "YQP7uqUzDp4RYG8Ni5jraxogMdEecH11NQr9sk2pump", "uri": "https://ipfs.io/ipfs/bafkreihm5oa75mru6dutgv3ihesgpndnqosxepzampd6onarjkcgacj6tm", "decimals": 6, "hasFileMetaData": true, "createdOn": "https://pump.fun"}, "pools": [{"liquidity": {"quote": 60, "usd": 9242.************}, "price": {"quote": 2.7958993476234855e-08, "usd": 4.306************e-06}, "tokenSupply": 1000000000, "lpBurn": 100, "tokenAddress": "YQP7uqUzDp4RYG8Ni5jraxogMdEecH11NQr9sk2pump", "marketCap": {"quote": 27.958993476234856, "usd": 4306.************}, "decimals": 6, "security": {"freezeAuthority": null, "mintAuthority": null}, "quoteToken": "So11111111111111111111111111111111111111112", "market": "pumpfun", "curvePercentage": 0, "curve": "JCm7goov16ZAozPEQyeueYZ1wgPW1XitkmZXX4LrBdVS", "deployer": "o2z56iaxvUoDa4pkiCszpJE2TCJq1Ux9ZsZva4zG23q", "openTime": 0, "createdAt": 1749085580965, "poolId": "YQP7uqUzDp4RYG8Ni5jraxogMdEecH11NQr9sk2pump"}], "events": {"30m": {"priceChangePercentage": 0}, "1h": {"priceChangePercentage": 0}, "4h": {"priceChangePercentage": 0}, "24h": {"priceChangePercentage": 0}}, "risk": {"rugged": false, "risks": [{"name": "Bonding curve not complete", "description": "No PumpSwap liquidity pool, bonding curve not complete", "level": "warning", "score": 4000}], "score": 3}}, "room": "latest"}