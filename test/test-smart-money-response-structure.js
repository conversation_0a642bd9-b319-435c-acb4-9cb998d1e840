import fetch from 'node-fetch';

// Test configuration
const BASE_URL = 'http://localhost:3000/api/v1';
const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG'; // Enterprise user

// Expected fields for Smart Money token responses
const EXPECTED_TOKEN_FIELDS = [
    'date',
    'token_address', 
    'buy_volume',
    'sell_volume',
    'net_volume',
    'rank_type',
    'rank',
    'top_buyer',
    'top_seller',
    'volume_change',
    'rank_change',
    'symbol',
    'name',
    'decimals',
    'logo'
];

// Expected fields for Smart Money flow responses
const EXPECTED_FLOW_FIELDS = [
    'date',
    'buy_volume',
    'sell_volume',
    'net_volume'
];

async function testEndpointStructure(endpoint, expectedFields, endpointName) {
    try {
        console.log(`\n🔍 Testing ${endpointName} response structure...`);
        
        const response = await fetch(`${BASE_URL}${endpoint}`, {
            method: 'GET',
            headers: {
                'X-API-Key': API_KEY,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            console.log(`  ❌ HTTP Error: ${response.status}`);
            const errorData = await response.json();
            console.log(`  🚫 Error: ${errorData.error || 'Unknown error'}`);
            return false;
        }
        
        const data = await response.json();
        
        if (!data.success) {
            console.log(`  ❌ API Error: ${data.error || 'Unknown error'}`);
            return false;
        }
        
        if (!data.data || !Array.isArray(data.data) || data.data.length === 0) {
            console.log(`  ⚠️  No data returned (this might be expected if cache is empty)`);
            return true; // Not an error, just no data
        }
        
        console.log(`  ✅ Success - ${data.data.length} items returned`);
        
        // Test first item structure
        const firstItem = data.data[0];
        console.log(`  📊 Testing structure of first item...`);
        
        let missingFields = [];
        let presentFields = [];
        
        for (const field of expectedFields) {
            if (firstItem.hasOwnProperty(field)) {
                presentFields.push(field);
            } else {
                missingFields.push(field);
            }
        }
        
        console.log(`  ✅ Present fields (${presentFields.length}): ${presentFields.join(', ')}`);
        
        if (missingFields.length > 0) {
            console.log(`  ❌ Missing fields (${missingFields.length}): ${missingFields.join(', ')}`);
        }
        
        // Show sample of actual structure
        console.log(`  📋 Sample item structure:`);
        console.log(`     - date: ${firstItem.date}`);
        console.log(`     - token_address: ${firstItem.token_address || 'N/A'}`);
        console.log(`     - rank: ${firstItem.rank || 'N/A'}`);
        console.log(`     - rank_type: ${firstItem.rank_type || 'N/A'}`);
        
        if (firstItem.top_buyer) {
            console.log(`     - top_buyer: ${firstItem.top_buyer.wallet} (${firstItem.top_buyer.amount})`);
        } else {
            console.log(`     - top_buyer: null`);
        }
        
        if (firstItem.top_seller) {
            console.log(`     - top_seller: ${firstItem.top_seller.wallet} (${firstItem.top_seller.amount})`);
        } else {
            console.log(`     - top_seller: null`);
        }
        
        return missingFields.length === 0;
        
    } catch (error) {
        console.log(`  💥 Request failed: ${error.message}`);
        return false;
    }
}

async function testAllStructures() {
    console.log('🚀 Testing Smart Money API Response Structures\n');
    console.log(`📡 Base URL: ${BASE_URL}`);
    console.log(`🔑 API Key: ${API_KEY.substring(0, 10)}...`);
    
    const tests = [
        {
            endpoint: '/smart-money/daily-trends/most-bought-tokens',
            expectedFields: EXPECTED_TOKEN_FIELDS,
            name: 'Most Bought Tokens'
        },
        {
            endpoint: '/smart-money/daily-trends/most-sold-tokens', 
            expectedFields: EXPECTED_TOKEN_FIELDS,
            name: 'Most Sold Tokens'
        },
        {
            endpoint: '/smart-money/daily-trends/daily-flows-sol',
            expectedFields: EXPECTED_FLOW_FIELDS,
            name: 'Daily SOL Flows'
        },
        {
            endpoint: '/smart-money/daily-trends/daily-flows-meme',
            expectedFields: EXPECTED_FLOW_FIELDS,
            name: 'Daily Meme Flows'
        }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        const passed = await testEndpointStructure(test.endpoint, test.expectedFields, test.name);
        if (passed) passedTests++;
    }
    
    console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All response structures match the documentation!');
    } else {
        console.log('⚠️  Some response structures need attention.');
        console.log('\n💡 Notes:');
        console.log('   - If endpoints return no data, ensure SmartMoney worker is running');
        console.log('   - Check Redis cache keys: smart_money:daily_trends:*');
        console.log('   - Verify worker has populated data in the last hour');
    }
}

// Run the tests
testAllStructures().catch(console.error);
