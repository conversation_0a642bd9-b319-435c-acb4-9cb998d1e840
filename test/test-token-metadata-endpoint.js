import axios from 'axios';

// Test configuration
const API_BASE_URL = 'http://localhost:3001/api/v1';
const TEST_API_KEY = 'sk_test_1234567890abcdef'; // Replace with actual test API key

// Test token addresses
const TEST_TOKENS = [
    '4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump', // DADDY TATE
    'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm'  // Another test token
];

async function testTokenMetadataEndpoint() {
    console.log('🧪 Testing Token Metadata API Endpoint\n');

    try {
        // Test 1: Single token request
        console.log('1️⃣ Testing single token request...');
        const singleTokenResponse = await axios.post(
            `${API_BASE_URL}/core/token-metadata`,
            {
                tokenAddress: TEST_TOKENS[0]
            },
            {
                headers: {
                    'Authorization': `Bearer ${TEST_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('✅ Single token request successful');
        console.log('📊 Response status:', singleTokenResponse.status);
        console.log('📝 Token data:', singleTokenResponse.data.data[0]);
        console.log('💳 Credits consumed:', singleTokenResponse.data.credits_consumed);
        console.log('');

        // Test 2: Multiple tokens request
        console.log('2️⃣ Testing multiple tokens request...');
        const multipleTokensResponse = await axios.post(
            `${API_BASE_URL}/core/token-metadata`,
            {
                tokenAddress: TEST_TOKENS
            },
            {
                headers: {
                    'Authorization': `Bearer ${TEST_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('✅ Multiple tokens request successful');
        console.log('📊 Response status:', multipleTokensResponse.status);
        console.log('📝 Number of tokens returned:', multipleTokensResponse.data.data.length);
        console.log('💳 Credits consumed:', multipleTokensResponse.data.credits_consumed);
        console.log('');

        // Test 3: Cached request (should be faster)
        console.log('3️⃣ Testing cached request (should be faster)...');
        const startTime = Date.now();
        const cachedResponse = await axios.post(
            `${API_BASE_URL}/core/token-metadata`,
            {
                tokenAddress: TEST_TOKENS
            },
            {
                headers: {
                    'Authorization': `Bearer ${TEST_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        const duration = Date.now() - startTime;

        console.log('✅ Cached request successful');
        console.log('📊 Response status:', cachedResponse.status);
        console.log('⚡ Response time:', duration + 'ms');
        console.log('📝 Cache info:', cachedResponse.data.cache_info);
        console.log('');

        // Test 4: Invalid token address
        console.log('4️⃣ Testing invalid token address...');
        try {
            await axios.post(
                `${API_BASE_URL}/core/token-metadata`,
                {
                    tokenAddress: 'invalid_token_address'
                },
                {
                    headers: {
                        'Authorization': `Bearer ${TEST_API_KEY}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
        } catch (error) {
            console.log('✅ Invalid token address properly handled');
            console.log('📊 Error status:', error.response?.status);
            console.log('📝 Error message:', error.response?.data?.error);
        }
        console.log('');

        console.log('🎉 All Token Metadata Endpoint Tests Completed Successfully!');

    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        if (error.response) {
            console.error('📊 Response status:', error.response.status);
            console.error('📝 Response data:', error.response.data);
        }
    }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
    testTokenMetadataEndpoint()
        .then(() => {
            console.log('\n✅ Endpoint test completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n❌ Endpoint test failed:', error);
            process.exit(1);
        });
}

export { testTokenMetadataEndpoint };
