import { cache } from '../src/config/redis.js';
import { query } from '../src/config/database.js';

/**
 * Test Cache Clearing Functionality
 * 
 * This test verifies that the cache clearing script works correctly
 * by simulating cached user data and then clearing it.
 */

async function testCacheClearing() {
    try {
        console.log('🧪 Testing cache clearing functionality...\n');
        
        // Step 1: Get a test user from database
        const userResult = await query(
            'SELECT email, api_key FROM users WHERE is_active = true LIMIT 1'
        );
        
        if (userResult.rows.length === 0) {
            console.log('❌ No active users found for testing');
            return;
        }
        
        const testUser = userResult.rows[0];
        const cacheKey = `apikey:${testUser.api_key}`;
        
        console.log(`👤 Using test user: ${testUser.email}`);
        console.log(`🔑 API Key: ${testUser.api_key.substring(0, 10)}...`);
        console.log(`🗂️  Cache Key: ${cacheKey}\n`);
        
        // Step 2: Create some test cache data
        const testCacheData = {
            id: 'test-user-id',
            email: testUser.email,
            api_key: testUser.api_key,
            tier_name: 'basic',
            allowed_endpoints: ['/api/v1/demo', '/api/v1/data'],
            cached_at: new Date().toISOString()
        };
        
        console.log('📝 Setting test cache data...');
        await cache.setex(cacheKey, 3600, JSON.stringify(testCacheData));
        
        // Step 3: Verify cache was set
        const cachedData = await cache.get(cacheKey);
        if (cachedData) {
            console.log('✅ Cache data set successfully');
            console.log(`📊 Cached data: ${JSON.stringify(JSON.parse(cachedData), null, 2)}`);
        } else {
            console.log('❌ Failed to set cache data');
            return;
        }
        
        console.log('\n🧹 Testing cache clearing...');
        
        // Step 4: Clear the cache using our script logic
        const deleted = await cache.del(cacheKey);
        
        if (deleted > 0) {
            console.log('✅ Cache cleared successfully');
            console.log(`🗑️  Deleted ${deleted} cache key(s)`);
        } else {
            console.log('❌ Failed to clear cache');
            return;
        }
        
        // Step 5: Verify cache was cleared
        const clearedData = await cache.get(cacheKey);
        if (clearedData === null) {
            console.log('✅ Cache verification: Data successfully removed');
        } else {
            console.log('❌ Cache verification: Data still exists');
            return;
        }
        
        console.log('\n🎉 Cache clearing test completed successfully!');
        console.log('\n💡 Usage examples:');
        console.log('  pnpm clear-cache                    # Clear all user caches');
        console.log(`  pnpm clear-cache ${testUser.email}   # Clear this user by email`);
        console.log(`  pnpm clear-cache ${testUser.api_key.substring(0, 20)}...  # Clear this user by API key`);
        
    } catch (error) {
        console.error('❌ Cache clearing test failed:', error);
        process.exit(1);
    }
}

async function testCacheStats() {
    try {
        console.log('\n📊 Current cache statistics:');
        
        // Count user cache keys
        const userCacheKeys = await cache.keys('apikey:*');
        console.log(`👥 Active user caches: ${userCacheKeys.length}`);
        
        if (userCacheKeys.length > 0) {
            console.log('📋 Sample cache keys:');
            userCacheKeys.slice(0, 3).forEach(key => {
                console.log(`  - ${key}`);
            });
            if (userCacheKeys.length > 3) {
                console.log(`  ... and ${userCacheKeys.length - 3} more`);
            }
        }
        
        // Get Redis memory info
        const info = await cache.info('memory');
        const memoryLines = info.split('\r\n').filter(line => 
            line.includes('used_memory_human') || 
            line.includes('keyspace')
        );
        
        memoryLines.forEach(line => {
            if (line.trim()) {
                console.log(`📈 ${line}`);
            }
        });
        
    } catch (error) {
        console.log('⚠️  Could not retrieve cache stats:', error.message);
    }
}

// Main execution
async function runTests() {
    await testCacheStats();
    await testCacheClearing();
    process.exit(0);
}

runTests().catch(console.error);
