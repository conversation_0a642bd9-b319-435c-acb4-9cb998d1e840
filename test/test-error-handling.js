import axios from 'axios';

// Test configuration
const API_BASE_URL = 'http://localhost:3001';
const TEST_API_KEY = 'sk_test_1234567890abcdef'; // Replace with actual test API key

async function testErrorHandling() {
    console.log('🧪 Testing Enhanced Error Handling Middleware\n');

    const tests = [
        {
            name: 'Invalid JSON - Malformed JSON',
            test: async () => {
                try {
                    await axios.post(`${API_BASE_URL}/api/v1/core/token-metadata`, 
                        '{"tokenAddress": ["invalid",]}', // Invalid trailing comma
                        {
                            headers: {
                                'Authorization': `Bearer ${TEST_API_KEY}`,
                                'Content-Type': 'application/json'
                            }
                        }
                    );
                } catch (error) {
                    return error.response;
                }
            }
        },
        {
            name: 'Invalid JSON - Unexpected token',
            test: async () => {
                try {
                    await axios.post(`${API_BASE_URL}/api/v1/core/token-metadata`, 
                        '{"tokenAddress": [}', // Missing closing bracket
                        {
                            headers: {
                                'Authorization': `Bearer ${TEST_API_KEY}`,
                                'Content-Type': 'application/json'
                            }
                        }
                    );
                } catch (error) {
                    return error.response;
                }
            }
        },
        {
            name: 'Invalid JSON - Incomplete JSON',
            test: async () => {
                try {
                    await axios.post(`${API_BASE_URL}/api/v1/core/token-metadata`, 
                        '{"tokenAddress":', // Incomplete JSON
                        {
                            headers: {
                                'Authorization': `Bearer ${TEST_API_KEY}`,
                                'Content-Type': 'application/json'
                            }
                        }
                    );
                } catch (error) {
                    return error.response;
                }
            }
        },
        {
            name: 'Invalid Authentication',
            test: async () => {
                try {
                    await axios.post(`${API_BASE_URL}/api/v1/core/token-metadata`, 
                        { tokenAddress: ['test'] },
                        {
                            headers: {
                                'Authorization': 'Bearer invalid_token',
                                'Content-Type': 'application/json'
                            }
                        }
                    );
                } catch (error) {
                    return error.response;
                }
            }
        },
        {
            name: 'Missing Authentication',
            test: async () => {
                try {
                    await axios.post(`${API_BASE_URL}/api/v1/core/token-metadata`, 
                        { tokenAddress: ['test'] },
                        {
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        }
                    );
                } catch (error) {
                    return error.response;
                }
            }
        },
        {
            name: '404 Not Found',
            test: async () => {
                try {
                    await axios.get(`${API_BASE_URL}/api/v1/nonexistent-endpoint`);
                } catch (error) {
                    return error.response;
                }
            }
        },
        {
            name: 'Method Not Allowed',
            test: async () => {
                try {
                    await axios.delete(`${API_BASE_URL}/api/v1/core/token-metadata`);
                } catch (error) {
                    return error.response;
                }
            }
        }
    ];

    let passedTests = 0;
    let totalTests = tests.length;

    for (const { name, test } of tests) {
        console.log(`🔍 Testing: ${name}`);
        
        try {
            const response = await test();
            
            if (response && response.data) {
                const { data, status } = response;
                
                console.log(`   📊 Status: ${status}`);
                console.log(`   📝 Error: ${data.error}`);
                console.log(`   🔒 Code: ${data.code || 'N/A'}`);
                console.log(`   💡 Hint: ${data.hint || 'N/A'}`);
                
                // Check if response is properly sanitized
                const responseStr = JSON.stringify(data);
                const isSanitized = !responseStr.includes('node_modules') &&
                                   !responseStr.includes('/Users/') &&
                                   !responseStr.includes('/home/') &&
                                   !responseStr.includes('C:\\') &&
                                   !responseStr.includes(' at ') &&
                                   !responseStr.includes('SyntaxError:') &&
                                   !responseStr.includes('TypeError:') &&
                                   !responseStr.includes('ReferenceError:') &&
                                   !data.stack;
                
                if (isSanitized) {
                    console.log(`   ✅ Response properly sanitized`);
                    passedTests++;
                } else {
                    console.log(`   ❌ Response contains sensitive information`);
                    console.log(`   🔍 Full response:`, JSON.stringify(data, null, 2));
                }
            } else {
                console.log(`   ❌ No response received`);
            }
            
        } catch (error) {
            console.log(`   ❌ Test failed: ${error.message}`);
        }
        
        console.log('');
    }

    // Summary
    console.log('📊 ERROR HANDLING TEST SUMMARY:');
    console.log(`✅ Passed: ${passedTests}/${totalTests}`);
    console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
    console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    console.log('');

    if (passedTests === totalTests) {
        console.log('🎉 All error handling tests passed! Responses are properly sanitized.');
    } else {
        console.log('⚠️  Some tests failed. Please review the error handling implementation.');
    }

    // Test specific scenarios
    console.log('🔍 ADDITIONAL SECURITY CHECKS:');
    
    // Test for stack trace exposure
    console.log('1️⃣ Checking for stack trace exposure...');
    try {
        const response = await axios.post(`${API_BASE_URL}/api/v1/core/token-metadata`,
            '{"invalid": json}',
            {
                headers: {
                    'Authorization': `Bearer ${TEST_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );
    } catch (error) {
        const responseStr = JSON.stringify(error.response?.data);
        const hasStackTrace = responseStr.includes(' at ') ||
                             responseStr.includes('node_modules') ||
                             responseStr.includes('SyntaxError: Unexpected') ||
                             responseStr.includes('TypeError:') ||
                             error.response?.data?.stack;

        if (hasStackTrace) {
            console.log('   ❌ Stack trace exposed in error response');
            console.log('   🔍 Response:', responseStr);
        } else {
            console.log('   ✅ No stack trace exposure detected');
        }
    }

    // Test for file path exposure
    console.log('2️⃣ Checking for file path exposure...');
    try {
        const response = await axios.post(`${API_BASE_URL}/api/v1/core/token-metadata`, 
            'invalid json',
            {
                headers: {
                    'Authorization': `Bearer ${TEST_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );
    } catch (error) {
        const hasFilePaths = JSON.stringify(error.response?.data).includes('/Users/') ||
                            JSON.stringify(error.response?.data).includes('/home/') ||
                            JSON.stringify(error.response?.data).includes('C:\\');
        
        if (hasFilePaths) {
            console.log('   ❌ File paths exposed in error response');
        } else {
            console.log('   ✅ No file path exposure detected');
        }
    }

    console.log('\n🔒 Error handling security assessment complete!');
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
    testErrorHandling()
        .then(() => {
            console.log('\n✅ Error handling test completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n❌ Error handling test failed:', error);
            process.exit(1);
        });
}

export { testErrorHandling };
