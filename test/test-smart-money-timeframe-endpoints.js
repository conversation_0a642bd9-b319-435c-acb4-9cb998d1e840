import fetch from 'node-fetch';

// Test configuration
const BASE_URL = 'http://localhost:3000/api/v1';
const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG'; // Enterprise user

// New timeframe-based Smart Money endpoints
const TIMEFRAME_ENDPOINTS = [
    {
        path: '/smart-money/top-tokens/24h',
        name: 'Top Tokens 24h',
        expectedRankType: 'top'
    },
    {
        path: '/smart-money/top-tokens/3d',
        name: 'Top Tokens 3d',
        expectedRankType: 'top'
    },
    {
        path: '/smart-money/top-tokens/7d',
        name: 'Top Tokens 7d',
        expectedRankType: 'top'
    },
    {
        path: '/smart-money/bottom-tokens/24h',
        name: 'Bottom Tokens 24h',
        expectedRankType: 'bottom'
    },
    {
        path: '/smart-money/bottom-tokens/3d',
        name: 'Bottom Tokens 3d',
        expectedRankType: 'bottom'
    },
    {
        path: '/smart-money/bottom-tokens/7d',
        name: 'Bottom Tokens 7d',
        expectedRankType: 'bottom'
    }
];

// Expected fields for timeframe token responses
const EXPECTED_TIMEFRAME_FIELDS = [
    'timeframe',
    'token_address',
    'buy_volume',
    'sell_volume',
    'net_volume',
    'rank_type',
    'rank',
    'biggest_buyer',
    'biggest_seller',
    'symbol',
    'name',
    'decimals',
    'logo'
];

async function testTimeframeEndpoint(endpoint) {
    try {
        console.log(`\n🔍 Testing ${endpoint.name}...`);
        
        const response = await fetch(`${BASE_URL}${endpoint.path}`, {
            method: 'GET',
            headers: {
                'X-API-Key': API_KEY,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            console.log(`  ❌ HTTP Error: ${response.status}`);
            const errorData = await response.json();
            console.log(`  🚫 Error: ${errorData.error || 'Unknown error'}`);
            return false;
        }
        
        const data = await response.json();
        
        if (!data.success) {
            console.log(`  ❌ API Error: ${data.error || 'Unknown error'}`);
            return false;
        }
        
        if (!data.data || !Array.isArray(data.data) || data.data.length === 0) {
            console.log(`  ⚠️  No data returned (cache might be empty)`);
            return true; // Not an error, just no data
        }
        
        console.log(`  ✅ Success - ${data.data.length} items returned`);
        console.log(`  💰 Credits consumed: ${data.credits_consumed}`);
        
        // Test first item structure
        const firstItem = data.data[0];
        console.log(`  📊 Testing structure of first item...`);
        
        // Check required fields
        let missingFields = [];
        let presentFields = [];
        
        for (const field of EXPECTED_TIMEFRAME_FIELDS) {
            if (firstItem.hasOwnProperty(field)) {
                presentFields.push(field);
            } else {
                missingFields.push(field);
            }
        }
        
        console.log(`  ✅ Present fields (${presentFields.length}): ${presentFields.join(', ')}`);
        
        if (missingFields.length > 0) {
            console.log(`  ❌ Missing fields (${missingFields.length}): ${missingFields.join(', ')}`);
        }
        
        // Validate timeframe field
        const timeframe = firstItem.timeframe;
        const expectedTimeframes = ['24h', '3d', '7d'];
        if (expectedTimeframes.includes(timeframe)) {
            console.log(`  ✅ Valid timeframe: ${timeframe}`);
        } else {
            console.log(`  ❌ Invalid timeframe: ${timeframe} (expected: ${expectedTimeframes.join(', ')})`);
        }
        
        // Validate rank_type
        const rankType = firstItem.rank_type;
        if (rankType === endpoint.expectedRankType) {
            console.log(`  ✅ Correct rank_type: ${rankType}`);
        } else {
            console.log(`  ❌ Wrong rank_type: ${rankType} (expected: ${endpoint.expectedRankType})`);
        }
        
        // Show sample data
        console.log(`  📋 Sample item:`);
        console.log(`     - timeframe: ${firstItem.timeframe}`);
        console.log(`     - token_address: ${firstItem.token_address}`);
        console.log(`     - rank: ${firstItem.rank} (${firstItem.rank_type})`);
        console.log(`     - net_volume: ${firstItem.net_volume}`);
        
        if (firstItem.biggest_buyer) {
            console.log(`     - biggest_buyer: ${firstItem.biggest_buyer.wallet} (${firstItem.biggest_buyer.amount})`);
        } else {
            console.log(`     - biggest_buyer: null`);
        }
        
        if (firstItem.biggest_seller) {
            console.log(`     - biggest_seller: ${firstItem.biggest_seller.wallet} (${firstItem.biggest_seller.amount})`);
        } else {
            console.log(`     - biggest_seller: null`);
        }
        
        return missingFields.length === 0 && 
               timeframe && expectedTimeframes.includes(timeframe) &&
               rankType === endpoint.expectedRankType;
        
    } catch (error) {
        console.log(`  💥 Request failed: ${error.message}`);
        return false;
    }
}

async function testAllTimeframeEndpoints() {
    console.log('🚀 Testing Smart Money Timeframe Endpoints\n');
    console.log(`📡 Base URL: ${BASE_URL}`);
    console.log(`🔑 API Key: ${API_KEY.substring(0, 10)}...`);
    console.log(`📊 Testing ${TIMEFRAME_ENDPOINTS.length} new endpoints\n`);
    
    let passedTests = 0;
    let totalTests = TIMEFRAME_ENDPOINTS.length;
    
    for (const endpoint of TIMEFRAME_ENDPOINTS) {
        const passed = await testTimeframeEndpoint(endpoint);
        if (passed) passedTests++;
    }
    
    console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All timeframe endpoints are working correctly!');
    } else {
        console.log('⚠️  Some timeframe endpoints need attention.');
    }
    
    console.log('\n💡 Notes:');
    console.log('   - If endpoints return no data, ensure SmartMoney worker is running');
    console.log('   - Check Redis cache keys: smart_money:top_tokens:* and smart_money:bottom_tokens:*');
    console.log('   - Verify worker has populated timeframe data in the last hour');
    console.log('\n📚 Documentation updated:');
    console.log('   - OpenAPI: src/docs/openapi.json');
    console.log('   - API Docs: docs/API_DOCUMENTATION.md');
    console.log('   - README: docs/README.md');
    console.log('   - Postman: postman/StalkAPI_User_Collection.json');
}

// Run the tests
testAllTimeframeEndpoints().catch(console.error);
