import fetch from 'node-fetch';

// Test configuration
const BASE_URL = 'http://localhost:3000/api/v1';
const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG'; // Enterprise user

// Smart Money endpoints to test
const endpoints = [
    '/smart-money/daily-trends/most-bought-tokens',
    '/smart-money/daily-trends/most-sold-tokens',
    '/smart-money/daily-trends/daily-flows-sol',
    '/smart-money/daily-trends/daily-flows-meme'
];

async function testEndpoint(endpoint) {
    try {
        console.log(`🔍 Testing: ${endpoint}`);
        
        const response = await fetch(`${BASE_URL}${endpoint}`, {
            method: 'GET',
            headers: {
                'X-API-Key': API_KEY,
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            console.log(`  ✅ Success (${response.status})`);
            console.log(`  📊 Data: ${data.data ? 'Present' : 'Missing'}`);
            console.log(`  💰 Credits: ${data.credits_consumed || 0}`);
            console.log(`  📝 Message: ${data.message || 'No message'}`);
        } else {
            console.log(`  ❌ Failed (${response.status})`);
            console.log(`  🚫 Error: ${data.error || 'Unknown error'}`);
            if (data.required_tier) {
                console.log(`  🎯 Required: ${data.required_tier}`);
            }
        }
        
    } catch (error) {
        console.log(`  💥 Request failed: ${error.message}`);
    }
    
    console.log('');
}

async function testAllEndpoints() {
    console.log('🚀 Testing Smart Money API Endpoints\n');
    console.log(`📡 Base URL: ${BASE_URL}`);
    console.log(`🔑 API Key: ${API_KEY.substring(0, 10)}...`);
    console.log('');
    
    for (const endpoint of endpoints) {
        await testEndpoint(endpoint);
    }
    
    console.log('🎯 Test completed!');
    console.log('');
    console.log('💡 If you see access denied errors:');
    console.log('   1. Restart your API server');
    console.log('   2. Check that the server is running on port 3000');
    console.log('   3. Verify the API key is correct');
    console.log('   4. Ensure the SmartMoney worker has populated Redis cache');
}

// Run the tests
testAllEndpoints().catch(console.error);
