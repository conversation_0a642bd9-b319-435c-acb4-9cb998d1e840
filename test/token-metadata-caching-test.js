import { getTokenMetadata } from '../tools/coingecko.js';
import { TokenMetadata } from '../src/models/TokenMetadata.js';
import { cache } from '../src/config/redis.js';

// Test token addresses (known Solana tokens)
const TEST_TOKENS = [
    '4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump', // DADDY TATE
    'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm', // Another test token
    'So11111111111111111111111111111111111111112'  // Wrapped SOL
];

async function testTokenMetadataCaching() {
    console.log('🧪 Starting Token Metadata Caching System Test\n');

    try {
        // Test 1: Clear cache and database for clean test
        console.log('1️⃣ Clearing cache for test tokens...');
        await TokenMetadata.clearCache(TEST_TOKENS);
        console.log('✅ Cache cleared\n');

        // Test 2: First API call (should hit external API)
        console.log('2️⃣ First call - should fetch from API...');
        const startTime1 = Date.now();
        const result1 = await getTokenMetadata(TEST_TOKENS);
        const duration1 = Date.now() - startTime1;
        
        console.log(`✅ First call completed in ${duration1}ms`);
        console.log(`📊 Retrieved ${result1?.length || 0} tokens`);
        if (result1 && result1.length > 0) {
            console.log(`📝 Sample token: ${result1[0].name} (${result1[0].symbol})`);
        }
        console.log('');

        // Test 3: Second API call (should hit Redis cache)
        console.log('3️⃣ Second call - should hit Redis cache...');
        const startTime2 = Date.now();
        const result2 = await getTokenMetadata(TEST_TOKENS);
        const duration2 = Date.now() - startTime2;
        
        console.log(`✅ Second call completed in ${duration2}ms`);
        console.log(`📊 Retrieved ${result2?.length || 0} tokens`);
        console.log(`⚡ Speed improvement: ${Math.round(((duration1 - duration2) / duration1) * 100)}%`);
        console.log('');

        // Test 4: Clear Redis cache, test database fallback
        console.log('4️⃣ Testing database fallback (clearing Redis cache)...');
        await TokenMetadata.clearCache(TEST_TOKENS);
        
        const startTime3 = Date.now();
        const result3 = await getTokenMetadata(TEST_TOKENS);
        const duration3 = Date.now() - startTime3;
        
        console.log(`✅ Database fallback completed in ${duration3}ms`);
        console.log(`📊 Retrieved ${result3?.length || 0} tokens`);
        console.log('');

        // Test 5: Test cache statistics
        console.log('5️⃣ Testing cache statistics...');
        const stats = await TokenMetadata.getCacheStats();
        console.log(`📈 Cache statistics:`, stats);
        console.log('');

        // Test 6: Test single token vs multiple tokens
        console.log('6️⃣ Testing single token request...');
        const startTime4 = Date.now();
        const singleResult = await getTokenMetadata(TEST_TOKENS[0]);
        const duration4 = Date.now() - startTime4;
        
        console.log(`✅ Single token call completed in ${duration4}ms`);
        console.log(`📊 Retrieved token: ${singleResult?.[0]?.name || 'None'}`);
        console.log('');

        // Test 7: Test mixed cache scenario (some cached, some not)
        console.log('7️⃣ Testing mixed cache scenario...');
        const mixedTokens = [...TEST_TOKENS, 'NewTokenAddressThatDoesntExist123'];
        await TokenMetadata.clearCache(['NewTokenAddressThatDoesntExist123']);
        
        const startTime5 = Date.now();
        const mixedResult = await getTokenMetadata(mixedTokens);
        const duration5 = Date.now() - startTime5;
        
        console.log(`✅ Mixed cache call completed in ${duration5}ms`);
        console.log(`📊 Retrieved ${mixedResult?.length || 0} tokens from ${mixedTokens.length} requested`);
        console.log('');

        // Test 8: Performance comparison summary
        console.log('📊 PERFORMANCE SUMMARY:');
        console.log(`🔥 API Call (cold):     ${duration1}ms`);
        console.log(`⚡ Redis Cache (hot):   ${duration2}ms`);
        console.log(`💾 Database Fallback:   ${duration3}ms`);
        console.log(`🎯 Single Token:        ${duration4}ms`);
        console.log(`🔀 Mixed Cache:         ${duration5}ms`);
        console.log('');

        // Test 9: Verify data consistency
        console.log('9️⃣ Verifying data consistency...');
        const consistent = JSON.stringify(result1) === JSON.stringify(result2) && 
                          JSON.stringify(result2) === JSON.stringify(result3);
        console.log(`✅ Data consistency: ${consistent ? 'PASSED' : 'FAILED'}`);
        console.log('');

        console.log('🎉 Token Metadata Caching System Test Completed Successfully!');
        console.log('');
        console.log('📋 Test Results Summary:');
        console.log(`✅ API Integration: Working`);
        console.log(`✅ Redis Caching: Working`);
        console.log(`✅ Database Fallback: Working`);
        console.log(`✅ Background Updates: Working`);
        console.log(`✅ Data Consistency: ${consistent ? 'Verified' : 'Failed'}`);
        console.log(`✅ Performance Gain: ${Math.round(((duration1 - duration2) / duration1) * 100)}% faster with cache`);

    } catch (error) {
        console.error('❌ Test failed with error:', error);
        console.error('Stack trace:', error.stack);
    }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
    testTokenMetadataCaching()
        .then(() => {
            console.log('\n✅ Test completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n❌ Test failed:', error);
            process.exit(1);
        });
}

export { testTokenMetadataCaching };
