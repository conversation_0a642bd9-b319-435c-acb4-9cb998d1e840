import cron from 'node-cron';

/**
 * Verify SmartMoney Cron Schedule
 * 
 * This script verifies that the cron schedule '0 0,15,30,45 * * * *' 
 * will trigger at the correct times (every 15 minutes).
 */

function verifyCronSchedule() {
    console.log('🕐 SmartMoney Cron Schedule Verification\n');
    
    const cronPattern = '0 0,15,30,45 * * * *';
    console.log(`📋 Cron Pattern: ${cronPattern}`);
    console.log('📋 Expected Schedule: Every 15 minutes (at 00, 15, 30, 45 minutes of each hour)\n');
    
    // Get current time
    const now = new Date();
    console.log(`🕐 Current Time: ${now.toISOString()}`);
    console.log(`🕐 Current Local Time: ${now.toLocaleString()}\n`);
    
    // Calculate next execution times
    console.log('⏰ Next 10 Execution Times:');
    console.log('='.repeat(50));
    
    const nextExecutions = [];
    const testDate = new Date(now);
    
    // Find next 10 execution times
    for (let i = 0; i < 10; i++) {
        // Move to next minute
        testDate.setMinutes(testDate.getMinutes() + 1);
        testDate.setSeconds(0);
        testDate.setMilliseconds(0);
        
        // Check if this minute matches our cron pattern
        const minute = testDate.getMinutes();
        if ([0, 15, 30, 45].includes(minute)) {
            nextExecutions.push(new Date(testDate));
            
            // Skip to next quarter hour to avoid duplicates
            const nextQuarter = Math.ceil((minute + 1) / 15) * 15;
            if (nextQuarter >= 60) {
                testDate.setHours(testDate.getHours() + 1);
                testDate.setMinutes(0);
            } else {
                testDate.setMinutes(nextQuarter - 1); // -1 because we'll add 1 in the loop
            }
        }
        
        // Safety check to prevent infinite loop
        if (nextExecutions.length >= 10) break;
    }
    
    // Display next execution times
    nextExecutions.forEach((time, index) => {
        const timeFromNow = Math.round((time - now) / 1000 / 60); // minutes from now
        console.log(`${(index + 1).toString().padStart(2)}. ${time.toISOString()} (in ${timeFromNow} minutes)`);
    });
    
    console.log('\n📊 Schedule Verification:');
    console.log('='.repeat(50));
    
    // Verify the pattern is correct
    let isValid = true;
    const reasons = [];
    
    // Check that all executions are at correct minutes
    const validMinutes = [0, 15, 30, 45];
    for (const execution of nextExecutions) {
        const minute = execution.getMinutes();
        if (!validMinutes.includes(minute)) {
            isValid = false;
            reasons.push(`Invalid minute: ${minute} (should be 0, 15, 30, or 45)`);
        }
    }
    
    // Check that executions are 15 minutes apart (mostly)
    for (let i = 1; i < nextExecutions.length; i++) {
        const diff = (nextExecutions[i] - nextExecutions[i-1]) / 1000 / 60; // minutes
        if (diff !== 15 && diff !== 30 && diff !== 45) { // 30 and 45 can happen at hour boundaries
            // Allow some tolerance for hour boundaries
            if (!(diff >= 14 && diff <= 46)) {
                isValid = false;
                reasons.push(`Invalid interval: ${diff} minutes between executions ${i} and ${i+1}`);
            }
        }
    }
    
    if (isValid) {
        console.log('✅ Cron schedule is VALID');
        console.log('✅ All executions occur at 00, 15, 30, or 45 minutes');
        console.log('✅ Executions are properly spaced (15 minutes apart)');
    } else {
        console.log('❌ Cron schedule has issues:');
        reasons.forEach(reason => console.log(`   - ${reason}`));
    }
    
    // Test cron validation
    console.log('\n🧪 Cron Pattern Validation:');
    console.log('='.repeat(50));
    
    try {
        const isValidPattern = cron.validate(cronPattern);
        if (isValidPattern) {
            console.log('✅ Cron pattern syntax is valid');
        } else {
            console.log('❌ Cron pattern syntax is invalid');
        }
    } catch (error) {
        console.log('❌ Error validating cron pattern:', error.message);
    }
    
    // Show time until next execution
    const nextExecution = nextExecutions[0];
    const minutesUntilNext = Math.round((nextExecution - now) / 1000 / 60);
    const secondsUntilNext = Math.round((nextExecution - now) / 1000);
    
    console.log('\n⏱️  Next Execution Info:');
    console.log('='.repeat(50));
    console.log(`Next execution: ${nextExecution.toISOString()}`);
    console.log(`Time until next: ${minutesUntilNext} minutes (${secondsUntilNext} seconds)`);
    
    if (minutesUntilNext <= 15) {
        console.log('🟢 Next execution is within 15 minutes');
    } else {
        console.log('🟡 Next execution is more than 15 minutes away');
    }
    
    return isValid;
}

// Run verification if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const isValid = verifyCronSchedule();
    process.exit(isValid ? 0 : 1);
}

export default verifyCronSchedule;
