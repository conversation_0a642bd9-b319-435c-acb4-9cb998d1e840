import SmartMoney from '../src/workers/smartMoney.js';
import { cache } from '../src/config/redis.js';
import { testMongoConnection } from '../src/config/mongodb.js';

/**
 * SmartMoney Worker Test Suite
 * 
 * Tests the SmartMoney worker functionality including:
 * - Worker initialization and lifecycle
 * - Data update functionality
 * - Redis cache operations
 * - Cron job management
 */

async function testSmartMoneyWorker() {
    console.log('🧪 Starting SmartMoney Worker Test Suite...\n');
    
    let smartMoney = null;
    let testsPassed = 0;
    let testsTotal = 0;
    
    try {
        // Test 1: MongoDB Connection
        testsTotal++;
        console.log('📋 Test 1: MongoDB Connection');
        const mongoConnected = await testMongoConnection();
        if (mongoConnected) {
            console.log('✅ MongoDB connection successful');
            testsPassed++;
        } else {
            console.log('❌ MongoDB connection failed');
        }
        console.log();
        
        // Test 2: Worker Initialization
        testsTotal++;
        console.log('📋 Test 2: Worker Initialization');
        smartMoney = new SmartMoney();
        
        // Check initial state
        if (!smartMoney.isRunning && smartMoney.cronJob === null) {
            console.log('✅ Worker initialized with correct initial state');
            testsPassed++;
        } else {
            console.log('❌ Worker initialization state incorrect');
        }
        console.log();
        
        // Test 3: Worker Start
        testsTotal++;
        console.log('📋 Test 3: Worker Start and Cron Job Creation');
        await smartMoney.init();
        
        // Check if worker is running and cron job is created
        if (smartMoney.isRunning && smartMoney.cronJob !== null) {
            console.log('✅ Worker started successfully with cron job');
            testsPassed++;
        } else {
            console.log('❌ Worker start failed or cron job not created');
        }
        console.log();
        
        // Test 4: Manual Data Update
        testsTotal++;
        console.log('📋 Test 4: Manual Data Update');
        const startTime = Date.now();
        const updateResult = await smartMoney.updateData();
        const duration = Date.now() - startTime;
        
        if (updateResult === true) {
            console.log(`✅ Data update completed successfully in ${duration}ms`);
            testsPassed++;
        } else {
            console.log('❌ Data update failed');
        }
        console.log();
        
        // Test 5: Redis Cache Verification
        testsTotal++;
        console.log('📋 Test 5: Redis Cache Verification');
        const cacheKeys = [
            'smart_money:most_bought_tokens',
            'smart_money:most_sold_tokens',
            'smart_money:daily_flows_sol',
            'smart_money:daily_flows_meme'
        ];
        
        let cacheTestsPassed = 0;
        for (const key of cacheKeys) {
            const data = await cache.get(key);
            if (data && Array.isArray(data) && data.length > 0) {
                console.log(`  ✅ Cache key '${key}' contains ${data.length} items`);
                cacheTestsPassed++;
            } else {
                console.log(`  ❌ Cache key '${key}' is empty or invalid`);
            }
        }
        
        if (cacheTestsPassed === cacheKeys.length) {
            console.log('✅ All Redis cache keys populated successfully');
            testsPassed++;
        } else {
            console.log(`❌ Only ${cacheTestsPassed}/${cacheKeys.length} cache keys populated`);
        }
        console.log();
        
        // Test 6: Cache Data Structure Validation
        testsTotal++;
        console.log('📋 Test 6: Cache Data Structure Validation');
        const mostBoughtTokens = await cache.get('smart_money:most_bought_tokens');

        if (mostBoughtTokens && mostBoughtTokens.length > 0) {
            const sampleToken = mostBoughtTokens[0];
            const requiredFields = ['date', 'rank', 'token_address', 'rank_type'];
            const metadataFields = ['symbol', 'name', 'decimals', 'logo']; // links commented out in addTokenMetadata function
            const hasAllRequiredFields = requiredFields.every(field => sampleToken.hasOwnProperty(field));
            const hasMetadataFields = metadataFields.every(field => sampleToken.hasOwnProperty(field));

            if (hasAllRequiredFields && hasMetadataFields) {
                console.log('✅ Cache data structure is valid with metadata');
                console.log(`  Sample token: ${sampleToken.token_address} (rank ${sampleToken.rank})`);
                console.log(`  Token metadata: ${sampleToken.symbol} - ${sampleToken.name}`);
                testsPassed++;
            } else {
                console.log('❌ Cache data structure is missing required fields or metadata');
                console.log('  Required fields:', requiredFields);
                console.log('  Metadata fields:', metadataFields);
                console.log('  Sample data fields:', Object.keys(sampleToken));
                console.log('  Missing required:', requiredFields.filter(field => !sampleToken.hasOwnProperty(field)));
                console.log('  Missing metadata:', metadataFields.filter(field => !sampleToken.hasOwnProperty(field)));
            }
        } else {
            console.log('❌ No data available for structure validation');
        }
        console.log();
        
        // Test 7: Worker Stop
        testsTotal++;
        console.log('📋 Test 7: Worker Stop and Cleanup');
        smartMoney.stop();
        
        // Check if worker is stopped and cron job is cleared
        if (!smartMoney.isRunning && smartMoney.cronJob === null) {
            console.log('✅ Worker stopped successfully and cron job cleared');
            testsPassed++;
        } else {
            console.log('❌ Worker stop failed or cron job not cleared');
        }
        console.log();
        
    } catch (error) {
        console.error('❌ Test suite error:', error);
    }
    
    // Test Results Summary
    console.log('📊 Test Results Summary');
    console.log('='.repeat(50));
    console.log(`Tests Passed: ${testsPassed}/${testsTotal}`);
    console.log(`Success Rate: ${((testsPassed / testsTotal) * 100).toFixed(1)}%`);
    
    if (testsPassed === testsTotal) {
        console.log('🎉 All tests passed! SmartMoney worker is functioning correctly.');
    } else {
        console.log('⚠️  Some tests failed. Please review the output above.');
    }
    
    // Cleanup
    if (smartMoney && smartMoney.isRunning) {
        smartMoney.stop();
    }
    
    return testsPassed === testsTotal;
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testSmartMoneyWorker()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Test execution error:', error);
            process.exit(1);
        });
}

export default testSmartMoneyWorker;
