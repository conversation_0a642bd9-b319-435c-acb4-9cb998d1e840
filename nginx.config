# Nginx configuration for data.stalkapi.com
# Optimized for Cloudflare origin with SSL and real IP handling

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=100r/m;
limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=20r/m;
limit_req_zone $binary_remote_addr zone=ws_limit:10m rate=50r/m;

# Upstream configuration for Node.js app
upstream nodejs_backend {
    server 127.0.0.1:3001;
    keepalive 32;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name data.stalkapi.com;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Redirect all HTTP traffic to HTTPS
    return 301 https://$server_name$request_uri;
}

# Main HTTPS server block
server {
    listen 443 ssl;
    http2 on;
    server_name data.stalkapi.com;

    # SSL Configuration for Cloudflare Origin Certificate
    ssl_certificate /etc/nginx/ssl/origin-cert.pem;
    ssl_certificate_key /etc/nginx/ssl/origin-key.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;

    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;

    # Cloudflare Real IP Configuration
    # Set real IP from Cloudflare
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from 2400:cb00::/32;
    set_real_ip_from 2606:4700::/32;
    set_real_ip_from 2803:f800::/32;
    set_real_ip_from 2405:b500::/32;
    set_real_ip_from 2405:8100::/32;
    set_real_ip_from 2c0f:f248::/32;
    set_real_ip_from 2a06:98c0::/29;

    # Use CF-Connecting-IP header for real IP
    real_ip_header CF-Connecting-IP;
    real_ip_recursive on;

    # Security Headers (global)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' wss: https:;" always;

    # Remove server signature
    server_tokens off;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        text/plain
        text/css
        text/js
        text/xml
        text/javascript
        text/html;

    # Client settings
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # Proxy settings for Node.js backend
    proxy_http_version 1.1;
    proxy_cache_bypass $http_upgrade;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;

    # API endpoints with rate limiting
    location /api/ {
        limit_req zone=api_limit burst=20 nodelay;
        limit_req_status 429;

        proxy_pass http://nodejs_backend;
        proxy_read_timeout 60s;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;

        # Add API-specific headers
        add_header X-API-Version "v1" always;
        add_header X-Frame-Options DENY always;
    }

    # Authentication endpoints with stricter rate limiting
    location /auth/ {
        limit_req zone=auth_limit burst=5 nodelay;
        limit_req_status 429;

        proxy_pass http://nodejs_backend;
        proxy_read_timeout 30s;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;

        # Security headers for auth endpoints
        add_header X-Frame-Options DENY always;
    }

    # Admin endpoints with additional security
    location /admin/ {
        limit_req zone=auth_limit burst=3 nodelay;
        limit_req_status 429;

        # Additional security for admin endpoints
        add_header X-Admin-Access "restricted" always;
        add_header X-Frame-Options DENY always;

        proxy_pass http://nodejs_backend;
        proxy_read_timeout 60s;
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
    }

    # WebSocket endpoints
    location /ws {
        limit_req zone=ws_limit burst=10 nodelay;
        limit_req_status 429;

        proxy_pass http://nodejs_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket specific timeouts
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
        proxy_connect_timeout 60s;

        # Disable buffering for real-time data
        proxy_buffering off;
    }

    # WebSocket API endpoints
    location /ws-api/ {
        limit_req zone=api_limit burst=10 nodelay;
        limit_req_status 429;

        proxy_pass http://nodejs_backend;
        proxy_read_timeout 30s;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;

        # Security headers for WebSocket API endpoints
        add_header X-Frame-Options DENY always;
    }

    # Documentation endpoints - optimized for web content
    location /docs/ {
        limit_req zone=api_limit burst=15 nodelay;
        limit_req_status 429;

        proxy_pass http://nodejs_backend;
        proxy_read_timeout 60s;
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;

        # Optimize for documentation content
        add_header Cache-Control "public, max-age=300" always;

        # Allow iframe embedding for documentation
        add_header X-Frame-Options SAMEORIGIN always;
    }

    # Documentation root (exact match)
    location = /docs {
        limit_req zone=api_limit burst=15 nodelay;
        limit_req_status 429;

        proxy_pass http://nodejs_backend;
        proxy_read_timeout 60s;
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;

        # Cache documentation page
        add_header Cache-Control "public, max-age=300" always;

        # Allow iframe embedding for documentation
        add_header X-Frame-Options SAMEORIGIN always;
    }

    # Health check endpoint (no rate limiting)
    location /health {
        proxy_pass http://nodejs_backend;
        proxy_read_timeout 10s;
        proxy_connect_timeout 10s;
        proxy_send_timeout 10s;

        # Cache health checks briefly
        add_header Cache-Control "public, max-age=60";
        add_header X-Frame-Options DENY always;
    }

    # Root and other endpoints
    location / {
        limit_req zone=api_limit burst=10 nodelay;
        limit_req_status 429;

        proxy_pass http://nodejs_backend;
        proxy_read_timeout 30s;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;

        # Security headers for general endpoints
        add_header X-Frame-Options DENY always;
    }

    # Static files (if any)
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://nodejs_backend;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        add_header X-Frame-Options DENY always;
    }

    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \.(env|config|log)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Custom error pages
    error_page 429 /429.html;
    error_page 500 502 503 504 /50x.html;

    location = /429.html {
        root /var/www/html;
        internal;
    }

    location = /50x.html {
        root /var/www/html;
        internal;
    }

    # Logging
    access_log /var/log/nginx/data.stalkapi.com.access.log combined;
    error_log /var/log/nginx/data.stalkapi.com.error.log warn;
}