# StalkAPI Postman Collection

This folder contains the comprehensive Postman collection for testing all aspects of the StalkAPI engine, including REST API endpoints and WebSocket connection information.

## 📦 Collection Overview

### 🔌 User API Collection
**File**: `StalkAPI_User_Collection.json`

Complete collection for end-user API testing:
- **System Health** - API status and health checks
- **KOL Feed Data** - Historical KOL trading data endpoints
- **WebSocket API** - Connection details and stream management
- **Credit Information** - Stream credit costs and usage statistics

### 👑 Admin API Collection
**File**: `StalkAPI_Admin_Collection.json`

Comprehensive admin collection for internal management:
- **Admin Authentication** - Admin profile and permissions
- **Tier Management** - Complete CRUD operations for access tiers
- **User Management** - User credit administration and monitoring
- **Admin User Management** - Admin user creation and management (system admin only)
- **Analytics & Monitoring** - System analytics and usage statistics

### ⚙️ Environment Configuration
**File**: `StalkAPI_Postman_Environment.json`

Environment variables for the collection:
- **Base URLs** - Development and production endpoints
- **Authentication** - JWT tokens and API keys
- **WebSocket URLs** - Real-time connection endpoints
- **Admin Keys** - Administrative access credentials

## 🚀 Quick Start

### 1. Import Collections
1. Open Postman
2. Click **Import** button
3. Select the JSON files you need:
   - `StalkAPI_User_Collection.json` - For end-user API testing
   - `StalkAPI_Admin_Collection.json` - For admin/internal management
   - `StalkAPI_Postman_Environment.json` - Environment variables

### 2. Setup Environment
1. Select **"StalkAPI Environment"** from the environment dropdown
2. Update environment variables if needed:
   - `base_url` - API server URL (default: http://localhost:3001)
   - `websocket_url` - WebSocket server URL (default: ws://localhost:3001/ws)
   - `admin_api_key` - Admin API key for administrative functions

### 3. Authentication Setup
1. **Get JWT Token**:
   - Run "Login User" request in REST API collection
   - Token will be automatically saved to environment
2. **Get API Key**:
   - Available in user profile after login
   - Manually set in environment variables if needed

## 📋 Collection Details

### User Collection Structure

```
📁 System Health
└── API Status

📁 KOL Feed Data
└── Get KOL Feed History (3 credits)

📁 WebSocket API
├── WebSocket Info
├── Available Streams
├── Active Sessions
├── Usage Statistics
├── Stream Credits Info
└── Stream Credit Usage Stats
```

### Admin Collection Structure

```
📁 System Health
└── API Status

📁 Admin Authentication
└── Get Admin Info

📁 Tier Management
├── Get All Tiers
├── Enable Tier
├── Disable Tier
├── Update Tier Configuration
└── Get Tier Statistics

📁 User Management
├── Get User Credits
├── Add Credits to User
└── Reset User Monthly Credits

📁 Admin User Management (System Admin Only)
├── Get All Admin Users
├── Create Admin User
└── Update Admin User

📁 Analytics & Monitoring
├── Credit Usage Analytics
└── Endpoint Usage Analytics
```

### WebSocket Connection Information

The REST API collection includes WebSocket-related endpoints:
- **Get WebSocket Server Info** - Connection details and capabilities
- **Get Available Streams** - Stream discovery based on user tier
- **WebSocket URL** - Available in environment variables for manual testing

For actual WebSocket testing, use:
- **WebSocket Client** - Browser-based tools like WebSocket King or Postman's WebSocket feature
- **Node.js Test Script** - Run `node tests/websocket-test.js` for automated testing
- **Browser Console** - Use JavaScript WebSocket API for manual testing

## 🔧 Testing Workflows

### User API Testing (StalkAPI_User_Collection.json)
1. **API Status** → Check system health
2. **KOL Feed History** → Test historical data retrieval (3 credits)
3. **WebSocket Info** → Get connection details and capabilities
4. **Stream Credits** → Check credit costs for streams
5. **Usage Statistics** → Monitor WebSocket usage

### Admin Testing (StalkAPI_Admin_Collection.json)
1. **Set Admin API Key** → Configure admin authentication in environment
2. **Get Admin Info** → Verify admin permissions and profile
3. **Tier Management** → View, enable/disable, update tier configurations
4. **User Management** → View user credits, add credits, reset monthly usage
5. **Admin User Management** → Create/update admin users (system admin only)
6. **Analytics** → Monitor credit usage and endpoint analytics

### WebSocket Testing (Both Collections)
1. **Get WebSocket Info** → Check connection details and capabilities
2. **Get Available Streams** → Check tier permissions and available streams
3. **Use WebSocket Client** → Connect using: `wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY`
4. **Run Test Script** → Use `tests/websocket-test.js` for automated testing

## 🎯 Stream Access by Tier

| Stream | Free | Basic | Premium | Enterprise |
|--------|------|-------|---------|------------|
| **demo-stream** | ✅ | ✅ | ✅ | ✅ |
| **data-stream** | ❌ | ✅ | ✅ | ✅ |
| **analytics-stream** | ❌ | ❌ | ✅ | ✅ |
| **enterprise-stream** | ❌ | ❌ | ❌ | ✅ |

## 💳 Credit Costs

| Endpoint | Credits | Notes |
|----------|---------|-------|
| `/api/v1/demo` | 1 | Basic functionality |
| `/api/v1/data` | 2 | Data retrieval |
| `/api/v1/analytics` | 5 | Complex processing |
| WebSocket connections | 0 | No credit cost |
| WebSocket messages | 0 | Real-time streaming |

## 🔐 Authentication Methods

### JWT Token Authentication
- **REST API**: `Authorization: Bearer <token>`
- **WebSocket**: `?token=<jwt_token>` query parameter
- **Expires**: Configurable (default: 24 hours)

### API Key Authentication
- **REST API**: `X-API-Key: <api_key>`
- **WebSocket**: `?apiKey=<api_key>` query parameter
- **Expires**: Never (permanent)

### Admin API Key Authentication
- **Admin Endpoints**: `X-Admin-API-Key: <admin_key>`
- **Permissions**: Granular permission system
- **Expires**: Never (permanent)

## 🧪 Testing Scenarios

### Credit System Testing
1. **Monitor Credits** → Check initial balance
2. **Make API Calls** → Consume credits
3. **Check Balance** → Verify deduction
4. **Test Limits** → Exceed monthly limit
5. **Admin Reset** → Restore credits

### Tier Access Testing
1. **Free Tier** → Test limited access
2. **Upgrade Tier** → Admin tier change
3. **Test New Access** → Verify expanded permissions
4. **WebSocket Limits** → Test connection limits

### Error Handling Testing
1. **Invalid Credentials** → Test authentication failures
2. **Insufficient Credits** → Test credit exhaustion
3. **Tier Restrictions** → Test unauthorized access
4. **Rate Limiting** → Test request limits

## 📊 Response Examples

### Successful API Response
```json
{
  "success": true,
  "data": { ... },
  "credits_remaining": 9949,
  "credits_used": 1
}
```

### WebSocket Stream Data
```json
{
  "stream": "demo-stream",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    "value": 42,
    "status": "active"
  }
}
```

### Error Response
```json
{
  "error": "Insufficient credits",
  "code": "INSUFFICIENT_CREDITS",
  "credits_remaining": 0,
  "credits_required": 5
}
```

## 🔧 Environment Variables

| Variable | Description | Default | Used In |
|----------|-------------|---------|---------|
| `base_url` | API server URL | `http://localhost:3001` | Both collections |
| `websocket_url` | WebSocket server URL | `ws://localhost:3001/ws` | User collection |
| `api_key` | User API key | Set manually | User collection |
| `admin_api_key` | Admin authentication key | `admin_api_key_super_secure_change_in_production` | Admin collection |
| `tier_id` | Tier ID for admin operations | `1` | Admin collection |
| `user_id` | User ID for admin operations | Set manually | Admin collection |
| `admin_id` | Admin ID for admin management | Set manually | Admin collection |

## 📚 Additional Resources

- **[API Documentation](../docs/API_DOCUMENTATION.md)** - Complete API reference
- **[WebSocket Guide](../docs/API_DOCUMENTATION.md#websocket-api)** - WebSocket implementation details
- **[Credit System](../docs/CREDIT_SYSTEM_GUIDE.md)** - Credit system documentation
- **[Postman Guide](../docs/POSTMAN_GUIDE.md)** - Detailed usage instructions

## 🆘 Troubleshooting

### Common Issues
1. **Connection Failed** → Check server is running on correct port
2. **Authentication Error** → Verify token/API key is valid
3. **WebSocket Connection Drops** → Check network stability
4. **Credit Errors** → Verify sufficient credits available

### Debug Tips
1. **Check Console** → Postman console shows detailed logs
2. **Verify Environment** → Ensure correct environment selected
3. **Test Authentication** → Start with login/profile endpoints
4. **Monitor Server Logs** → Check application logs for errors

---

**Last Updated**: June 2025  
**Collections Version**: 1.0.0  
**Postman Version**: 10.0+
