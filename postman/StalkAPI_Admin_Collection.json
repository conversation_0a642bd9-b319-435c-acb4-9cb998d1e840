{"info": {"_postman_id": "stalkapi-admin-collection", "name": "StalkAPI - Admin Collection", "description": "Comprehensive admin collection for StalkAPI with tier management, user administration, and system analytics.\n\n## Features\n- Complete tier management (CRUD operations)\n- User credit administration\n- System analytics and monitoring\n- Admin user management\n- Audit logging and security\n\n## Authentication\n- Admin API Key: Include in X-Admin-API-Key header\n- All endpoints require admin authentication\n- Permission-based access control\n\n## Base URL\n- Development: `http://localhost:3001`\n- Production: `https://data.stalkapi.com`\n\n## Access Tiers (Current Database Values)\n- **Free**: 1,000 credits/month ($0.00) - 1 WebSocket connection - Disabled\n- **Basic**: 1,000,000 credits/month ($49.99) - 3 WebSocket connections\n- **Premium**: 5,000,000 credits/month ($149.99) - 5 WebSocket connections\n- **Enterprise**: Unlimited credits ($499.99) - 10 WebSocket connections\n\n## Admin Permissions\n- `tiers:read` - View tier information\n- `tiers:write` - Modify tier configuration\n- `users:read` - View user information\n- `users:write` - Modify user data\n- `analytics:read` - View system analytics\n- `system:admin` - Full system administration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🏥 System Health", "item": [{"name": "API Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "Check API server status and basic information."}, "response": []}], "description": "System health and status endpoints."}, {"name": "👑 Admin <PERSON>cation", "item": [{"name": "Get Admin Info", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/me", "host": ["{{base_url}}"], "path": ["admin", "me"]}, "description": "Get current admin user information and permissions."}, "response": []}], "description": "Admin authentication and profile management."}, {"name": "🎯 Tier Management", "item": [{"name": "Get All Tiers", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/tiers", "host": ["{{base_url}}"], "path": ["admin", "tiers"]}, "description": "Get all access tiers including disabled ones. Requires 'tiers:read' permission."}, "response": []}, {"name": "Enable Tier", "request": {"method": "POST", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/tiers/{{tier_id}}/enable", "host": ["{{base_url}}"], "path": ["admin", "tiers", "{{tier_id}}", "enable"]}, "description": "Enable a disabled tier. Requires 'tiers:write' permission."}, "response": []}, {"name": "Disable Tier", "request": {"method": "POST", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/tiers/{{tier_id}}/disable", "host": ["{{base_url}}"], "path": ["admin", "tiers", "{{tier_id}}", "disable"]}, "description": "Disable a tier (with safety checks for users). Requires 'tiers:write' permission."}, "response": []}, {"name": "Update Tier Configuration", "request": {"method": "PUT", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Tier Name\",\n  \"description\": \"Updated tier description\",\n  \"max_credits_per_month\": 50000,\n  \"max_requests_per_minute\": 120,\n  \"max_websocket_connections\": 5,\n  \"price_per_month\": 29.99,\n  \"allowed_endpoints\": [\"/api/v1/demo\", \"/api/v1/data\"],\n  \"allowed_streams\": [\"demo-stream\", \"data-stream\"]\n}"}, "url": {"raw": "{{base_url}}/admin/tiers/{{tier_id}}", "host": ["{{base_url}}"], "path": ["admin", "tiers", "{{tier_id}}"]}, "description": "Update tier configuration. Requires 'tiers:write' permission."}, "response": []}, {"name": "Get Tier Statistics", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/tiers/{{tier_id}}/stats", "host": ["{{base_url}}"], "path": ["admin", "tiers", "{{tier_id}}", "stats"]}, "description": "Get usage statistics for a specific tier. Requires 'tiers:read' permission."}, "response": []}], "description": "Access tier management and configuration."}, {"name": "👥 User Management", "item": [{"name": "Get User Credits", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}/credits", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}", "credits"]}, "description": "Get detailed credit information for a specific user. Requires 'users:read' permission."}, "response": []}, {"name": "Add Credits to User", "request": {"method": "POST", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"credits_to_add\": 1000,\n  \"reason\": \"Bonus credits for testing\"\n}"}, "url": {"raw": "{{base_url}}/admin/users/{{user_id}}/credits", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}", "credits"]}, "description": "Add credits to a user's account. Requires 'users:write' permission."}, "response": []}, {"name": "Reset User Monthly Credits", "request": {"method": "POST", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/users/{{user_id}}/credits/reset", "host": ["{{base_url}}"], "path": ["admin", "users", "{{user_id}}", "credits", "reset"]}, "description": "Reset user's monthly credit usage counter. Requires 'users:write' permission."}, "response": []}], "description": "User account and credit management."}, {"name": "🔐 Admin User Management", "item": [{"name": "Get All Admin Users", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/admins", "host": ["{{base_url}}"], "path": ["admin", "admins"]}, "description": "Get all admin users. Requires 'system:admin' permission."}, "response": []}, {"name": "Create Admin User", "request": {"method": "POST", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Admin\",\n  \"email\": \"<EMAIL>\",\n  \"permissions\": [\"tiers:read\", \"tiers:write\", \"users:read\"]\n}"}, "url": {"raw": "{{base_url}}/admin/admins", "host": ["{{base_url}}"], "path": ["admin", "admins"]}, "description": "Create a new admin user. Requires 'system:admin' permission."}, "response": []}, {"name": "Update Admin User", "request": {"method": "PUT", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Admin Name\",\n  \"permissions\": [\"tiers:read\", \"tiers:write\", \"users:read\", \"users:write\"]\n}"}, "url": {"raw": "{{base_url}}/admin/admins/{{admin_id}}", "host": ["{{base_url}}"], "path": ["admin", "admins", "{{admin_id}}"]}, "description": "Update an admin user. Requires 'system:admin' permission."}, "response": []}], "description": "Admin user management (system admin only)."}, {"name": "📊 Analytics & Monitoring", "item": [{"name": "Credit Usage Analytics", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/analytics/credits?period=month&limit=10", "host": ["{{base_url}}"], "path": ["admin", "analytics", "credits"], "query": [{"key": "period", "value": "month", "description": "Time period: day, week, month"}, {"key": "limit", "value": "10", "description": "Number of top results to return"}]}, "description": "Get credit usage analytics and top consumers. Requires 'analytics:read' permission."}, "response": []}, {"name": "Endpoint Usage Analytics", "request": {"method": "GET", "header": [{"key": "X-Admin-API-Key", "value": "{{admin_api_key}}"}], "url": {"raw": "{{base_url}}/admin/analytics/endpoints?period=week&limit=15", "host": ["{{base_url}}"], "path": ["admin", "analytics", "endpoints"], "query": [{"key": "period", "value": "week", "description": "Time period: day, week, month"}, {"key": "limit", "value": "15", "description": "Number of top endpoints to return"}]}, "description": "Get endpoint usage analytics. Requires 'analytics:read' permission."}, "response": []}], "description": "System analytics and monitoring."}], "variable": [{"key": "base_url", "value": "http://localhost:3001", "type": "string"}, {"key": "admin_api_key", "value": "admin_api_key_super_secure_change_in_production", "type": "string"}, {"key": "tier_id", "value": "1", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "admin_id", "value": "", "type": "string"}]}