{"info": {"_postman_id": "stalkapi-user-collection", "name": "StalkAPI - User Collection", "description": "Professional KOL (Key Opinion Leader) trading data API with real-time WebSocket streaming, historical data access, and smart money analytics.\n\n## Features\n- Real-time KOL trading stream via WebSocket (2 credits per message)\n- Historical KOL trading data via REST API (3 credits per request)\n- **Smart Money Analytics**: Daily trends and flow analysis (2 credits per request)\n  - Most bought/sold tokens by smart money traders\n  - Daily SOL and memecoin flow data\n  - 15-day historical analysis with ranking changes\n- Credit-based usage tracking\n- Multi-tier access control\n- API key authentication\n\n## Authentication\n- API Key: Include in X-API-Key header\n- All endpoints require API key authentication\n- Registration/login are admin-only functions\n\n## Base URL\n- Development: `http://localhost:3001`\n- Production: `https://data.stalkapi.com`\n\n## Access Tiers\n- **Free**: 1,000 credits/month ($0.00) - 1 WebSocket connection\n- **Basic**: 1,000,000 credits/month ($49.99) - 3 WebSocket connections, Smart Money API\n- **Premium**: 5,000,000 credits/month ($149.99) - 5 WebSocket connections, Smart Money API\n- **Enterprise**: Unlimited credits ($499.99) - 10 WebSocket connections, Smart Money API\n\n## Core API Endpoints\n- `POST /api/v1/core/token-price` - Token price and market data (3 credits)\n\n## Smart Money API Endpoints\n- `GET /api/v1/smart-money/daily-trends/most-bought-tokens` - Most bought tokens (2 credits)\n- `GET /api/v1/smart-money/daily-trends/most-sold-tokens` - Most sold tokens (2 credits)\n- `GET /api/v1/smart-money/daily-trends/daily-flows-sol` - Daily SOL flows (2 credits)\n- `GET /api/v1/smart-money/daily-trends/daily-flows-meme` - Daily meme flows (2 credits)\n- `GET /api/v1/smart-money/top-tokens/24h` - Top tokens 24h (2 credits)\n- `GET /api/v1/smart-money/top-tokens/3d` - Top tokens 3d (2 credits)\n- `GET /api/v1/smart-money/top-tokens/7d` - Top tokens 7d (2 credits)\n- `GET /api/v1/smart-money/bottom-tokens/24h` - Bottom tokens 24h (2 credits)\n- `GET /api/v1/smart-money/bottom-tokens/3d` - Bottom tokens 3d (2 credits)\n- `GET /api/v1/smart-money/bottom-tokens/7d` - Bottom tokens 7d (2 credits)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🏥 System Health", "item": [{"name": "API Root", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "Get API information and available endpoints. No authentication required."}, "response": []}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check API health status. No authentication required."}, "response": []}, {"name": "API Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "status"]}, "description": "Get API status information. No authentication required."}, "response": []}], "description": "System health and status endpoints."}, {"name": "🔐 Authentication", "item": [{"name": "Get Usage Statistics", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/auth/usage?days=30", "host": ["{{base_url}}"], "path": ["auth", "usage"], "query": [{"key": "days", "value": "30", "description": "Number of days to get usage statistics for"}]}, "description": "Get user's API usage statistics for the specified period."}, "response": []}], "description": "User authentication and profile management."}, {"name": "🔧 Core Endpoints", "item": [{"name": "Get Token Price", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tokenAddress\": \"EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/core/token-price", "host": ["{{base_url}}"], "path": ["api", "v1", "core", "token-price"]}, "description": "Get current price and market data for Solana tokens from GeckoTerminal. Supports single token address or array of up to 30 token addresses. Costs 3 credits per request. Requires Basic tier or higher."}, "response": [{"name": "Single Token Success", "originalRequest": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tokenAddress\": \"EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/core/token-price", "host": ["{{base_url}}"], "path": ["api", "v1", "core", "token-price"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": [\n    {\n      \"tokenAddress\": \"EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm\",\n      \"price\": \"0.893596704404597317132775363093366352048947631\",\n      \"market_cap\": \"892637331.8339748\",\n      \"24h_volume\": \"3688406.77435693\",\n      \"24h_price_change_percentage\": \"4.0356857922\",\n      \"total_reserve_usd\": \"8150312.6721030133398176\"\n    }\n  ],\n  \"credits_consumed\": 3,\n  \"message\": \"Token price retrieved successfully\"\n}"}, {"name": "Multiple Tokens Success", "originalRequest": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tokenAddress\": [\n    \"EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm\",\n    \"JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/core/token-price", "host": ["{{base_url}}"], "path": ["api", "v1", "core", "token-price"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": [\n    {\n      \"tokenAddress\": \"EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm\",\n      \"price\": \"0.893596704404597317132775363093366352048947631\",\n      \"market_cap\": \"892637331.8339748\",\n      \"24h_volume\": \"3688406.77435693\",\n      \"24h_price_change_percentage\": \"4.0356857922\",\n      \"total_reserve_usd\": \"8150312.6721030133398176\"\n    },\n    {\n      \"tokenAddress\": \"JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump\",\n      \"price\": \"0.0215240294427430989745915588630198928187561555\",\n      \"market_cap\": \"21475407.940906033\",\n      \"24h_volume\": \"3505137.70607422\",\n      \"24h_price_change_percentage\": \"-17.231500045179928\",\n      \"total_reserve_usd\": \"1302442.208296009184474\"\n    }\n  ],\n  \"credits_consumed\": 3,\n  \"message\": \"Token price retrieved successfully\"\n}"}]}, {"name": "<PERSON><PERSON> (API Key)", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tokenAddress\": [\n    \"EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm\",\n    \"JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/core/token-metadata", "host": ["{{base_url}}"], "path": ["api", "v1", "core", "token-metadata"]}, "description": "Get token metadata including name, symbol, decimals, and logo. Supports up to 30 token addresses per request. Features intelligent caching (Redis → Database → GeckoTerminal API) for optimal performance. Costs 3 credits per request. Requires Basic tier or higher."}, "response": [{"name": "Single Token Success", "originalRequest": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tokenAddress\": \"EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/core/token-metadata", "host": ["{{base_url}}"], "path": ["api", "v1", "core", "token-metadata"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": [\n    {\n      \"tokenAddress\": \"EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm\",\n      \"name\": \"dogwifhat\",\n      \"symbol\": \"$WIF\",\n      \"decimals\": 6,\n      \"logo\": \"https://coin-images.coingecko.com/coins/images/33566/large/dogwifhat.jpg?1702499428\"\n    }\n  ],\n  \"credits_consumed\": 3,\n  \"message\": \"Token metadata retrieved successfully\",\n  \"cache_info\": {\n    \"note\": \"Data served from cache/database when available for optimal performance\"\n  }\n}"}, {"name": "Multiple Tokens Success", "originalRequest": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tokenAddress\": [\n    \"EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm\",\n    \"JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/core/token-metadata", "host": ["{{base_url}}"], "path": ["api", "v1", "core", "token-metadata"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": [\n    {\n      \"tokenAddress\": \"EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm\",\n      \"name\": \"dogwifhat\",\n      \"symbol\": \"$WIF\",\n      \"decimals\": 6,\n      \"logo\": \"https://coin-images.coingecko.com/coins/images/33566/large/dogwifhat.jpg?1702499428\"\n    },\n    {\n      \"tokenAddress\": \"JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump\",\n      \"name\": \"LABUBU\",\n      \"symbol\": \"LABUBU\",\n      \"decimals\": 6,\n      \"logo\": \"https://assets.geckoterminal.com/kmsbuidlgor4fzk6jutnrqwd95ob\"\n    }\n  ],\n  \"credits_consumed\": 3,\n  \"message\": \"Token metadata retrieved successfully\",\n  \"cache_info\": {\n    \"note\": \"Data served from cache/database when available for optimal performance\"\n  }\n}"}, {"name": "Token Not Found", "originalRequest": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tokenAddress\": [\"nonexistent_token_address\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/core/token-metadata", "host": ["{{base_url}}"], "path": ["api", "v1", "core", "token-metadata"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": false,\n  \"error\": \"Token metadata not found\",\n  \"credits_consumed\": 0,\n  \"credit_note\": \"No credits charged for server errors\",\n  \"timestamp\": \"2025-01-30T12:34:56.789Z\",\n  \"path\": \"/api/v1/core/token-metadata\",\n  \"method\": \"POST\"\n}"}, {"name": "Invalid JSON Error", "originalRequest": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tokenAddress\": [\"invalid\",]\n}"}, "url": {"raw": "{{base_url}}/api/v1/core/token-metadata", "host": ["{{base_url}}"], "path": ["api", "v1", "core", "token-metadata"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": false,\n  \"error\": \"Invalid JSON format in request body\",\n  \"code\": \"INVALID_JSON\",\n  \"hint\": \"Please check your request body format and ensure it contains valid JSON\",\n  \"credits_consumed\": 0,\n  \"timestamp\": \"2025-01-30T12:34:56.789Z\",\n  \"path\": \"/api/v1/core/token-metadata\",\n  \"method\": \"POST\"\n}"}]}], "description": "Core system endpoints for token data and utilities."}, {"name": "📊 KOL Feed API", "item": [{"name": "KOL Feed History (API Key)", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/kol-feed/history?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "kol-feed", "history"], "query": [{"key": "limit", "value": "10", "description": "Number of transactions to return (max 100)"}, {"key": "offset", "value": "0", "description": "Number of transactions to skip"}]}, "description": "Get historical KOL trading data using API Key authentication. Costs 3 credits per request."}, "response": []}], "description": "KOL trading activity data endpoints."}, {"name": "🧠 Smart Money API", "item": [{"name": "Most Bought Tokens", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/daily-trends/most-bought-tokens", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "daily-trends", "most-bought-tokens"]}, "description": "Get most bought tokens by smart money traders over the last 15 days. Includes token metadata, volume data, and ranking information. Costs 2 credits per request. Requires Basic tier or higher."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/daily-trends/most-bought-tokens", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "daily-trends", "most-bought-tokens"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": [\n    {\n      \"date\": \"2025-06-06T00:00:00.000Z\",\n      \"token_address\": \"9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump\",\n      \"buy_volume\": 186862.69698474216,\n      \"sell_volume\": 61808.12228895664,\n      \"net_volume\": 125054.57469578552,\n      \"rank_type\": \"top\",\n      \"rank\": 1,\n      \"top_buyer\": {\n        \"wallet\": \"GnAHnGRFXrU9cSyYMdvjdUc6BzSj4e6qP6Py79BKZgUB\",\n        \"amount\": 167805.8567037489\n      },\n      \"top_seller\": {\n        \"wallet\": \"CubRkB3zzp1ibiiRpKW8vjR7afm5XSSJkg7XKtDX1gBC\",\n        \"amount\": 26636.232022063738\n      },\n      \"volume_change\": -94.87622732035607,\n      \"rank_change\": 0,\n      \"symbol\": \"Fartcoin\",\n      \"name\": \"Fartcoin\",\n      \"decimals\": 6,\n      \"logo\": \"https://ipfs.io/ipfs/QmQr3Fz4h1etNsF7oLGMRHiCzhB5y9a7GjyodnF7zLHK1g\"\n    }\n  ],\n  \"credits_consumed\": 2,\n  \"message\": \"Most bought tokens retrieved successfully\"\n}"}]}, {"name": "Most Sold Tokens", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/daily-trends/most-sold-tokens", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "daily-trends", "most-sold-tokens"]}, "description": "Get most sold tokens by smart money traders over the last 15 days. Includes token metadata, volume data, and ranking information. Costs 2 credits per request. Requires Basic tier or higher."}, "response": []}, {"name": "Daily SOL Flows", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/daily-trends/daily-flows-sol", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "daily-trends", "daily-flows-sol"]}, "description": "Get daily SOL flow data (buy_volume, sell_volume, net_volume) by smart money traders over the last 15 days. Costs 2 credits per request. Requires Basic tier or higher."}, "response": []}, {"name": "Daily Meme Flows", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/daily-trends/daily-flows-meme", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "daily-trends", "daily-flows-meme"]}, "description": "Get daily memecoin flow data (buy_volume, sell_volume, net_volume) by smart money traders over the last 15 days. Costs 2 credits per request. Requires Basic tier or higher."}, "response": []}, {"name": "Top Tokens 24h", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/top-tokens/24h", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "top-tokens", "24h"]}, "description": "Get top performing tokens by smart money traders over the last 24 hours. Costs 2 credits per request. Requires Basic tier or higher."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/top-tokens/24h", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "top-tokens", "24h"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": [\n    {\n      \"timeframe\": \"24h\",\n      \"token_address\": \"YXUpFaULqhrLJS79JmFtAsNZQ2JDTnPemmdVZEFpump\",\n      \"buy_volume\": 12759.542570171594,\n      \"sell_volume\": 6125.2727827169,\n      \"net_volume\": 6634.269787454694,\n      \"rank_type\": \"top\",\n      \"rank\": 1,\n      \"biggest_buyer\": {\n        \"wallet\": \"8rm7k5YqfmSKjDG5ZpXDRoPnRWmCELyF2xQunuTriQ2T\",\n        \"amount\": 12759.542570171594\n      },\n      \"biggest_seller\": {\n        \"wallet\": \"GorNqtHP4Zsd4HrcYTH2VYwU9FxefJZqaAEbzobnq2r1\",\n        \"amount\": 6125.2727827169\n      },\n      \"symbol\": \"PUMP\",\n      \"name\": \"Pump Token\",\n      \"decimals\": 6,\n      \"logo\": \"https://example.com/logo.png\"\n    }\n  ],\n  \"credits_consumed\": 2,\n  \"message\": \"Top tokens 24h retrieved successfully\"\n}"}]}, {"name": "Top Tokens 3d", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/top-tokens/3d", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "top-tokens", "3d"]}, "description": "Get top performing tokens by smart money traders over the last 3 days. Costs 2 credits per request. Requires Basic tier or higher."}, "response": []}, {"name": "Top Tokens 7d", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/top-tokens/7d", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "top-tokens", "7d"]}, "description": "Get top performing tokens by smart money traders over the last 7 days. Costs 2 credits per request. Requires Basic tier or higher."}, "response": []}, {"name": "Bottom Tokens 24h", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/bottom-tokens/24h", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "bottom-tokens", "24h"]}, "description": "Get worst performing tokens by smart money traders over the last 24 hours. Costs 2 credits per request. Requires Basic tier or higher."}, "response": []}, {"name": "Bottom Tokens 3d", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/bottom-tokens/3d", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "bottom-tokens", "3d"]}, "description": "Get worst performing tokens by smart money traders over the last 3 days. Costs 2 credits per request. Requires Basic tier or higher."}, "response": []}, {"name": "Bottom Tokens 7d", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/smart-money/bottom-tokens/7d", "host": ["{{base_url}}"], "path": ["api", "v1", "smart-money", "bottom-tokens", "7d"]}, "description": "Get worst performing tokens by smart money traders over the last 7 days. Costs 2 credits per request. Requires Basic tier or higher."}, "response": []}], "description": "Smart money trading insights and analytics endpoints. All endpoints require Basic tier or higher and consume 2 credits per request."}, {"name": "🔌 WebSocket API", "item": [{"name": "WebSocket Info", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/info", "host": ["{{base_url}}"], "path": ["ws-api", "info"]}, "description": "Get WebSocket server connection details and status."}, "response": []}, {"name": "Available Streams", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/streams", "host": ["{{base_url}}"], "path": ["ws-api", "streams"]}, "description": "Get list of available WebSocket streams for user's tier."}, "response": []}, {"name": "Active Sessions", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/sessions", "host": ["{{base_url}}"], "path": ["ws-api", "sessions"]}, "description": "Get information about user's active WebSocket sessions."}, "response": []}, {"name": "Usage Statistics", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/stats", "host": ["{{base_url}}"], "path": ["ws-api", "stats"]}, "description": "Get WebSocket usage statistics for the authenticated user."}, "response": []}, {"name": "Stream Credits Info", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/credits?stream=kol-feed", "host": ["{{base_url}}"], "path": ["ws-api", "credits"], "query": [{"key": "stream", "value": "kol-feed", "description": "Specific stream to get credit info for"}]}, "description": "Get credit costs and information for WebSocket streams."}, "response": []}, {"name": "Stream Credit Usage Stats", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/credits/stats?days=7&stream=kol-feed", "host": ["{{base_url}}"], "path": ["ws-api", "credits", "stats"], "query": [{"key": "days", "value": "7", "description": "Number of days to get statistics for"}, {"key": "stream", "value": "kol-feed", "description": "Specific stream to get statistics for"}]}, "description": "Get detailed credit usage statistics for WebSocket streams."}, "response": []}], "description": "WebSocket server information and stream management endpoints."}], "variable": [{"key": "base_url", "value": "http://localhost:3001", "type": "string"}, {"key": "api_key", "value": "2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG", "type": "string"}, {"key": "demo_email", "value": "<EMAIL>", "type": "string"}, {"key": "demo_password", "value": "demo123", "type": "string"}]}