{"id": "12345678-1234-1234-1234-123456789def", "name": "StalkAPI Environment", "values": [{"key": "base_url", "value": "http://localhost:3001", "description": "Base URL for the API. Change to https://data.stalkapi.com for production", "enabled": true, "type": "default"}, {"key": "api_key", "value": "demo_api_key_12345", "description": "Demo user API key for testing", "enabled": true, "type": "default"}, {"key": "user_id", "value": "", "description": "User ID (automatically set after login)", "enabled": true, "type": "default"}, {"key": "demo_email", "value": "<EMAIL>", "description": "Demo user email", "enabled": true, "type": "default"}, {"key": "websocket_url", "value": "ws://localhost:3001/ws", "description": "WebSocket URL for real-time streaming. Change to wss://data.stalkapi.com/ws for production", "enabled": true, "type": "default"}, {"key": "admin_api_key", "value": "admin_api_key_super_secure_change_in_production", "description": "Admin API key for backend/admin access", "enabled": true, "type": "secret"}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-01T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}