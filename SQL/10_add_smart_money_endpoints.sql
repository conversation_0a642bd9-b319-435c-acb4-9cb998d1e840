-- Migration to add Smart Money endpoints to access tiers
-- This migration adds Smart Money Daily Trends endpoints to appropriate tiers

-- Add Smart Money - Daily Trends - Most Bought Tokens endpoint to tiers (excluding free)
DO $$ 
BEGIN
    -- Add endpoint to basic tier
    UPDATE access_tiers 
    SET allowed_endpoints = array_append(allowed_endpoints, '/api/v1/smart-money/daily-trends/most-bought-tokens')
    WHERE name = 'basic' 
    AND NOT ('/api/v1/smart-money/daily-trends/most-bought-tokens' = ANY(allowed_endpoints));
    
    -- Add endpoint to premium tier
    UPDATE access_tiers 
    SET allowed_endpoints = array_append(allowed_endpoints, '/api/v1/smart-money/daily-trends/most-bought-tokens')
    WHERE name = 'premium' 
    AND NOT ('/api/v1/smart-money/daily-trends/most-bought-tokens' = ANY(allowed_endpoints));
    
    -- Add endpoint to enterprise tier (if it doesn't already have * access)
    UPDATE access_tiers 
    SET allowed_endpoints = array_append(allowed_endpoints, '/api/v1/smart-money/daily-trends/most-bought-tokens')
    WHERE name = 'enterprise' 
    AND NOT ('*' = ANY(allowed_endpoints))
    AND NOT ('/api/v1/smart-money/daily-trends/most-bought-tokens' = ANY(allowed_endpoints));

    RAISE NOTICE 'Added Smart Money Daily Trends Most Bought Tokens endpoint to basic, premium, and enterprise tiers';
END $$;

-- Display updated tier configuration for verification
SELECT 
    name,
    description,
    max_credits_per_month,
    price_per_month,
    allowed_endpoints,
    is_enabled
FROM access_tiers 
WHERE name IN ('basic', 'premium', 'enterprise')
ORDER BY 
    CASE name 
        WHEN 'basic' THEN 1 
        WHEN 'premium' THEN 2 
        WHEN 'enterprise' THEN 3 
        ELSE 4 
    END;
