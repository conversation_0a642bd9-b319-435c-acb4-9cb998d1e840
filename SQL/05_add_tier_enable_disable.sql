-- Migration to add tier enable/disable functionality
-- This migration adds the is_enabled column to access_tiers table

-- Add is_enabled column to access_tiers table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'access_tiers' AND column_name = 'is_enabled'
    ) THEN
        ALTER TABLE access_tiers ADD COLUMN is_enabled BOOLEAN DEFAULT true;
        
        -- Add index for the new column
        CREATE INDEX IF NOT EXISTS idx_access_tiers_enabled ON access_tiers(is_enabled);
        CREATE INDEX IF NOT EXISTS idx_access_tiers_enabled_name ON access_tiers(is_enabled, name);
        
        -- Update existing tiers - disable free tier, enable others
        UPDATE access_tiers SET is_enabled = false WHERE name = 'free';
        UPDATE access_tiers SET is_enabled = true WHERE name != 'free';
        
        -- Update free tier description
        UPDATE access_tiers SET description = 'Free tier with basic access (currently disabled)' WHERE name = 'free';
        
        RAISE NOTICE 'Added is_enabled column to access_tiers table and updated tier status';
    ELSE
        RAISE NOTICE 'is_enabled column already exists in access_tiers table';
    END IF;
END $$;

-- Update demo user to use basic tier (tier_id = 2) if currently using free tier (tier_id = 1)
DO $$
BEGIN
    -- Check if demo user exists and is using free tier
    IF EXISTS (
        SELECT 1 FROM users 
        WHERE email = '<EMAIL>' AND tier_id = 1
    ) THEN
        -- Update demo user to basic tier and give appropriate credits
        UPDATE users 
        SET tier_id = 2, credits_remaining = 10000 
        WHERE email = '<EMAIL>' AND tier_id = 1;
        
        RAISE NOTICE 'Updated demo user from free tier to basic tier';
    ELSE
        RAISE NOTICE 'Demo user not found or not using free tier';
    END IF;
END $$;
