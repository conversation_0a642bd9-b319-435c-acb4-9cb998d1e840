-- Migration to add KOL feed history endpoint to access tiers
-- This migration adds the /api/v1/kol-feed/history endpoint to allowed_endpoints for appropriate tiers

-- Add KOL feed history endpoint to tiers that have access to kol-feed stream
DO $$ 
BEGIN
    -- Add endpoint to basic tier (if it has kol-feed stream access)
    IF EXISTS (
        SELECT 1 FROM access_tiers 
        WHERE name = 'basic' AND 'kol-feed' = ANY(allowed_streams)
    ) THEN
        UPDATE access_tiers 
        SET allowed_endpoints = array_append(allowed_endpoints, '/api/v1/kol-feed/history')
        WHERE name = 'basic' 
        AND NOT ('/api/v1/kol-feed/history' = ANY(allowed_endpoints));
        
        RAISE NOTICE 'Added KOL feed history endpoint to basic tier';
    END IF;

    -- Add endpoint to premium tier (if it has kol-feed stream access)
    IF EXISTS (
        SELECT 1 FROM access_tiers 
        WHERE name = 'premium' AND 'kol-feed' = ANY(allowed_streams)
    ) THEN
        UPDATE access_tiers 
        SET allowed_endpoints = array_append(allowed_endpoints, '/api/v1/kol-feed/history')
        WHERE name = 'premium' 
        AND NOT ('/api/v1/kol-feed/history' = ANY(allowed_endpoints));
        
        RAISE NOTICE 'Added KOL feed history endpoint to premium tier';
    END IF;

    -- Add endpoint to enterprise tier (if it has kol-feed stream access)
    IF EXISTS (
        SELECT 1 FROM access_tiers 
        WHERE name = 'enterprise' AND 'kol-feed' = ANY(allowed_streams)
    ) THEN
        UPDATE access_tiers 
        SET allowed_endpoints = array_append(allowed_endpoints, '/api/v1/kol-feed/history')
        WHERE name = 'enterprise' 
        AND NOT ('/api/v1/kol-feed/history' = ANY(allowed_endpoints));
        
        RAISE NOTICE 'Added KOL feed history endpoint to enterprise tier';
    END IF;

    RAISE NOTICE 'KOL feed history endpoint migration completed';
END $$;
