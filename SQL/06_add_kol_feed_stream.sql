-- Migration to add KOL feed stream
-- This migration adds the KOL feed stream to the stream_definitions table

-- Add KOL feed stream if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM stream_definitions WHERE stream_name = 'kol-feed'
    ) THEN
        -- Insert KOL feed stream with basic tier access
        INSERT INTO stream_definitions (
            stream_name,
            description,
            required_tier_id,
            credits_per_message,
            max_subscribers,
            is_active,
            metadata
        ) VALUES (
            'kol-feed',
            'Real-time KOL (Key Opinion Leader) trading activity feed',
            2, -- basic tier
            2, -- 2 credits per message
            500, -- max subscribers
            true,
            '{"type": "event-driven", "source": "kol-feed", "format": "json"}'
        );

        -- Add kol-feed to basic tier's allowed streams
        UPDATE access_tiers 
        SET allowed_streams = array_append(allowed_streams, 'kol-feed')
        WHERE name = 'basic';

        -- Add kol-feed to premium tier's allowed streams
        UPDATE access_tiers 
        SET allowed_streams = array_append(allowed_streams, 'kol-feed')
        WHERE name = 'premium';

        -- Add kol-feed to enterprise tier's allowed streams
        UPDATE access_tiers 
        SET allowed_streams = array_append(allowed_streams, 'kol-feed')
        WHERE name = 'enterprise';

        RAISE NOTICE 'Added KOL feed stream and updated tier permissions';
    ELSE
        RAISE NOTICE 'KOL feed stream already exists';
    END IF;
END $$; 