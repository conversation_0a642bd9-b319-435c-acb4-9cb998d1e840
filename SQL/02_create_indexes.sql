-- Indexes for Performance Optimization
-- These indexes will significantly improve query performance

-- Users table indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_api_key ON users(api_key);
CREATE INDEX idx_users_tier_id ON users(tier_id);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);

-- API Usage Logs indexes
CREATE INDEX idx_api_usage_logs_user_id ON api_usage_logs(user_id);
CREATE INDEX idx_api_usage_logs_endpoint ON api_usage_logs(endpoint);
CREATE INDEX idx_api_usage_logs_created_at ON api_usage_logs(created_at);
CREATE INDEX idx_api_usage_logs_user_endpoint ON api_usage_logs(user_id, endpoint);
CREATE INDEX idx_api_usage_logs_user_created ON api_usage_logs(user_id, created_at);

-- WebSocket Sessions indexes
CREATE INDEX idx_websocket_sessions_user_id ON websocket_sessions(user_id);
CREATE INDEX idx_websocket_sessions_session_id ON websocket_sessions(session_id);
CREATE INDEX idx_websocket_sessions_connection_id ON websocket_sessions(connection_id);
CREATE INDEX idx_websocket_sessions_connected_at ON websocket_sessions(connected_at);
CREATE INDEX idx_websocket_sessions_active ON websocket_sessions(user_id) WHERE disconnected_at IS NULL;

-- Credit Transactions indexes
CREATE INDEX idx_credit_transactions_user_id ON credit_transactions(user_id);
CREATE INDEX idx_credit_transactions_type ON credit_transactions(transaction_type);
CREATE INDEX idx_credit_transactions_created_at ON credit_transactions(created_at);
CREATE INDEX idx_credit_transactions_user_type ON credit_transactions(user_id, transaction_type);

-- Rate Limits indexes
CREATE INDEX idx_rate_limits_user_endpoint ON rate_limits(user_id, endpoint);
CREATE INDEX idx_rate_limits_window ON rate_limits(window_start, window_end);
CREATE INDEX idx_rate_limits_user_window ON rate_limits(user_id, window_start, window_end);

-- API Keys indexes
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX idx_api_keys_api_key ON api_keys(api_key);
CREATE INDEX idx_api_keys_is_active ON api_keys(is_active);
CREATE INDEX idx_api_keys_expires_at ON api_keys(expires_at);

-- Stream Definitions indexes
CREATE INDEX idx_stream_definitions_name ON stream_definitions(stream_name);
CREATE INDEX idx_stream_definitions_tier ON stream_definitions(required_tier_id);
CREATE INDEX idx_stream_definitions_active ON stream_definitions(is_active);

-- Access Tiers indexes
CREATE INDEX idx_access_tiers_enabled ON access_tiers(is_enabled);
CREATE INDEX idx_access_tiers_name ON access_tiers(name);
CREATE INDEX idx_access_tiers_enabled_name ON access_tiers(is_enabled, name);

-- Composite indexes for common queries
CREATE INDEX idx_users_active_tier ON users(is_active, tier_id);
CREATE INDEX idx_api_usage_user_created ON api_usage_logs(user_id, created_at);
CREATE INDEX idx_websocket_active_sessions ON websocket_sessions(user_id, connected_at) WHERE disconnected_at IS NULL;
