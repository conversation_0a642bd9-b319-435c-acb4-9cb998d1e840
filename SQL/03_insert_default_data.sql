-- Default Data for API Engine
-- Insert default access tiers and sample data

-- Insert default access tiers
INSERT INTO access_tiers (name, description, max_credits_per_month, max_requests_per_minute, max_websocket_connections, allowed_endpoints, allowed_streams, price_per_month, is_enabled) VALUES
('free', 'Free tier with basic access (currently disabled)', 1000, 10, 1, ARRAY['/api/v1/demo', '/api/v1/status'], ARRAY['demo-stream'], 0.00, false),
('basic', 'Basic tier with moderate access', 1000000, 60, 3, ARRAY['/api/v1/demo', '/api/v1/status', '/api/v1/data'], ARRAY['demo-stream', 'data-stream'], 49.99, true),
('premium', 'Premium tier with high access', 5000000, 300, 5, ARRAY['/api/v1/demo', '/api/v1/status', '/api/v1/data', '/api/v1/analytics'], ARRAY['demo-stream', 'data-stream', 'analytics-stream'], 149.99, true),
('enterprise', 'Enterprise tier with unlimited access', -1, 1000, 10, ARRAY['*'], ARRAY['*'], 499.99, true);

-- Insert default stream definitions
INSERT INTO stream_definitions (stream_name, description, required_tier_id, credits_per_message, max_subscribers) VALUES
('demo-stream', 'Demo stream for testing purposes', 1, 1, 100),
('data-stream', 'Real-time data stream', 2, 2, 500),
('analytics-stream', 'Advanced analytics stream', 3, 5, 200),
('enterprise-stream', 'Enterprise-only high-frequency stream', 4, 1, 1000);

-- Create a demo user for testing (password: 'demo123')
-- Note: In production, this should be created through the API with proper password hashing
-- Using tier_id = 2 (basic) since free tier is disabled
INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining) VALUES
('<EMAIL>', '$2a$10$rOzJqQZQZQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZOzJqQZQZQZQZQ', 'demo_api_key_12345', 2, 10000);

-- Create admin users table for admin access control
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    permissions TEXT[] DEFAULT '{}', -- Array of permissions like ['tiers:read', 'tiers:write', 'users:read']
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP WITH TIME ZONE
);

-- Create indexes for admin_users
CREATE INDEX IF NOT EXISTS idx_admin_users_api_key ON admin_users(api_key);
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_users_active ON admin_users(is_active);

-- Insert default admin user
INSERT INTO admin_users (name, email, api_key, permissions) VALUES
('System Admin', '<EMAIL>', 'admin_api_key_super_secure_change_in_production',
 ARRAY['tiers:read', 'tiers:write', 'users:read', 'users:write', 'analytics:read', 'system:admin']);

-- Note: The password hash above is just a placeholder. In the actual implementation,
-- we'll use bcryptjs to properly hash passwords.
