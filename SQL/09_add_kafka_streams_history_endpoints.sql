-- Migration to add Kafka streams history endpoints to access tiers
-- This migration adds the Kafka stream history endpoints to allowed_endpoints for enterprise tier

-- Add Kafka stream history endpoints to enterprise tier
DO $$ 
BEGIN
    -- Add Jupiter AMM swaps history endpoint to enterprise tier
    UPDATE access_tiers 
    SET allowed_endpoints = array_append(allowed_endpoints, '/api/v1/jupiter-amm-swaps/history')
    WHERE name = 'enterprise' 
    AND NOT ('/api/v1/jupiter-amm-swaps/history' = ANY(allowed_endpoints));
    
    -- Add Pump.fun AMM swaps history endpoint to enterprise tier
    UPDATE access_tiers 
    SET allowed_endpoints = array_append(allowed_endpoints, '/api/v1/pumpfun-amm-swaps/history')
    WHERE name = 'enterprise' 
    AND NOT ('/api/v1/pumpfun-amm-swaps/history' = ANY(allowed_endpoints));
    
    -- Add Jupiter DCA orders history endpoint to enterprise tier
    UPDATE access_tiers 
    SET allowed_endpoints = array_append(allowed_endpoints, '/api/v1/jupiter-dca-orders/history')
    WHERE name = 'enterprise' 
    AND NOT ('/api/v1/jupiter-dca-orders/history' = ANY(allowed_endpoints));

    RAISE NOTICE 'Added Kafka streams history endpoints to enterprise tier';
END $$;
