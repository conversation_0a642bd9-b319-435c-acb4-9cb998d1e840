-- Database Functions and Triggers for API Engine
-- These functions will help with automatic updates and data integrity

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_access_tiers_updated_at BEFORE UPDATE ON access_tiers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to consume credits and log usage
CREATE OR REPLACE FUNCTION consume_credits(
    p_user_id UUID,
    p_endpoint VARCHAR(255),
    p_method VARCHAR(10),
    p_credits INTEGER DEFAULT 1,
    p_response_status INTEGER DEFAULT NULL,
    p_response_time_ms INTEGER DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_request_payload JSONB DEFAULT NULL,
    p_response_payload JSONB DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_credits INTEGER;
    tier_max_credits INTEGER;
BEGIN
    -- Get current credits and tier limits
    SELECT u.credits_remaining, at.max_credits_per_month
    INTO current_credits, tier_max_credits
    FROM users u
    JOIN access_tiers at ON u.tier_id = at.id
    WHERE u.id = p_user_id AND u.is_active = true;
    
    -- Check if user exists and is active
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Check if user has enough credits (unlimited tier has -1)
    IF tier_max_credits != -1 AND current_credits < p_credits THEN
        RETURN FALSE;
    END IF;
    
    -- Consume credits (only if not unlimited tier)
    IF tier_max_credits != -1 THEN
        UPDATE users 
        SET credits_remaining = credits_remaining - p_credits,
            credits_used_this_month = credits_used_this_month + p_credits
        WHERE id = p_user_id;
    END IF;
    
    -- Log the usage
    INSERT INTO api_usage_logs (
        user_id, endpoint, method, credits_consumed, response_status,
        response_time_ms, ip_address, user_agent, request_payload, response_payload
    ) VALUES (
        p_user_id, p_endpoint, p_method, p_credits, p_response_status,
        p_response_time_ms, p_ip_address, p_user_agent, p_request_payload, p_response_payload
    );
    
    -- Log credit transaction
    INSERT INTO credit_transactions (user_id, transaction_type, credits_amount, description)
    VALUES (p_user_id, 'usage', -p_credits, 'API usage: ' || p_method || ' ' || p_endpoint);
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to add credits to user account
CREATE OR REPLACE FUNCTION add_credits(
    p_user_id UUID,
    p_credits INTEGER,
    p_transaction_type VARCHAR(20) DEFAULT 'purchase',
    p_description TEXT DEFAULT NULL,
    p_reference_id VARCHAR(255) DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Add credits to user account
    UPDATE users 
    SET credits_remaining = credits_remaining + p_credits,
        total_credits_purchased = total_credits_purchased + p_credits
    WHERE id = p_user_id AND is_active = true;
    
    -- Check if user exists and is active
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Log credit transaction
    INSERT INTO credit_transactions (
        user_id, transaction_type, credits_amount, description, reference_id
    ) VALUES (
        p_user_id, p_transaction_type, p_credits, p_description, p_reference_id
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to reset monthly credits (to be called monthly)
CREATE OR REPLACE FUNCTION reset_monthly_credits()
RETURNS INTEGER AS $$
DECLARE
    reset_count INTEGER := 0;
BEGIN
    UPDATE users 
    SET credits_used_this_month = 0
    WHERE is_active = true;
    
    GET DIAGNOSTICS reset_count = ROW_COUNT;
    RETURN reset_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean old logs (for maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_logs(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    DELETE FROM api_usage_logs 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
