-- Cleanup Test/Demo Endpoints and Streams
-- This migration removes all test endpoints and streams, keeping only KOL feed functionality

-- Remove test endpoints from all access tiers
UPDATE access_tiers 
SET allowed_endpoints = array_remove(
    array_remove(
        array_remove(
            array_remove(
                array_remove(
                    array_remove(allowed_endpoints, '/api/v1/demo'),
                    '/api/v1/data'
                ),
                '/api/v1/analytics'
            ),
            '/api/v1/search'
        ),
        '/api/v1/submit'
    ),
    '/api/v1/batch'
);

-- Remove test streams from all access tiers
UPDATE access_tiers 
SET allowed_streams = array_remove(
    array_remove(
        array_remove(
            array_remove(allowed_streams, 'demo-stream'),
            'data-stream'
        ),
        'analytics-stream'
    ),
    'enterprise-stream'
);

-- Add KOL feed stream and history endpoint to appropriate tiers
UPDATE access_tiers 
SET allowed_streams = array_append(allowed_streams, 'kol-feed')
WHERE name IN ('basic', 'premium', 'enterprise') 
AND NOT ('kol-feed' = ANY(allowed_streams));

UPDATE access_tiers 
SET allowed_endpoints = array_append(allowed_endpoints, '/api/v1/kol-feed/history')
WHERE name IN ('basic', 'premium', 'enterprise') 
AND NOT ('/api/v1/kol-feed/history' = ANY(allowed_endpoints));

-- Remove test stream definitions
DELETE FROM stream_definitions 
WHERE stream_name IN ('demo-stream', 'data-stream', 'analytics-stream', 'enterprise-stream');

-- Add KOL feed stream definition
INSERT INTO stream_definitions (stream_name, description, required_tier_id, credits_per_message, max_subscribers) 
VALUES ('kol-feed', 'Real-time KOL trading activity stream', 2, 2, 1000)
ON CONFLICT (stream_name) DO UPDATE SET
    description = EXCLUDED.description,
    required_tier_id = EXCLUDED.required_tier_id,
    credits_per_message = EXCLUDED.credits_per_message,
    max_subscribers = EXCLUDED.max_subscribers;

-- Clean up any API usage logs for removed endpoints (optional - keeps historical data)
-- Uncomment the following lines if you want to remove historical test data:
-- DELETE FROM api_usage_logs 
-- WHERE endpoint IN ('/api/v1/demo', '/api/v1/data', '/api/v1/analytics', '/api/v1/search', '/api/v1/submit', '/api/v1/batch');

-- Update tier descriptions to reflect real functionality
UPDATE access_tiers SET 
    description = 'Free tier with basic access (currently disabled)',
    allowed_endpoints = ARRAY['/api/v1/status'],
    allowed_streams = ARRAY[]::TEXT[]
WHERE name = 'free';

UPDATE access_tiers SET 
    description = 'Basic tier with KOL feed access',
    allowed_endpoints = ARRAY['/api/v1/status', '/api/v1/kol-feed/history'],
    allowed_streams = ARRAY['kol-feed']
WHERE name = 'basic';

UPDATE access_tiers SET 
    description = 'Premium tier with enhanced KOL feed access',
    allowed_endpoints = ARRAY['/api/v1/status', '/api/v1/kol-feed/history'],
    allowed_streams = ARRAY['kol-feed']
WHERE name = 'premium';

UPDATE access_tiers SET 
    description = 'Enterprise tier with full KOL feed access',
    allowed_endpoints = ARRAY['*'],
    allowed_streams = ARRAY['*']
WHERE name = 'enterprise';

-- Display updated tier configuration
SELECT 
    name,
    description,
    max_credits_per_month,
    price_per_month,
    allowed_endpoints,
    allowed_streams,
    is_enabled
FROM access_tiers 
ORDER BY 
    CASE name 
        WHEN 'free' THEN 1 
        WHEN 'basic' THEN 2 
        WHEN 'premium' THEN 3 
        WHEN 'enterprise' THEN 4 
        ELSE 5 
    END;
