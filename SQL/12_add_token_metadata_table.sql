-- Token Metadata Caching Table
-- Stores token metadata from GeckoTerminal API for caching purposes

CREATE TABLE token_metadata (
    id SERIAL PRIMARY KEY,
    token_address VARCHAR(255) UNIQUE NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255),
    symbol VARCHAR(50),
    decimals INTEGER,
    logo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for fast lookups by token address
CREATE INDEX idx_token_metadata_address ON token_metadata(token_address);

-- Create index for updated_at for cache invalidation queries
CREATE INDEX idx_token_metadata_updated_at ON token_metadata(updated_at);

-- Add trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_token_metadata_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_token_metadata_updated_at
    BEFORE UPDATE ON token_metadata
    FOR EACH ROW
    EXECUTE FUNCTION update_token_metadata_updated_at();

-- Add comment to table
COMMENT ON TABLE token_metadata IS 'Caches token metadata from GeckoTerminal API to reduce external API calls';
COMMENT ON COLUMN token_metadata.token_address IS 'Unique token address (Solana format)';
COMMENT ON COLUMN token_metadata.name IS 'Token name from GeckoTerminal';
COMMENT ON COLUMN token_metadata.symbol IS 'Token symbol from GeckoTerminal';
COMMENT ON COLUMN token_metadata.decimals IS 'Token decimals from GeckoTerminal';
COMMENT ON COLUMN token_metadata.logo IS 'Token logo URL from GeckoTerminal';
