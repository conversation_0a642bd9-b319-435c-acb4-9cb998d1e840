#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to validate and format the OpenAPI JSON specification
 * Run this script to validate the OpenAPI spec and ensure proper formatting
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const main = () => {
    try {
        console.log('🔄 Validating OpenAPI JSON specification...');

        // Path to JSON file
        const jsonPath = path.join(__dirname, '../src/docs/openapi.json');

        // Check if JSON file exists
        if (!fs.existsSync(jsonPath)) {
            console.error('❌ Error: openapi.json not found in src/docs/');
            process.exit(1);
        }

        // Read and parse JSON file
        const jsonContent = fs.readFileSync(jsonPath, 'utf8');
        const spec = JSON.parse(jsonContent);

        // Basic validation
        if (!spec.openapi) {
            throw new Error('Missing openapi version field');
        }

        if (!spec.info || !spec.info.title) {
            throw new Error('Missing info.title field');
        }

        if (!spec.paths || Object.keys(spec.paths).length === 0) {
            throw new Error('No paths defined in specification');
        }

        // Reformat and write back (ensures consistent formatting)
        fs.writeFileSync(jsonPath, JSON.stringify(spec, null, 2));

        // Get file size
        const jsonSize = fs.statSync(jsonPath).size;

        console.log('✅ OpenAPI specification is valid!');
        console.log(`📄 JSON size: ${(jsonSize / 1024).toFixed(1)} KB`);
        console.log(`📍 File: ${jsonPath}`);
        console.log(`🏷️  Title: ${spec.info.title}`);
        console.log(`📊 Endpoints: ${Object.keys(spec.paths).length}`);
        console.log(`🏷️  Tags: ${spec.tags ? spec.tags.length : 0}`);

    } catch (error) {
        console.error('❌ Error validating OpenAPI spec:', error.message);
        process.exit(1);
    }
};

main();
