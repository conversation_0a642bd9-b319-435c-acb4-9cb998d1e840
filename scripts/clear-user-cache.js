import { cache } from '../src/config/redis.js';
import { query } from '../src/config/database.js';

/**
 * Clear User Cache Script
 * 
 * This script clears cached user data from Redis to force fresh database lookups.
 * Use this after making direct database changes to:
 * - access_tiers.allowed_endpoints
 * - users.tier_id
 * - access_tiers configuration
 * 
 * Usage:
 * - Clear all user caches: pnpm clear-cache
 * - Clear specific user: pnpm clear-cache <EMAIL>
 * - Clear by API key: pnpm clear-cache sk_test_123...
 */

async function clearUserCache() {
    try {
        const targetUser = process.argv[2]; // Optional: specific user email or API key
        
        console.log('🧹 Starting user cache cleanup...\n');
        
        if (targetUser) {
            // Clear specific user cache
            await clearSpecificUserCache(targetUser);
        } else {
            // Clear all user caches
            await clearAllUserCaches();
        }
        
        console.log('\n✅ User cache cleanup completed successfully!');
        console.log('💡 Users will get fresh permissions on their next API request.');
        
        process.exit(0);
        
    } catch (error) {
        console.error('❌ Cache cleanup failed:', error);
        process.exit(1);
    }
}

async function clearSpecificUserCache(identifier) {
    console.log(`🎯 Clearing cache for specific user: ${identifier}`);
    
    let apiKey = identifier;
    
    // If identifier looks like an email, find the API key
    if (identifier.includes('@')) {
        console.log('📧 Identifier appears to be an email, looking up API key...');
        
        const result = await query(
            'SELECT api_key FROM users WHERE email = $1',
            [identifier]
        );
        
        if (result.rows.length === 0) {
            console.log(`❌ User not found with email: ${identifier}`);
            return;
        }
        
        apiKey = result.rows[0].api_key;
        console.log(`🔑 Found API key: ${apiKey.substring(0, 10)}...`);
    }
    
    // Clear the user cache
    const cacheKey = `apikey:${apiKey}`;
    const deleted = await cache.del(cacheKey);
    
    if (deleted > 0) {
        console.log(`✅ Cleared cache for user: ${identifier}`);
        console.log(`🗑️  Deleted cache key: ${cacheKey}`);
    } else {
        console.log(`⚠️  No cache found for user: ${identifier}`);
        console.log(`🔍 Checked cache key: ${cacheKey}`);
    }
}

async function clearAllUserCaches() {
    console.log('🌍 Clearing all user caches...');
    
    // Get all API keys from database
    const result = await query(
        'SELECT email, api_key FROM users WHERE is_active = true ORDER BY email'
    );
    
    console.log(`📊 Found ${result.rows.length} active users`);
    
    if (result.rows.length === 0) {
        console.log('⚠️  No active users found in database');
        return;
    }
    
    let clearedCount = 0;
    let notFoundCount = 0;
    
    for (const user of result.rows) {
        const cacheKey = `apikey:${user.api_key}`;
        const deleted = await cache.del(cacheKey);
        
        if (deleted > 0) {
            console.log(`  ✅ ${user.email} (${user.api_key.substring(0, 10)}...)`);
            clearedCount++;
        } else {
            console.log(`  ⚪ ${user.email} (no cache found)`);
            notFoundCount++;
        }
    }
    
    console.log(`\n📊 Cache cleanup summary:`);
    console.log(`  🗑️  Cleared: ${clearedCount} user caches`);
    console.log(`  ⚪ Not cached: ${notFoundCount} users`);
    console.log(`  📈 Total users: ${result.rows.length}`);
}

async function clearTierCaches() {
    console.log('🏷️  Clearing tier-related caches...');
    
    // Clear any tier-specific caches that might exist
    const tierCachePatterns = [
        'tier:*',
        'access_tier:*',
        'user_tier:*'
    ];
    
    for (const pattern of tierCachePatterns) {
        try {
            const keys = await cache.keys(pattern);
            if (keys.length > 0) {
                const deleted = await cache.del(...keys);
                console.log(`  🗑️  Cleared ${deleted} tier cache keys matching: ${pattern}`);
            } else {
                console.log(`  ⚪ No cache keys found for pattern: ${pattern}`);
            }
        } catch (error) {
            console.log(`  ⚠️  Error clearing pattern ${pattern}:`, error.message);
        }
    }
}

async function showCacheStats() {
    console.log('\n📊 Current cache statistics:');
    
    try {
        // Get Redis info
        const info = await cache.info('memory');
        const memoryLines = info.split('\r\n').filter(line => 
            line.includes('used_memory_human') || 
            line.includes('used_memory_peak_human') ||
            line.includes('keyspace')
        );
        
        memoryLines.forEach(line => {
            if (line.trim()) {
                console.log(`  📈 ${line}`);
            }
        });
        
        // Count user cache keys
        const userCacheKeys = await cache.keys('apikey:*');
        console.log(`  👥 Active user caches: ${userCacheKeys.length}`);
        
    } catch (error) {
        console.log('  ⚠️  Could not retrieve cache stats:', error.message);
    }
}

// Enhanced version that also clears tier caches
async function clearAllCaches() {
    await clearAllUserCaches();
    await clearTierCaches();
    await showCacheStats();
}

// Main execution
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🧹 Clear User Cache Script

Usage:
  pnpm clear-cache                    # Clear all user caches
  pnpm clear-cache <EMAIL>   # Clear specific user by email
  pnpm clear-cache sk_test_123...     # Clear specific user by API key
  pnpm clear-cache --help             # Show this help

When to use:
  - After changing access_tiers.allowed_endpoints in database
  - After updating users.tier_id directly in database  
  - After modifying access_tiers configuration
  - When users report stale permissions

Examples:
  pnpm clear-cache                           # Clear all
  pnpm clear-cache <EMAIL>          # Clear by email
  pnpm clear-cache 2fF24O8HUvmHYAOUz...      # Clear by API key
`);
    process.exit(0);
}

// Run the cache clearing
clearUserCache();
