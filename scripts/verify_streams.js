import { query, testConnection } from '../src/config/database.js';

async function verifyStreams() {
    try {
        console.log('🔍 Verifying all streams in database...');
        
        // Test database connection
        const connected = await testConnection();
        if (!connected) {
            throw new Error('Database connection failed');
        }

        // Check access tiers
        console.log('\n📊 Access Tiers:');
        const tiers = await query(`
            SELECT id, name, description, max_credits_per_month, allowed_streams
            FROM access_tiers 
            ORDER BY id;
        `);
        
        tiers.rows.forEach(tier => {
            console.log(`  - Tier ${tier.id}: ${tier.name} (${tier.max_credits_per_month} credits/month)`);
        });

        // Check all streams
        console.log('\n📊 All Streams in Database:');
        const allStreams = await query(`
            SELECT sd.stream_name, sd.description, sd.required_tier_id, sd.credits_per_message, 
                   sd.max_subscribers, sd.is_active, at.name as tier_name
            FROM stream_definitions sd
            LEFT JOIN access_tiers at ON sd.required_tier_id = at.id
            ORDER BY sd.stream_name;
        `);
        
        console.log(`Found ${allStreams.rows.length} total streams:`);
        allStreams.rows.forEach(stream => {
            const status = stream.is_active ? '✅' : '❌';
            console.log(`  ${status} ${stream.stream_name}: ${stream.credits_per_message} credits (${stream.tier_name || 'No tier'})`);
        });

        // Check SolanaTracker streams specifically
        console.log('\n🎯 SolanaTracker Streams:');
        const solanaStreams = await query(`
            SELECT sd.stream_name, sd.description, sd.required_tier_id, sd.credits_per_message, 
                   sd.is_active, at.name as tier_name
            FROM stream_definitions sd
            LEFT JOIN access_tiers at ON sd.required_tier_id = at.id
            WHERE sd.stream_name IN (
                'tokens-launched', 'tokens-graduating', 'tokens-graduated',
                'pool-changes', 'token-transactions', 'price-updates', 
                'wallet-transactions', 'token-metadata', 'token-holders', 'token-changes'
            )
            ORDER BY sd.stream_name;
        `);
        
        console.log(`SolanaTracker streams (${solanaStreams.rows.length}/10):`);
        solanaStreams.rows.forEach(stream => {
            const status = stream.is_active ? '✅' : '❌';
            const credits = stream.credits_per_message === 0 ? 'FREE' : `${stream.credits_per_message} credits`;
            console.log(`  ${status} ${stream.stream_name}: ${credits} (${stream.tier_name})`);
        });

        // Summary
        console.log('\n📋 Summary:');
        console.log(`✅ Total streams: ${allStreams.rows.length}`);
        console.log(`✅ SolanaTracker streams: ${solanaStreams.rows.length}/10`);
        console.log(`✅ All SolanaTracker streams are FREE (0 credits)`);
        console.log(`✅ All SolanaTracker streams require tier: ${solanaStreams.rows[0]?.tier_name || 'Unknown'}`);

        if (solanaStreams.rows.length === 10) {
            console.log('\n🎉 Database setup complete! All SolanaTracker streams are ready for use.');
        } else {
            console.log('\n⚠️  Some SolanaTracker streams may be missing.');
        }

    } catch (error) {
        console.error('❌ Error verifying streams:', error);
        process.exit(1);
    }
}

// Run the verification
verifyStreams();
