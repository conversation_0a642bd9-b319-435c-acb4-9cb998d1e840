import { query } from '../src/config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runSmartMoneyMigration() {
    try {
        console.log('🚀 Running Smart Money endpoints migration...');
        
        const sqlPath = path.join(__dirname, '../SQL/10_add_smart_money_endpoints.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');
        
        await query(sql);
        
        console.log('✅ Migration completed successfully!');
        console.log('📊 Checking updated tiers...');
        
        const tiersResult = await query(`
            SELECT name, allowed_endpoints, is_enabled
            FROM access_tiers 
            WHERE name IN ('basic', 'premium', 'enterprise')
            ORDER BY 
                CASE name 
                    WHEN 'basic' THEN 1 
                    WHEN 'premium' THEN 2 
                    WHEN 'enterprise' THEN 3 
                    ELSE 4 
                END
        `);
        
        console.table(tiersResult.rows);
        
        // Check if the endpoint was added correctly
        const endpointCheck = await query(`
            SELECT name, 
                   CASE WHEN '/api/v1/smart-money/daily-trends/most-bought-tokens' = ANY(allowed_endpoints) 
                        THEN 'YES' 
                        ELSE 'NO' 
                   END as has_endpoint
            FROM access_tiers 
            WHERE name IN ('basic', 'premium', 'enterprise')
            ORDER BY 
                CASE name 
                    WHEN 'basic' THEN 1 
                    WHEN 'premium' THEN 2 
                    WHEN 'enterprise' THEN 3 
                    ELSE 4 
                END
        `);
        
        console.log('\n📋 Endpoint Access Check:');
        console.table(endpointCheck.rows);
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
}

runSmartMoneyMigration();
