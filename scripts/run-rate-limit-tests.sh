#!/bin/bash

# Rate Limiting Test Runner
# This script sets up test users and runs comprehensive rate limiting tests

echo "🚀 Starting Rate Limiting Test Suite..."
echo "========================================"

# Check if the API server is running
echo "🔍 Checking if API server is running..."
if ! curl -s http://localhost:3001/health > /dev/null; then
    echo "❌ API server is not running on localhost:3001"
    echo "Please start the server with: pnpm dev"
    exit 1
fi

echo "✅ API server is running"

# Setup test users
echo ""
echo "🔧 Setting up test users..."
pnpm setup:test-users

if [ $? -ne 0 ]; then
    echo "❌ Failed to setup test users"
    exit 1
fi

echo "✅ Test users setup completed"

# Wait a moment for database changes to propagate
echo ""
echo "⏳ Waiting for database changes to propagate..."
sleep 2

# Run rate limiting tests
echo ""
echo "🧪 Running rate limiting tests..."
pnpm test:rate-limiting

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Rate limiting tests completed successfully!"
else
    echo ""
    echo "❌ Rate limiting tests failed"
    exit 1
fi

echo ""
echo "📋 Test Summary:"
echo "- API Rate Limiting: Tested for all tiers"
echo "- WebSocket Connection Limits: Tested for all tiers"
echo "- Admin Bypass: Verified unlimited access"
echo ""
echo "✅ All rate limiting functionality verified!"
