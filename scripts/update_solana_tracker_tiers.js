import { query, testConnection } from '../src/config/database.js';

async function updateSolanaTrackerTiers() {
    try {
        console.log('🔧 Updating SolanaTracker stream tier requirements...');
        
        // Test database connection
        const connected = await testConnection();
        if (!connected) {
            throw new Error('Database connection failed');
        }

        // Define SolanaTracker streams
        const solanaStreams = [
            'tokens-launched', 'tokens-graduating', 'tokens-graduated',
            'pool-changes', 'token-transactions', 'price-updates', 
            'wallet-transactions', 'token-metadata', 'token-holders', 'token-changes'
        ];

        console.log('\n📊 Current tier configuration:');
        const currentTiers = await query(`
            SELECT id, name, description, allowed_streams
            FROM access_tiers 
            ORDER BY id;
        `);
        
        currentTiers.rows.forEach(tier => {
            console.log(`  - Tier ${tier.id}: ${tier.name} (allowed streams: ${tier.allowed_streams?.length || 0})`);
        });

        // Step 1: Update stream definitions to require Premium tier (tier 3)
        console.log('\n🔄 Step 1: Updating stream tier requirements to Premium (tier 3)...');
        
        for (const streamName of solanaStreams) {
            const result = await query(`
                UPDATE stream_definitions 
                SET required_tier_id = 3 
                WHERE stream_name = $1;
            `, [streamName]);
            
            if (result.rowCount > 0) {
                console.log(`✅ Updated ${streamName} to require Premium tier`);
            } else {
                console.log(`⚠️  Stream ${streamName} not found in database`);
            }
        }

        // Step 2: Update Premium tier (tier 3) allowed_streams
        console.log('\n🔄 Step 2: Adding SolanaTracker streams to Premium tier allowed_streams...');
        
        const premiumTier = await query(`
            SELECT id, allowed_streams FROM access_tiers WHERE id = 3;
        `);
        
        if (premiumTier.rows.length > 0) {
            const currentAllowedStreams = premiumTier.rows[0].allowed_streams || [];
            const newAllowedStreams = [...new Set([...currentAllowedStreams, ...solanaStreams])];
            
            await query(`
                UPDATE access_tiers 
                SET allowed_streams = $1 
                WHERE id = 3;
            `, [newAllowedStreams]);
            
            console.log(`✅ Added ${solanaStreams.length} SolanaTracker streams to Premium tier`);
            console.log(`   Total allowed streams for Premium: ${newAllowedStreams.length}`);
        } else {
            console.log('❌ Premium tier (id=3) not found');
        }

        // Step 3: Update Enterprise tier (tier 4) allowed_streams
        console.log('\n🔄 Step 3: Adding SolanaTracker streams to Enterprise tier allowed_streams...');
        
        const enterpriseTier = await query(`
            SELECT id, allowed_streams FROM access_tiers WHERE id = 4;
        `);
        
        if (enterpriseTier.rows.length > 0) {
            const currentAllowedStreams = enterpriseTier.rows[0].allowed_streams || [];
            
            // Check if Enterprise already has '*' (all streams)
            if (currentAllowedStreams.includes('*')) {
                console.log('✅ Enterprise tier already has access to all streams (*)');
            } else {
                const newAllowedStreams = [...new Set([...currentAllowedStreams, ...solanaStreams])];
                
                await query(`
                    UPDATE access_tiers 
                    SET allowed_streams = $1 
                    WHERE id = 4;
                `, [newAllowedStreams]);
                
                console.log(`✅ Added ${solanaStreams.length} SolanaTracker streams to Enterprise tier`);
                console.log(`   Total allowed streams for Enterprise: ${newAllowedStreams.length}`);
            }
        } else {
            console.log('❌ Enterprise tier (id=4) not found');
        }

        // Step 4: Verify the updates
        console.log('\n🔍 Verification: Checking updated configuration...');
        
        // Check stream definitions
        const updatedStreams = await query(`
            SELECT sd.stream_name, sd.required_tier_id, at.name as tier_name
            FROM stream_definitions sd
            LEFT JOIN access_tiers at ON sd.required_tier_id = at.id
            WHERE sd.stream_name = ANY($1)
            ORDER BY sd.stream_name;
        `, [solanaStreams]);
        
        console.log('\nSolanaTracker stream tier requirements:');
        updatedStreams.rows.forEach(stream => {
            console.log(`  - ${stream.stream_name}: requires ${stream.tier_name} (tier ${stream.required_tier_id})`);
        });

        // Check tier allowed_streams
        const updatedTiers = await query(`
            SELECT id, name, allowed_streams
            FROM access_tiers 
            WHERE id IN (2, 3, 4)
            ORDER BY id;
        `);
        
        console.log('\nTier access configuration:');
        updatedTiers.rows.forEach(tier => {
            const allowedStreams = tier.allowed_streams || [];
            const solanaStreamCount = solanaStreams.filter(s => allowedStreams.includes(s) || allowedStreams.includes('*')).length;
            console.log(`  - ${tier.name} (tier ${tier.id}): ${solanaStreamCount}/10 SolanaTracker streams allowed`);
        });

        // Step 5: Verify Basic tier restrictions
        console.log('\n🔒 Verification: Basic tier access restrictions...');
        const basicTier = await query(`
            SELECT id, name, allowed_streams FROM access_tiers WHERE id = 2;
        `);
        
        if (basicTier.rows.length > 0) {
            const basicAllowedStreams = basicTier.rows[0].allowed_streams || [];
            const basicHasSolanaStreams = solanaStreams.some(s => basicAllowedStreams.includes(s));
            
            if (basicHasSolanaStreams) {
                console.log('⚠️  WARNING: Basic tier still has access to some SolanaTracker streams');
                const allowedSolanaStreams = solanaStreams.filter(s => basicAllowedStreams.includes(s));
                console.log(`   Allowed streams: ${allowedSolanaStreams.join(', ')}`);
            } else {
                console.log('✅ Basic tier correctly restricted from SolanaTracker streams');
            }
        }

        console.log('\n🎉 Tier configuration update completed successfully!');
        console.log('\n📋 Summary:');
        console.log('✅ All SolanaTracker streams now require Premium tier (tier 3)');
        console.log('✅ Premium tier has access to all SolanaTracker streams');
        console.log('✅ Enterprise tier has access to all SolanaTracker streams');
        console.log('✅ Basic tier is properly restricted from SolanaTracker streams');

    } catch (error) {
        console.error('❌ Error updating tier configuration:', error);
        process.exit(1);
    }
}

// Run the update
updateSolanaTrackerTiers();
