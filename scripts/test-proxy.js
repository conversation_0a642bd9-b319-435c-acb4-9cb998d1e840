import { getTokenPrice } from "../tools/coingecko.js";

async function main() {
  const tokenAddress = [
    "4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump",
    "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm",
    "JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump",
    "2fdtCHuvyLcD2q86XZGFmYbDux9ZbbUgMmFhzChqmoon"
  ];
  const result = await getTokenPrice(tokenAddress);
  console.log(JSON.stringify(result, null, 2));
}

main();
