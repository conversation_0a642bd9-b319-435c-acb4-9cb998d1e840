import { query } from '../src/config/database.js';
import { cache } from '../src/config/redis.js';
import { Admin } from '../src/models/Admin.js';

async function debugAuthMiddleware() {
    try {
        console.log('🔍 Debugging auth middleware...');
        
        const apiKey = '0ab22b3faf8cb187330c8260868e4eb2fd41891db01678e18acf82649cc14ffb';
        
        console.log('1️⃣ Testing Redis cache...');
        const cacheKey = `apikey:${apiKey}`;
        const cachedUser = await cache.get(cacheKey);
        console.log('Cache result:', cachedUser ? 'Found' : 'Not found');
        
        console.log('2️⃣ Testing database query...');
        const startTime = Date.now();
        const result = await query(
            `SELECT u.*, at.name as tier_name, at.max_credits_per_month,
                    at.max_requests_per_minute, at.max_websocket_connections,
                    at.allowed_endpoints, at.allowed_streams
             FROM users u
             JOIN access_tiers at ON u.tier_id = at.id
             WHERE u.api_key = $1 AND u.is_active = true AND at.is_enabled = true`,
            [apiKey]
        );
        const duration = Date.now() - startTime;
        console.log(`Database query completed in ${duration}ms`);
        console.log(`Found ${result.rows.length} users`);
        
        if (result.rows.length > 0) {
            const user = result.rows[0];
            console.log('User found:', {
                email: user.email,
                tier_name: user.tier_name,
                allowed_endpoints: user.allowed_endpoints
            });
        } else {
            console.log('3️⃣ Testing admin lookup...');
            const adminStartTime = Date.now();
            const admin = await Admin.findByApiKey(apiKey);
            const adminDuration = Date.now() - adminStartTime;
            console.log(`Admin lookup completed in ${adminDuration}ms`);
            console.log('Admin found:', admin ? 'Yes' : 'No');
        }
        
        console.log('✅ Auth middleware debug completed');
        process.exit(0);
    } catch (error) {
        console.error('❌ Error in auth middleware debug:', error);
        process.exit(1);
    }
}

debugAuthMiddleware();
