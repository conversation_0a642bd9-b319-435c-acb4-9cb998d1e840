#!/usr/bin/env node

/**
 * Fix Database Issues Script
 * Enables free tier and ensures test users are properly configured
 */

import { query } from '../src/config/database.js';

async function fixDatabaseIssues() {
    console.log('🔧 Fixing database issues for rate limiting...\n');
    
    try {
        // Step 1: Enable free tier
        console.log('1️⃣ Enabling free tier...');
        const enableResult = await query("UPDATE access_tiers SET is_enabled = true WHERE name = 'free'");
        console.log(`✅ Free tier enabled (${enableResult.rowCount} row updated)`);
        
        // Step 2: Check all tiers status
        console.log('\n2️⃣ Checking all access tiers...');
        const tiersResult = await query(`
            SELECT id, name, max_requests_per_minute, max_websocket_connections, is_enabled 
            FROM access_tiers 
            ORDER BY id
        `);
        console.table(tiersResult.rows);
        
        // Step 3: Ensure test users exist with correct configuration
        console.log('\n3️⃣ Creating/updating test users...');
        
        const testUsers = [
            {
                email: '<EMAIL>',
                apiKey: 'test_free_api_key_12345',
                tierId: 1,
                credits: 1000,
                tier: 'Free'
            },
            {
                email: '<EMAIL>',
                apiKey: 'test_basic_api_key_12345',
                tierId: 2,
                credits: 1000000,
                tier: 'Basic'
            },
            {
                email: '<EMAIL>',
                apiKey: 'test_premium_api_key_12345',
                tierId: 3,
                credits: 5000000,
                tier: 'Premium'
            },
            {
                email: '<EMAIL>',
                apiKey: 'test_enterprise_api_key_12345',
                tierId: 4,
                credits: -1,
                tier: 'Enterprise'
            }
        ];
        
        for (const user of testUsers) {
            try {
                const result = await query(`
                    INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining, is_active) 
                    VALUES ($1, $2, $3, $4, $5, true)
                    ON CONFLICT (email) DO UPDATE SET 
                        api_key = EXCLUDED.api_key,
                        tier_id = EXCLUDED.tier_id,
                        credits_remaining = EXCLUDED.credits_remaining,
                        is_active = true
                    RETURNING id
                `, [
                    user.email,
                    '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZOzJqQZQZQZQZQ', // bcrypt hash for 'test123'
                    user.apiKey,
                    user.tierId,
                    user.credits
                ]);
                console.log(`✅ ${user.tier} tier user: ${user.email} (ID: ${result.rows[0].id})`);
            } catch (error) {
                console.error(`❌ Failed to create ${user.tier} user:`, error.message);
            }
        }
        
        // Step 4: Verify test users are properly configured
        console.log('\n4️⃣ Verifying test users configuration...');
        const usersResult = await query(`
            SELECT 
                u.id,
                u.email,
                u.api_key,
                u.is_active,
                u.tier_id,
                u.credits_remaining,
                at.name as tier_name,
                at.max_requests_per_minute,
                at.max_websocket_connections,
                at.is_enabled as tier_enabled
            FROM users u
            JOIN access_tiers at ON u.tier_id = at.id
            WHERE u.email LIKE '<EMAIL>'
            ORDER BY u.tier_id
        `);
        
        if (usersResult.rows.length > 0) {
            console.table(usersResult.rows);
        } else {
            console.log('❌ No test users found!');
        }
        
        // Step 5: Test authentication for one user
        console.log('\n5️⃣ Testing user authentication...');
        const authTestResult = await query(`
            SELECT u.*, at.name as tier_name, at.max_requests_per_minute,
                   at.max_websocket_connections, at.allowed_endpoints, at.allowed_streams
            FROM users u
            JOIN access_tiers at ON u.tier_id = at.id
            WHERE u.api_key = $1 AND u.is_active = true AND at.is_enabled = true
        `, ['test_free_api_key_12345']);
        
        if (authTestResult.rows.length > 0) {
            console.log('✅ Free tier user authentication test passed:');
            console.log('   Email:', authTestResult.rows[0].email);
            console.log('   Tier:', authTestResult.rows[0].tier_name);
            console.log('   Rate Limit:', authTestResult.rows[0].max_requests_per_minute, 'requests/minute');
            console.log('   WebSocket Limit:', authTestResult.rows[0].max_websocket_connections, 'connections');
        } else {
            console.log('❌ Free tier user authentication test FAILED');
        }
        
        console.log('\n🎉 Database issues fixed! Rate limiting should now work properly.');
        console.log('\n🧪 You can now test with:');
        console.log('   pnpm test:rate-limiting');
        
    } catch (error) {
        console.error('❌ Error fixing database issues:', error);
        console.error('Full error:', error);
        process.exit(1);
    }
}

// Run the fix
fixDatabaseIssues();
