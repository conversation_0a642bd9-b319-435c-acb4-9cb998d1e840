-- Create test users for rate limiting tests
-- This script creates one user for each tier to test rate limiting

-- First, let's check current tiers
SELECT id, name, max_requests_per_minute, max_websocket_connections, is_enabled 
FROM access_tiers 
ORDER BY id;

-- Enable the free tier for testing
UPDATE access_tiers SET is_enabled = true WHERE name = 'free';

-- Create test users for each tier
-- Note: Using bcrypt hash for password 'test123' ($2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZOzJqQZQZQZQZQ)

-- Free tier user (10 requests/min, 1 WebSocket connection)
INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining) VALUES
('<EMAIL>', '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZOzJqQZQZQZQZQ', 'test_free_api_key_12345', 1, 1000)
ON CONFLICT (email) DO UPDATE SET 
    api_key = EXCLUDED.api_key,
    tier_id = EXCLUDED.tier_id,
    credits_remaining = EXCLUDED.credits_remaining;

-- Basic tier user (60 requests/min, 3 WebSocket connections)
INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining) VALUES
('<EMAIL>', '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZOzJqQZQZQZQZQ', 'test_basic_api_key_12345', 2, 1000000)
ON CONFLICT (email) DO UPDATE SET 
    api_key = EXCLUDED.api_key,
    tier_id = EXCLUDED.tier_id,
    credits_remaining = EXCLUDED.credits_remaining;

-- Premium tier user (300 requests/min, 5 WebSocket connections)
INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining) VALUES
('<EMAIL>', '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZOzJqQZQZQZQZQZQZQ', 'test_premium_api_key_12345', 3, 5000000)
ON CONFLICT (email) DO UPDATE SET 
    api_key = EXCLUDED.api_key,
    tier_id = EXCLUDED.tier_id,
    credits_remaining = EXCLUDED.credits_remaining;

-- Enterprise tier user (1000 requests/min, 10 WebSocket connections)
INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining) VALUES
('<EMAIL>', '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZOzJqQZQZQZQZQZQZQ', 'test_enterprise_api_key_12345', 4, -1)
ON CONFLICT (email) DO UPDATE SET 
    api_key = EXCLUDED.api_key,
    tier_id = EXCLUDED.tier_id,
    credits_remaining = EXCLUDED.credits_remaining;

-- Verify the test users were created
SELECT 
    u.email,
    u.api_key,
    at.name as tier_name,
    at.max_requests_per_minute,
    at.max_websocket_connections,
    u.credits_remaining,
    u.is_active
FROM users u
JOIN access_tiers at ON u.tier_id = at.id
WHERE u.email LIKE '<EMAIL>'
ORDER BY at.id;

-- Display the test API keys for easy copying
SELECT 
    at.name as tier,
    u.api_key
FROM users u
JOIN access_tiers at ON u.tier_id = at.id
WHERE u.email LIKE '<EMAIL>'
ORDER BY at.id;
