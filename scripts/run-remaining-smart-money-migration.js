import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { query } from '../src/config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runRemainingSmartMoneyMigration() {
    try {
        console.log('🚀 Running remaining Smart Money endpoints migration...');
        
        const sqlPath = path.join(__dirname, '../SQL/11_add_remaining_smart_money_endpoints.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');
        
        await query(sql);
        
        console.log('✅ Migration completed successfully!');
        console.log('📊 Checking updated tiers...');
        
        const tiersResult = await query(`
            SELECT name, allowed_endpoints, is_enabled
            FROM access_tiers 
            WHERE name IN ('basic', 'premium', 'enterprise')
            ORDER BY 
                CASE name 
                    WHEN 'basic' THEN 1 
                    WHEN 'premium' THEN 2 
                    WHEN 'enterprise' THEN 3 
                    ELSE 4 
                END
        `);
        
        console.table(tiersResult.rows);
        
        // Check if the endpoints were added correctly
        const endpointCheck = await query(`
            SELECT name, 
                   CASE WHEN '/api/v1/smart-money/daily-trends/most-sold-tokens' = ANY(allowed_endpoints) 
                        THEN 'YES' 
                        ELSE 'NO' 
                   END as has_most_sold,
                   CASE WHEN '/api/v1/smart-money/daily-trends/daily-flows-sol' = ANY(allowed_endpoints) 
                        THEN 'YES' 
                        ELSE 'NO' 
                   END as has_flows_sol,
                   CASE WHEN '/api/v1/smart-money/daily-trends/daily-flows-meme' = ANY(allowed_endpoints) 
                        THEN 'YES' 
                        ELSE 'NO' 
                   END as has_flows_meme
            FROM access_tiers 
            WHERE name IN ('basic', 'premium', 'enterprise')
            ORDER BY 
                CASE name 
                    WHEN 'basic' THEN 1 
                    WHEN 'premium' THEN 2 
                    WHEN 'enterprise' THEN 3 
                    ELSE 4 
                END
        `);
        
        console.log('\n📋 Endpoint Access Check:');
        console.table(endpointCheck.rows);
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
}

runRemainingSmartMoneyMigration();
