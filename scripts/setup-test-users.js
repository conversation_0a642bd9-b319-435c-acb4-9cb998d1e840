#!/usr/bin/env node

/**
 * Setup Test Users Script
 * Creates test users for rate limiting tests
 */

import { query } from '../src/config/database.js';

async function setupTestUsers() {
    console.log('🔧 Setting up test users for rate limiting tests...\n');

    try {
        // First, check current access tiers
        console.log('📊 Checking current access tiers...');
        const tiersResult = await query(`
            SELECT id, name, max_requests_per_minute, max_websocket_connections, is_enabled
            FROM access_tiers
            ORDER BY id
        `);
        console.table(tiersResult.rows);

        // Enable the free tier for testing
        console.log('🔓 Enabling free tier for testing...');
        await query("UPDATE access_tiers SET is_enabled = true WHERE name = 'free'");
        console.log('✅ Free tier enabled');

        // Create test users for each tier
        console.log('\n👥 Creating test users...');

        const testUsers = [
            {
                email: '<EMAIL>',
                apiKey: 'test_free_api_key_12345',
                tierId: 1,
                credits: 1000,
                tier: 'Free'
            },
            {
                email: '<EMAIL>',
                apiKey: 'test_basic_api_key_12345',
                tierId: 2,
                credits: 1000000,
                tier: 'Basic'
            },
            {
                email: '<EMAIL>',
                apiKey: 'test_premium_api_key_12345',
                tierId: 3,
                credits: 5000000,
                tier: 'Premium'
            },
            {
                email: '<EMAIL>',
                apiKey: 'test_enterprise_api_key_12345',
                tierId: 4,
                credits: -1,
                tier: 'Enterprise'
            }
        ];

        for (const user of testUsers) {
            try {
                await query(`
                    INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining)
                    VALUES ($1, $2, $3, $4, $5)
                    ON CONFLICT (email) DO UPDATE SET
                        api_key = EXCLUDED.api_key,
                        tier_id = EXCLUDED.tier_id,
                        credits_remaining = EXCLUDED.credits_remaining
                `, [
                    user.email,
                    '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZOzJqQZQZQZQZQ', // bcrypt hash for 'test123'
                    user.apiKey,
                    user.tierId,
                    user.credits
                ]);
                console.log(`✅ Created/Updated ${user.tier} tier user: ${user.email}`);
            } catch (error) {
                console.error(`❌ Failed to create ${user.tier} user:`, error.message);
            }
        }

        console.log('\n🎉 Test users setup completed successfully!');

        // Display the test API keys
        console.log('\n📋 Test User API Keys:');
        const testUsersResult = await query(`
            SELECT
                at.name as tier,
                u.email,
                u.api_key,
                at.max_requests_per_minute,
                at.max_websocket_connections,
                u.credits_remaining
            FROM users u
            JOIN access_tiers at ON u.tier_id = at.id
            WHERE u.email LIKE '<EMAIL>'
            ORDER BY at.id
        `);

        if (testUsersResult.rows.length > 0) {
            console.table(testUsersResult.rows);
        } else {
            console.log('⚠️ No test users found. There may have been an issue creating them.');
        }

        console.log('\n🧪 You can now run the rate limiting tests:');
        console.log('   pnpm test:rate-limiting');

    } catch (error) {
        console.error('❌ Error setting up test users:', error);
        console.error('Full error:', error);
        process.exit(1);
    }
}

// Run the setup
setupTestUsers();
