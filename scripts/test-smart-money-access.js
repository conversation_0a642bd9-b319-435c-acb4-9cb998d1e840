import { query } from '../src/config/database.js';
import { cache } from '../src/config/redis.js';

async function testSmartMoneyAccess() {
    try {
        console.log('🔍 Testing Smart Money endpoint access...\n');
        
        // Test API keys
        const testKeys = [
            { key: '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG', name: '<EMAIL> (enterprise)' },
            { key: 'test_basic_api_key_12345', name: '<EMAIL> (basic)' },
            { key: 'test_premium_api_key_12345', name: '<EMAIL> (premium)' },
            { key: '0ab22b3faf8cb187330c8260868e4eb2fd41891db01678e18acf82649cc14ffb', name: '<EMAIL> (basic)' }
        ];
        
        // Smart Money endpoints to test
        const endpoints = [
            '/api/v1/smart-money/daily-trends/most-bought-tokens',
            '/api/v1/smart-money/daily-trends/most-sold-tokens',
            '/api/v1/smart-money/daily-trends/daily-flows-sol',
            '/api/v1/smart-money/daily-trends/daily-flows-meme'
        ];
        
        console.log('🧹 Clearing Redis cache for all test users...');
        for (const testKey of testKeys) {
            const cacheKey = `apikey:${testKey.key}`;
            await cache.del(cacheKey);
            console.log(`  ✅ Cleared cache for ${testKey.name}`);
        }
        
        console.log('\n📊 Testing endpoint access for each user:\n');
        
        for (const testKey of testKeys) {
            console.log(`🔑 Testing ${testKey.name}:`);
            
            // Get fresh user data from database
            const result = await query(
                `SELECT u.*, at.name as tier_name, at.max_credits_per_month,
                        at.max_requests_per_minute, at.max_websocket_connections,
                        at.allowed_endpoints, at.allowed_streams
                 FROM users u
                 JOIN access_tiers at ON u.tier_id = at.id
                 WHERE u.api_key = $1 AND u.is_active = true AND at.is_enabled = true`,
                [testKey.key]
            );
            
            if (result.rows.length === 0) {
                console.log(`  ❌ User not found or inactive`);
                continue;
            }
            
            const user = result.rows[0];
            console.log(`  📋 Tier: ${user.tier_name}`);
            console.log(`  📋 Allowed endpoints: ${user.allowed_endpoints ? user.allowed_endpoints.length : 0}`);
            
            // Test each endpoint
            for (const endpoint of endpoints) {
                const hasAccess = checkEndpointAccess(user.allowed_endpoints, endpoint);
                console.log(`    ${hasAccess ? '✅' : '❌'} ${endpoint}`);
            }
            console.log('');
        }
        
        console.log('🎯 If you\'re still getting access denied errors, try these steps:');
        console.log('1. Make sure you\'re using the correct API key');
        console.log('2. Restart your API server to clear any in-memory caches');
        console.log('3. Check that your user is active and tier is enabled');
        console.log('4. Verify the exact endpoint URL you\'re calling');
        
        process.exit(0);
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    }
}

// Helper function to check endpoint access (mimics the middleware logic)
function checkEndpointAccess(allowedEndpoints, endpoint) {
    if (!allowedEndpoints) return false;
    
    // Check if user has access to all endpoints (*)
    if (allowedEndpoints.includes('*')) {
        return true;
    }
    
    // Check if specific endpoint is allowed
    if (allowedEndpoints.includes(endpoint)) {
        return true;
    }
    
    // Check for wildcard patterns
    const hasWildcardAccess = allowedEndpoints.some(pattern => {
        if (pattern.endsWith('*')) {
            const prefix = pattern.slice(0, -1);
            return endpoint.startsWith(prefix);
        }
        return false;
    });
    
    return hasWildcardAccess;
}

testSmartMoneyAccess();
