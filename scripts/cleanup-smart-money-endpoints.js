import { query } from '../src/config/database.js';

async function cleanupSmartMoneyEndpoints() {
    try {
        console.log('🧹 Cleaning up Smart Money endpoints...');
        
        // First, let's see the current state
        console.log('\n📊 Current state:');
        const currentState = await query(`
            SELECT name, allowed_endpoints 
            FROM access_tiers 
            WHERE name IN ('basic', 'premium', 'enterprise')
            ORDER BY 
                CASE name 
                    WHEN 'basic' THEN 1 
                    WHEN 'premium' THEN 2 
                    WHEN 'enterprise' THEN 3 
                    ELSE 4 
                END
        `);
        
        currentState.rows.forEach(row => {
            console.log(`\n${row.name.toUpperCase()}:`);
            if (row.allowed_endpoints) {
                row.allowed_endpoints.forEach(endpoint => {
                    console.log(`  - ${endpoint}`);
                });
            }
        });
        
        // Clean up basic tier
        console.log('\n🔧 Cleaning up basic tier...');
        await query(`
            UPDATE access_tiers 
            SET allowed_endpoints = ARRAY[
                '/api/v1/status',
                '/api/v1/kol-feed/history',
                '/api/v1/smart-money/daily-trends/most-bought-tokens',
                '/api/v1/smart-money/daily-trends/most-sold-tokens',
                '/api/v1/smart-money/daily-trends/daily-flows-sol',
                '/api/v1/smart-money/daily-trends/daily-flows-meme'
            ]
            WHERE name = 'basic'
        `);
        
        // Clean up premium tier
        console.log('🔧 Cleaning up premium tier...');
        await query(`
            UPDATE access_tiers 
            SET allowed_endpoints = ARRAY[
                '/api/v1/status',
                '/api/v1/kol-feed/history',
                '/api/v1/smart-money/daily-trends/most-bought-tokens',
                '/api/v1/smart-money/daily-trends/most-sold-tokens',
                '/api/v1/smart-money/daily-trends/daily-flows-sol',
                '/api/v1/smart-money/daily-trends/daily-flows-meme'
            ]
            WHERE name = 'premium'
        `);
        
        // Enterprise should keep wildcard access
        console.log('🔧 Ensuring enterprise has wildcard access...');
        await query(`
            UPDATE access_tiers 
            SET allowed_endpoints = ARRAY['*']
            WHERE name = 'enterprise'
        `);
        
        // Verify the cleanup
        console.log('\n✅ After cleanup:');
        const afterState = await query(`
            SELECT name, allowed_endpoints 
            FROM access_tiers 
            WHERE name IN ('basic', 'premium', 'enterprise')
            ORDER BY 
                CASE name 
                    WHEN 'basic' THEN 1 
                    WHEN 'premium' THEN 2 
                    WHEN 'enterprise' THEN 3 
                    ELSE 4 
                END
        `);
        
        afterState.rows.forEach(row => {
            console.log(`\n${row.name.toUpperCase()}:`);
            if (row.allowed_endpoints) {
                row.allowed_endpoints.forEach(endpoint => {
                    console.log(`  - ${endpoint}`);
                });
            }
        });
        
        console.log('\n🎉 Cleanup completed successfully!');
        process.exit(0);
        
    } catch (error) {
        console.error('❌ Cleanup failed:', error);
        process.exit(1);
    }
}

cleanupSmartMoneyEndpoints();
