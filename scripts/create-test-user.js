import { query } from '../src/config/database.js';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';

async function createTestUser() {
    try {
        console.log('👤 Creating test user for Smart Money endpoint...');
        
        // Generate API key
        const apiKey = crypto.randomBytes(32).toString('hex');
        const email = '<EMAIL>';
        const password = 'test123';
        
        // Hash password
        const passwordHash = await bcrypt.hash(password, 12);
        
        // Check if user already exists
        const existingUser = await query(
            'SELECT id FROM users WHERE email = $1',
            [email]
        );
        
        if (existingUser.rows.length > 0) {
            console.log('ℹ️  User already exists, updating API key...');
            
            await query(
                'UPDATE users SET api_key = $1 WHERE email = $2',
                [apiKey, email]
            );
            
            console.log('✅ User updated successfully');
        } else {
            console.log('📝 Creating new test user...');
            
            // Get basic tier ID (tier that has access to the endpoint)
            const tierResult = await query(
                'SELECT id FROM access_tiers WHERE name = $1',
                ['basic']
            );
            
            if (tierResult.rows.length === 0) {
                throw new Error('Basic tier not found');
            }
            
            const tierId = tierResult.rows[0].id;
            
            // Create user
            await query(
                `INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining, is_active)
                 VALUES ($1, $2, $3, $4, $5, $6)`,
                [email, passwordHash, apiKey, tierId, 10000, true]
            );
            
            console.log('✅ Test user created successfully');
        }
        
        console.log('\n📋 Test User Details:');
        console.log(`Email: ${email}`);
        console.log(`Password: ${password}`);
        console.log(`API Key: ${apiKey}`);
        
        console.log('\n🧪 Test the endpoint with:');
        console.log(`curl -H "X-API-Key: ${apiKey}" http://localhost:3001/api/v1/smart-money/daily-trends/most-bought-tokens`);
        
        // Verify user has access to the endpoint
        const userCheck = await query(`
            SELECT u.email, u.api_key, at.name as tier_name, at.allowed_endpoints
            FROM users u
            JOIN access_tiers at ON u.tier_id = at.id
            WHERE u.email = $1
        `, [email]);
        
        if (userCheck.rows.length > 0) {
            const user = userCheck.rows[0];
            const hasAccess = user.allowed_endpoints.includes('/api/v1/smart-money/daily-trends/most-bought-tokens');
            console.log(`\n✅ User tier: ${user.tier_name}`);
            console.log(`✅ Has endpoint access: ${hasAccess ? 'YES' : 'NO'}`);
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error creating test user:', error);
        process.exit(1);
    }
}

createTestUser();
