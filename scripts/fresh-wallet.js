import dotenv from "dotenv";
import Redis from "ioredis";

dotenv.config();

const redis = new Redis(process.env.REDIS_CENTRAL_URL);

redis.on('connect', () => {
  console.log('✅ Redis connected successfully');
});

redis.on('error', (err) => {
  console.error('❌ Redis connection error:', err.message);
});

redis.on('ready', () => {
  console.log('✅ Redis ready for operations');
});

async function main() {
  await redis.subscribe("fresh_wallet_feed");
  redis.on("message", (channel, message) => {
    console.log(channel, JSON.stringify(JSON.parse(message), null, 2));
  });
}

main();