# MongoDB Integration Guide

This guide covers the MongoDB integration in the StalkAPI engine, including configuration, usage, and best practices.

## 📋 Overview

MongoDB is integrated as a NoSQL database solution alongside PostgreSQL, providing flexible document storage capabilities for analytics, logging, and data that benefits from schema flexibility.

## 🔧 Configuration

### Environment Variables

Add the following to your `.env` file:

```bash
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/stalkreact
```

For MongoDB Atlas (cloud):
```bash
MONGODB_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority
```

### Connection Settings

The MongoDB configuration includes optimized connection pooling:

```javascript
const dbPoolConfig = {
  min: 5,                    // Minimum connections
  max: 20,                   // Maximum connections
  acquireTimeoutMillis: 60000,
  createTimeoutMillis: 30000,
  idleTimeoutMillis: 30000,
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  retryWrites: true,
  retryReads: true,
};
```

## 🚀 Usage

### Basic Operations

The MongoDB helper provides convenient methods for common operations:

```javascript
import { mongodb } from './src/config/mongodb.js';

// Insert a document
const result = await mongodb.insertOne('users', {
  name: 'John Doe',
  email: '<EMAIL>',
  createdAt: new Date()
});

// Find a document
const user = await mongodb.findOne('users', { email: '<EMAIL>' });

// Update a document
await mongodb.updateOne('users', 
  { email: '<EMAIL>' },
  { $set: { lastLogin: new Date() } }
);

// Delete a document
await mongodb.deleteOne('users', { email: '<EMAIL>' });
```

### Available Methods

#### Collection Operations
- `getCollection(collectionName, dbName?)` - Get a collection instance
- `insertOne(collection, document, dbName?)` - Insert single document
- `insertMany(collection, documents, dbName?)` - Insert multiple documents
- `findOne(collection, query, options?, dbName?)` - Find single document
- `find(collection, query?, options?, dbName?)` - Find multiple documents
- `updateOne(collection, filter, update, options?, dbName?)` - Update single document
- `updateMany(collection, filter, update, options?, dbName?)` - Update multiple documents
- `deleteOne(collection, filter, dbName?)` - Delete single document
- `deleteMany(collection, filter, dbName?)` - Delete multiple documents
- `countDocuments(collection, query?, dbName?)` - Count documents
- `createIndex(collection, indexSpec, options?, dbName?)` - Create index
- `aggregate(collection, pipeline, options?, dbName?)` - Run aggregation pipeline

#### Connection Management
- `testMongoConnection()` - Test database connectivity
- `connectToDatabase()` - Get database connection
- `getDb(dbName?)` - Get database instance
- `closeDatabase()` - Close all connections

## 🏥 Health Monitoring

MongoDB is integrated into the application health check system:

```bash
curl http://localhost:3001/health
```

Response includes MongoDB status:
```json
{
  "status": "healthy",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "mongodb": "healthy"
  }
}
```

## 🔄 Application Integration

### Initialization

MongoDB is automatically initialized when the application starts:

1. Connection test during startup
2. Graceful shutdown handling
3. Error recovery and reconnection
4. Connection pooling management

### Error Handling

The MongoDB helper includes comprehensive error handling:

```javascript
try {
  const result = await mongodb.insertOne('collection', document);
  console.log('Document inserted:', result.insertedId);
} catch (error) {
  console.error('MongoDB operation failed:', error.message);
  // Handle error appropriately
}
```

## 🧪 Testing

Run the MongoDB test suite to verify functionality:

```bash
node test/mongodb-test.js
```

The test covers:
- Connection testing
- Document insertion
- Document retrieval
- Document updates
- Document counting
- Document deletion

## 📊 Use Cases

### Analytics Data
Store flexible analytics data that doesn't fit well in relational schemas:

```javascript
await mongodb.insertOne('analytics', {
  event: 'api_call',
  endpoint: '/api/v1/data',
  userId: 'user123',
  timestamp: new Date(),
  metadata: {
    userAgent: 'Mozilla/5.0...',
    ip: '***********',
    responseTime: 150
  }
});
```

### Logging
Store application logs with flexible structure:

```javascript
await mongodb.insertOne('logs', {
  level: 'error',
  message: 'Database connection failed',
  timestamp: new Date(),
  context: {
    service: 'api',
    function: 'connectToDatabase',
    error: error.stack
  }
});
```

### User Preferences
Store user settings and preferences:

```javascript
await mongodb.updateOne('user_preferences',
  { userId: 'user123' },
  { 
    $set: {
      theme: 'dark',
      notifications: {
        email: true,
        push: false,
        sms: true
      },
      updatedAt: new Date()
    }
  },
  { upsert: true }
);
```

## 🔒 Security Best Practices

1. **Connection Security**: Use TLS/SSL for production connections
2. **Authentication**: Always use username/password authentication
3. **Network Security**: Restrict network access to MongoDB instances
4. **Data Validation**: Validate data before insertion
5. **Index Management**: Create appropriate indexes for query performance

## 🚨 Troubleshooting

### Common Issues

**Connection Timeout**
```
❌ MongoDB connection failed: Server selection timed out after 30000 ms
```
- Check MongoDB server status
- Verify connection string
- Check network connectivity
- Ensure MongoDB is running

**Authentication Failed**
```
❌ MongoDB connection failed: Authentication failed
```
- Verify username and password
- Check database permissions
- Ensure user exists in the correct database

**Database Not Found**
```
❌ MongoDB connection failed: Database does not exist
```
- MongoDB creates databases automatically on first write
- Verify database name in connection string

### Debug Mode

Enable debug logging by setting environment variable:
```bash
DEBUG_MONGODB=true
```

## 📈 Performance Tips

1. **Indexing**: Create indexes for frequently queried fields
2. **Connection Pooling**: Use the built-in connection pooling
3. **Batch Operations**: Use `insertMany` for bulk inserts
4. **Aggregation**: Use aggregation pipelines for complex queries
5. **Projection**: Only fetch required fields using projection

## 🔄 Migration from Other Databases

When migrating data to MongoDB:

1. **Schema Design**: Design documents based on access patterns
2. **Data Transformation**: Transform relational data to document format
3. **Indexing Strategy**: Plan indexes based on query requirements
4. **Testing**: Thoroughly test all operations after migration

---

**Last Updated**: June 2025  
**Version**: 1.0.0  
**Status**: Production Ready
