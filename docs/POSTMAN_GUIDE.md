# StalkAPI Postman Collection Guide

This guide explains how to use the comprehensive Postman collection to test and interact with the StalkAPI engine, including REST API endpoints and WebSocket connection information.

## 📦 Collection Files

Located in the `postman/` folder:
- **`StalkAPI_User_Collection.json`** - End-user API collection for testing public endpoints
- **`StalkAPI_Admin_Collection.json`** - Admin collection for internal management and administration
- **`StalkAPI_Postman_Environment.json`** - Environment variables for development and production
- **`README.md`** - Detailed collection documentation

## 🚀 Quick Setup

### 1. Import Collection and Environment

1. Open Postman
2. Click **Import** button
3. Import the files you need from the `postman/` folder:
   - `StalkAPI_User_Collection.json` (End-user API testing)
   - `StalkAPI_Admin_Collection.json` (Admin/internal management)
   - `StalkAPI_Postman_Environment.json` (Environment variables)

### 2. Select Environment

1. In the top-right corner, select **"StalkAPI Environment"**
2. Verify the `base_url` is set correctly:
   - Development: `http://localhost:3001`
   - Production: `https://data.stalkapi.com`

### 3. Start Testing

1. Run the **"Login"** request first to get a JWT token
2. The token will be automatically saved to environment variables
3. All other requests will use this token automatically

## 📋 Collection Structure

### 🔌 User Collection (StalkAPI_User_Collection.json)

**System Health**
- **API Status** - Check system health and basic information

**KOL Feed Data**
- **Get KOL Feed History** - Historical KOL trading data (Basic+, 3 credits)

**WebSocket API**
- **WebSocket Info** - Get connection details and capabilities
- **Available Streams** - List streams available for user's tier
- **Active Sessions** - View current WebSocket sessions
- **Usage Statistics** - Get WebSocket usage statistics
- **Stream Credits Info** - Get credit costs for streams
- **Stream Credit Usage Stats** - Get detailed credit usage statistics

### 👑 Admin Collection (StalkAPI_Admin_Collection.json)

**System Health**
- **API Status** - Check system health

**Admin Authentication**
- **Get Admin Info** - Get current admin user information and permissions

**Tier Management**
- **Get All Tiers** - View all access tiers (including disabled)
- **Enable Tier** - Enable disabled tiers
- **Disable Tier** - Disable tiers (with safety checks)
- **Update Tier Configuration** - Modify tier settings
- **Get Tier Statistics** - View tier usage statistics

**User Management**
- **Get User Credits** - Get detailed credit information for users
- **Add Credits to User** - Add credits to user accounts
- **Reset User Monthly Credits** - Reset monthly credit usage

**Admin User Management** (System Admin Only)
- **Get All Admin Users** - View all admin users
- **Create Admin User** - Create new admin users
- **Update Admin User** - Update admin user permissions

**Analytics & Monitoring**
- **Credit Usage Analytics** - System credit usage analytics
- **Endpoint Usage Analytics** - Endpoint usage statistics

## 🎯 Demo Credentials

The collection comes pre-configured with demo user credentials:

```
Email: <EMAIL>
Password: demo123
API Key: demo_api_key_12345
Tier: Basic (1,000,000 credits)
```

## 🔄 Workflow Examples

### User API Testing Workflow (StalkAPI_User_Collection.json)

1. **API Status** → Check system health
2. **KOL Feed History** → Test historical data retrieval (3 credits)
3. **WebSocket Info** → Get connection details and capabilities
4. **Stream Credits Info** → Check credit costs for streams
5. **Usage Statistics** → Monitor WebSocket usage

### Admin Testing Workflow (StalkAPI_Admin_Collection.json)

1. **Set Admin API Key** → Configure admin authentication in environment
2. **Get Admin Info** → Verify admin permissions and profile
3. **Get All Tiers** → View all tier configurations
4. **Get User Credits** → Check specific user credit information
5. **Credit Usage Analytics** → Monitor system usage patterns

### Advanced Admin Workflow (System Admin Only)

1. **Create Admin User** → Add new admin users
2. **Update Tier Configuration** → Modify tier settings
3. **Add Credits to User** → Manage user credit balances
4. **Endpoint Usage Analytics** → Monitor API usage patterns

### WebSocket Testing (Both Collections)

1. **WebSocket Info** → Get connection details and capabilities
2. **Available Streams** → Check tier permissions and available streams
3. Use WebSocket client with: `wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY`
4. **Stream Credit Usage Stats** → Monitor credit consumption

## 🌍 Environment Variables

### Automatic Variables

- `jwt_token` - Set automatically after login
- `user_id` - Set automatically after login

### Manual Variables

- `base_url` - API base URL
- `api_key` - Demo API key
- `websocket_url` - WebSocket connection URL

### Production Setup

To test against production:

1. Change `base_url` to `https://data.stalkapi.com`
2. Change `websocket_url` to `wss://data.stalkapi.com/ws`

## 📊 Understanding Responses

### Successful API Response

```json
{
  "success": true,
  "data": { ... },
  "credits_remaining": 9999,
  "credits_used": 1
}
```

### Error Response

```json
{
  "error": "Insufficient credits",
  "code": "INSUFFICIENT_CREDITS",
  "credits_remaining": 0
}
```

### Authentication Response

```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "tier": "basic",
    "credits_remaining": 1000000
  }
}
```

## 🔧 Testing Different Scenarios

### Credit Consumption Testing

1. Check initial credits with **Get Profile**
2. Make API calls and watch credits decrease
3. Verify credit consumption matches endpoint costs

### Tier Access Testing

1. Try **Analytics Endpoint** with Basic tier (should fail)
2. Try **Demo Endpoint** with any tier (should work)
3. Check error messages for access denied

### Rate Limiting Testing

1. Make rapid requests to same endpoint
2. Observe rate limiting responses (429 status)
3. Wait and try again

### API Key vs JWT Testing

1. Use JWT auth endpoints
2. Use API key auth endpoints
3. Compare functionality and responses

## 🐛 Troubleshooting

### Common Issues

**"Unauthorized" Error**

- Check if JWT token is set in environment
- Try logging in again to refresh token
- Verify API key is correct for API key endpoints

**"Insufficient Credits" Error**

- Check credits with **Get Profile**
- This is expected behavior when credits run out

**"Access Denied" Error**

- Check user's tier with **Get Profile**
- Verify endpoint is available for user's tier

**Connection Refused**

- Ensure API server is running on correct port
- Check `base_url` in environment variables

### Debug Tips

1. **Check Environment Variables**

   - Click the eye icon next to environment dropdown
   - Verify all variables are set correctly

2. **View Request Details**

   - Check Headers tab for Authorization header
   - Verify request body format for POST requests

3. **Check Response**
   - Look at response status code
   - Read error messages in response body

## 📝 Custom Testing

### Adding New Requests

1. Right-click on folder → Add Request
2. Set method, URL, headers, and body
3. Use environment variables: `{{variable_name}}`

### Creating Test Scripts

Add to Tests tab:

```javascript
pm.test("Status code is 200", function () {
  pm.response.to.have.status(200);
});

pm.test("Response has success field", function () {
  const response = pm.response.json();
  pm.expect(response).to.have.property("success");
});
```

## 🔗 WebSocket Testing

For WebSocket testing, use external tools:

### Using wscat (Node.js)

```bash
npm install -g wscat
wscat -c "ws://localhost:3001/ws?token=YOUR_JWT_TOKEN"
```

### WebSocket Messages

```json
// Subscribe to stream
{
    "type": "subscribe",
    "payload": {
        "stream": "demo-stream"
    }
}

// Unsubscribe from stream
{
    "type": "unsubscribe",
    "payload": {
        "stream": "demo-stream"
    }
}

// Ping (keep alive)
{"type": "ping"}
```

These collections provide comprehensive testing capabilities for all StalkAPI features:

- **User Collection**: End-user API testing, WebSocket management, and credit monitoring
- **Admin Collection**: Internal management, tier administration, user management, and system analytics

Both collections support the current tier structure:
- **Free**: 1,000 credits/month ($0.00) - 1 WebSocket connection - Disabled
- **Basic**: 1,000,000 credits/month ($49.99) - 3 WebSocket connections
- **Premium**: 5,000,000 credits/month ($149.99) - 5 WebSocket connections
- **Enterprise**: Unlimited credits ($499.99) - 10 WebSocket connections
