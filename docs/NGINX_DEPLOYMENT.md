# Nginx Deployment Guide for data.stalkapi.com

This guide covers deploying the API engine behind nginx with Cloudflare origin certificates and proper real IP handling.

## Prerequisites

- Ubuntu/Debian server with nginx installed
- Cloudflare account with domain configured
- SSL certificates from Cloudflare Origin CA

## 1. Install Nginx

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install nginx
sudo apt install nginx -y

# Enable nginx to start on boot
sudo systemctl enable nginx
```

## 2. Cloudflare Origin Certificate Setup

### Generate Origin Certificate in Cloudflare Dashboard

1. Go to Cloudflare Dashboard → SSL/TLS → Origin Server
2. Click "Create Certificate"
3. Choose "Let Cloudflare generate a private key and a CSR"
4. Set hostnames: `data.stalkapi.com, *.stalkapi.com`
5. Choose key type: RSA (2048)
6. Certificate validity: 15 years
7. Click "Create"

### Install Certificates on Server

```bash
# Create SSL directory
sudo mkdir -p /etc/ssl/certs /etc/ssl/private

# Create certificate file (copy from Cloudflare dashboard)
sudo nano /etc/ssl/certs/cloudflare_origin.pem
# Paste the Origin Certificate content

# Create private key file (copy from Cloudflare dashboard)
sudo nano /etc/ssl/private/cloudflare_origin.key
# Paste the Private Key content

# Set proper permissions
sudo chmod 644 /etc/ssl/certs/cloudflare_origin.pem
sudo chmod 600 /etc/ssl/private/cloudflare_origin.key
sudo chown root:root /etc/ssl/certs/cloudflare_origin.pem
sudo chown root:root /etc/ssl/private/cloudflare_origin.key
```

## 3. Deploy Nginx Configuration

```bash
# Copy the nginx configuration
sudo cp nginx.config /etc/nginx/sites-available/data.stalkapi.com

# Create symbolic link to enable the site
sudo ln -s /etc/nginx/sites-available/data.stalkapi.com /etc/nginx/sites-enabled/

# Remove default nginx site (optional)
sudo rm /etc/nginx/sites-enabled/default

# Test nginx configuration
sudo nginx -t

# If test passes, reload nginx
sudo systemctl reload nginx
```

## 4. Create Error Pages

```bash
# Create directory for error pages
sudo mkdir -p /var/www/html

# Create 429 rate limit error page
sudo tee /var/www/html/429.html > /dev/null << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Rate Limited</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .error { color: #e74c3c; }
    </style>
</head>
<body>
    <h1 class="error">Rate Limited</h1>
    <p>Too many requests. Please try again later.</p>
</body>
</html>
EOF

# Create 50x server error page
sudo tee /var/www/html/50x.html > /dev/null << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Server Error</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .error { color: #e74c3c; }
    </style>
</head>
<body>
    <h1 class="error">Server Error</h1>
    <p>Something went wrong. Please try again later.</p>
</body>
</html>
EOF
```

## 5. Configure Cloudflare Settings

### DNS Settings
- Set A record for `data.stalkapi.com` pointing to your server IP
- Enable Cloudflare proxy (orange cloud)

### SSL/TLS Settings
- SSL/TLS encryption mode: **Full (strict)**
- Always Use HTTPS: **On**
- Minimum TLS Version: **1.2**
- Opportunistic Encryption: **On**
- TLS 1.3: **On**

### Security Settings
- Security Level: **Medium** or **High**
- Challenge Passage: **1 hour**
- Browser Integrity Check: **On**

### Speed Settings
- Auto Minify: **JavaScript, CSS, HTML**
- Brotli: **On**
- Early Hints: **On**

## 6. Firewall Configuration

```bash
# Allow HTTP and HTTPS traffic
sudo ufw allow 'Nginx Full'

# Allow SSH (if not already allowed)
sudo ufw allow ssh

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status
```

## 7. Start Node.js Application

```bash
# Navigate to application directory
cd /path/to/nodejs-api

# Install dependencies
npm install

# Start application with PM2 (recommended for production)
npm install -g pm2
pm2 start app.js --name "stalkapi"
pm2 startup
pm2 save

# Or start with npm (for testing)
npm start
```

## 8. Verify Deployment

### Test Real IP Detection
```bash
# Check if real IP is being passed correctly
curl -H "CF-Connecting-IP: *******" https://data.stalkapi.com/health
```

### Test SSL Certificate
```bash
# Check SSL certificate
openssl s_client -connect data.stalkapi.com:443 -servername data.stalkapi.com
```

### Test API Endpoints
```bash
# Test health endpoint
curl https://data.stalkapi.com/health

# Test authentication
curl -X POST https://data.stalkapi.com/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"demo123"}'
```

### Test WebSocket Connection
```bash
# Test WebSocket (requires wscat: npm install -g wscat)
wscat -c wss://data.stalkapi.com/ws?token=YOUR_JWT_TOKEN
```

## 9. Monitoring and Logs

### Nginx Logs
```bash
# Access logs
sudo tail -f /var/log/nginx/data.stalkapi.com.access.log

# Error logs
sudo tail -f /var/log/nginx/data.stalkapi.com.error.log
```

### Application Logs
```bash
# PM2 logs
pm2 logs stalkapi

# Application logs (if using file logging)
tail -f logs/api.log
```

## 10. Maintenance Commands

### Reload Nginx Configuration
```bash
sudo nginx -t && sudo systemctl reload nginx
```

### Restart Application
```bash
pm2 restart stalkapi
```

### Update SSL Certificates
```bash
# When certificates expire (every 15 years), repeat step 2
# Then reload nginx
sudo systemctl reload nginx
```

## Troubleshooting

### Common Issues

1. **502 Bad Gateway**
   - Check if Node.js app is running on port 3001
   - Verify firewall allows internal connections

2. **Real IP not detected**
   - Verify Cloudflare proxy is enabled (orange cloud)
   - Check CF-Connecting-IP header in logs

3. **SSL Certificate errors**
   - Ensure certificates are properly installed
   - Check file permissions (644 for cert, 600 for key)

4. **Rate limiting not working**
   - Verify nginx rate limiting zones are configured
   - Check Redis is running and accessible

### Debug Commands
```bash
# Check nginx configuration
sudo nginx -t

# Check nginx status
sudo systemctl status nginx

# Check application status
pm2 status

# Check port usage
sudo netstat -tlnp | grep :3001
```

## Security Considerations

1. **Keep certificates secure** - Private keys should have 600 permissions
2. **Regular updates** - Keep nginx and system packages updated
3. **Monitor logs** - Set up log monitoring for suspicious activity
4. **Backup certificates** - Store certificate backups securely
5. **Rate limiting** - Monitor and adjust rate limits based on usage patterns

This configuration provides a production-ready setup with proper SSL termination, real IP detection, and security headers optimized for Cloudflare.
