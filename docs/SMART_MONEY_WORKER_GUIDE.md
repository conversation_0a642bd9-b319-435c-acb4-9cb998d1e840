# SmartMoney Worker Guide

## Overview

The SmartMoney worker is a scheduled data processing service that aggregates and caches smart money trading data from Jupiter DCA orders and token rankings. It runs automatically every 15 minutes to provide up-to-date insights on token buying/selling patterns.

## Features

- **Automated Data Collection**: Fetches top bought/sold tokens and daily flow data
- **Scheduled Updates**: Runs every 15 minutes (at 00, 15, 30, 45 minutes of each hour)
- **Redis Caching**: Stores processed data in Redis with 1-hour TTL
- **Token Metadata**: Enriches data with token symbols and metadata
- **Error Handling**: Robust error handling with detailed logging

## Architecture

### Worker Lifecycle

1. **Initialization**: Worker starts when the application boots
2. **Initial Update**: Runs `updateData()` immediately on startup
3. **Cron Scheduler**: Starts background cron job for periodic updates
4. **Graceful Shutdown**: <PERSON><PERSON><PERSON> stops cron job during application shutdown

### Data Sources

- **MongoDB Collections**:
  - `jupdca.daily_top_token_rankings` - Most bought tokens
  - `jupdca.daily_bottom_token_rankings` - Most sold tokens  
  - `jupdca.daily_flows` - SOL and memecoin flow data
  - `centraldata.token_metadata` - Token symbols and metadata
  - `centraldata.stablecoins` - Excluded stablecoin addresses
  - `centraldata.sol_derivative_tokens` - Excluded SOL derivative tokens

### Redis Cache Keys

The worker stores processed data in Redis with the following keys:

- `smart_money:daily_trends:most_bought_tokens` - Top 10 bought tokens per day (last 15 days)
- `smart_money:daily_trends:most_sold_tokens` - Top 10 sold tokens per day (last 15 days)
- `smart_money:daily_trends:daily_flows_sol` - Daily SOL flow data (buy/sell/net volumes)
- `smart_money:daily_trends:daily_flows_meme` - Daily memecoin flow data (buy/sell/net volumes)

All cache entries have a 1-hour TTL (3600 seconds).

### API Endpoints

The cached data is accessible through the following REST API endpoints:

- `GET /api/v1/smart-money/daily-trends/most-bought-tokens` - Most bought tokens (2 credits)
- `GET /api/v1/smart-money/daily-trends/most-sold-tokens` - Most sold tokens (2 credits)
- `GET /api/v1/smart-money/daily-trends/daily-flows-sol` - Daily SOL flows (2 credits)
- `GET /api/v1/smart-money/daily-trends/daily-flows-meme` - Daily meme flows (2 credits)

**Access Control**: Available to Basic, Premium, and Enterprise tiers (Free tier excluded)

## Configuration

### Cron Schedule

The worker runs every 15 minutes using the cron pattern: `0 0,15,30,45 * * * *`

- **00 minutes**: :00, :15, :30, :45 of every hour
- **Timezone**: UTC
- **Overlap Protection**: Checks if worker is running before executing

### Data Retention

- **Historical Data**: Fetches last 15 days of data
- **Token Rankings**: Top 10 tokens per category per day
- **Cache TTL**: 1 hour (3600 seconds)

## Usage

### Automatic Operation

The SmartMoney worker starts automatically when the application boots:

```javascript
// In src/dataManager.js
const dataManager = new DataManager();
await dataManager.initialize(); // This starts SmartMoney worker
```

### Manual Control

```javascript
import SmartMoney from './src/workers/smartMoney.js';

const smartMoney = new SmartMoney();

// Initialize and start
await smartMoney.init();

// Stop the worker
smartMoney.stop();

// Manual data update
await smartMoney.updateData();
```

## Data Structure

### Most Bought/Sold Tokens

```json
{
  "date": "2024-01-01T00:00:00.000Z",
  "rank": 1,
  "token_address": "So11111111111111111111111111111111111111112",
  "rank_type": "top",
  "total_volume": 1000000,
  "transaction_count": 150
}
```

### Daily Flows

```json
{
  "date": "2024-01-01T00:00:00.000Z",
  "buy_volume": 5000000,
  "sell_volume": 4500000,
  "net_volume": 500000
}
```

## Monitoring

### Logs

The worker provides detailed logging for monitoring:

```
🚀 Initializing SmartMoney worker
📊 Running initial SmartMoney data update...
⏰ SmartMoney cron job started - will run every 15 minutes (00, 15, 30, 45)
✅ SmartMoney worker initialized successfully

⏰ SmartMoney scheduled update started at 2024-01-01T12:15:00.000Z
📊 SmartMoney: Fetched metadata for 104 tokens
✅ SmartMoney scheduled update completed in 2341ms
```

### Error Handling

```
❌ Error in SmartMoney scheduled update: [error details]
⚠️ SmartMoney worker is not running, skipping scheduled update
```

## Development

### Testing

The worker saves test data to `temp/smartmoney_test.json` for development purposes:

```json
{
  "most_bought_tokens": [...],
  "most_sold_tokens": [...],
  "dailyFlows": [...]
}
```

### Future Enhancements

The worker includes placeholder functions for future chart processing:

- `processTokenCharts()` - Process token ranking charts
- `injectTokenSymbols()` - Add token symbols to chart data

## Troubleshooting

### Common Issues

1. **Cron Job Not Running**
   - Check if worker is properly initialized
   - Verify `this.isRunning` flag is true
   - Check for initialization errors

2. **Database Connection Errors**
   - Verify MongoDB connection is established
   - Check database credentials and network connectivity
   - Review database connection logs

3. **Redis Cache Issues**
   - Verify Redis connection is active
   - Check Redis memory usage and TTL settings
   - Review cache key naming and structure

4. **Data Processing Errors**
   - Check MongoDB collection names and structure
   - Verify date range calculations
   - Review aggregation pipeline syntax

### Performance Considerations

- **Memory Usage**: Worker cleans up database connections after each run
- **Execution Time**: Typical run takes 2-5 seconds depending on data volume
- **Database Load**: Uses efficient aggregation pipelines to minimize load
- **Cache Efficiency**: 1-hour TTL balances freshness with performance

## Integration

The SmartMoney worker integrates with:

- **DataManager**: Manages worker lifecycle
- **MongoDB**: Data source for trading data
- **Redis**: Caching layer for processed data
- **Application Lifecycle**: Graceful startup and shutdown
