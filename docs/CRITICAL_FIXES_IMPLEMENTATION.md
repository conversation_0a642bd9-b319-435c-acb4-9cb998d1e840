# Critical Performance & Security Fixes Implementation Guide

## 🚨 Immediate Critical Fixes (Week 1)

### 1. Database Connection Pool Optimization

**Current Issue**: Limited to 20 connections, causing bottlenecks

**Implementation**:

```javascript
// src/config/database.js - Update configuration
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || 'api_engine',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    
    // CRITICAL: Increase connection pool
    max: parseInt(process.env.DB_MAX_CONNECTIONS) || 100, // Increased from 20
    min: parseInt(process.env.DB_MIN_CONNECTIONS) || 10,  // Add minimum connections
    
    // Optimize timeouts
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 5000, // Reduced from 2000
    acquireTimeoutMillis: 60000,   // Add acquire timeout
    
    // Add connection validation
    allowExitOnIdle: true,
    maxUses: 7500, // Rotate connections after 7500 uses
};

// Add connection pool monitoring
pool.on('connect', (client) => {
    console.log(`Database connection established. Pool size: ${pool.totalCount}, Idle: ${pool.idleCount}`);
});

pool.on('acquire', (client) => {
    console.log(`Database connection acquired. Pool size: ${pool.totalCount}, Idle: ${pool.idleCount}`);
});

pool.on('remove', (client) => {
    console.log(`Database connection removed. Pool size: ${pool.totalCount}, Idle: ${pool.idleCount}`);
});
```

**Environment Variables to Add**:
```bash
# .env
DB_MAX_CONNECTIONS=100
DB_MIN_CONNECTIONS=10
```

### 2. Redis-Backed Rate Limiting

**Current Issue**: Rate limiting uses memory store, not persistent

**Implementation**:

```javascript
// src/middleware/rateLimiter.js - Replace memory store with Redis
import { redis } from '../config/redis.js';

export const createRedisRateLimit = (options = {}) => {
    const {
        windowMs = 60 * 1000,
        max = 100,
        keyGenerator = (req) => req.user ? `user:${req.user.id}` : `ip:${req.realIP || req.ip}`,
        skipSuccessfulRequests = false,
        skipFailedRequests = false,
        message = 'Too many requests'
    } = options;
    
    return rateLimit({
        windowMs,
        limit: max,
        keyGenerator,
        message: typeof message === 'string' ? { error: message } : message,
        skipSuccessfulRequests,
        skipFailedRequests,
        standardHeaders: true,
        legacyHeaders: false,
        
        // CRITICAL: Use Redis store instead of memory
        store: {
            incr: async (key) => {
                const multi = redis.multi();
                multi.incr(key);
                multi.expire(key, Math.ceil(windowMs / 1000));
                const results = await multi.exec();
                
                const count = results[0][1];
                const resetTime = new Date(Date.now() + windowMs);
                
                return { totalHits: count, resetTime };
            },
            
            decrement: async (key) => {
                await redis.decr(key);
            },
            
            resetKey: async (key) => {
                await redis.del(key);
            },
            
            resetAll: async () => {
                // Implementation for clearing all rate limit keys
                const keys = await redis.keys('rate_limit:*');
                if (keys.length > 0) {
                    await redis.del(...keys);
                }
            }
        }
    });
};

// Update user rate limiter to use Redis
export const createUserRateLimit = () => {
    return createRedisRateLimit({
        windowMs: 60 * 1000,
        keyGenerator: (req) => {
            const baseKey = req.user ? `user:${req.user.id}` : `ip:${req.realIP || req.ip}`;
            return `rate_limit:${baseKey}`;
        },
        max: (req) => {
            if (req.user && req.user.max_requests_per_minute) {
                if (req.user.is_admin || req.user.max_requests_per_minute === -1) {
                    return 0; // No limit
                }
                return req.user.max_requests_per_minute;
            }
            return parseInt(process.env.DEFAULT_RATE_LIMIT) || 100;
        },
        skip: (req) => {
            return req.user && (req.user.is_admin || req.user.max_requests_per_minute === -1);
        }
    });
};
```

### 3. Security Headers Configuration

**Current Issue**: CSP and other security headers disabled

**Implementation**:

```javascript
// app.js - Fix security headers
app.use(helmet({
    // CRITICAL: Enable Content Security Policy
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "wss:", "ws:"],
            frameSrc: ["'none'"],
            objectSrc: ["'none'"],
            baseUri: ["'self'"],
            formAction: ["'self'"],
            upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null,
        },
    },
    
    // Enable other security headers
    crossOriginEmbedderPolicy: false, // Keep disabled for API compatibility
    crossOriginOpenerPolicy: { policy: "same-origin" },
    crossOriginResourcePolicy: { policy: "cross-origin" },
    
    // CRITICAL: Enable HSTS in production
    hsts: process.env.NODE_ENV === 'production' ? {
        maxAge: 31536000, // 1 year
        includeSubDomains: true,
        preload: true
    } : false,
    
    // Additional security headers
    noSniff: true,
    frameguard: { action: 'deny' },
    xssFilter: true,
    referrerPolicy: { policy: "strict-origin-when-cross-origin" }
}));
```

### 4. Input Validation Middleware

**Current Issue**: No comprehensive input validation

**Implementation**:

```javascript
// src/middleware/validation.js - New file
import validator from 'validator';

// Sanitize string input
export const sanitizeString = (str, maxLength = 255) => {
    if (typeof str !== 'string') return '';
    return validator.escape(str.trim().substring(0, maxLength));
};

// Validate email
export const validateEmail = (email) => {
    return validator.isEmail(email) && email.length <= 255;
};

// Validate API key format
export const validateApiKey = (apiKey) => {
    return typeof apiKey === 'string' && 
           apiKey.length >= 32 && 
           apiKey.length <= 64 && 
           /^[a-zA-Z0-9_-]+$/.test(apiKey);
};

// General input validation middleware
export const validateInput = (schema) => {
    return (req, res, next) => {
        const errors = [];
        
        // Validate body parameters
        if (schema.body) {
            for (const [field, rules] of Object.entries(schema.body)) {
                const value = req.body[field];
                
                if (rules.required && (value === undefined || value === null || value === '')) {
                    errors.push(`${field} is required`);
                    continue;
                }
                
                if (value !== undefined && value !== null) {
                    // Type validation
                    if (rules.type === 'email' && !validateEmail(value)) {
                        errors.push(`${field} must be a valid email`);
                    }
                    
                    if (rules.type === 'string' && typeof value !== 'string') {
                        errors.push(`${field} must be a string`);
                    }
                    
                    if (rules.type === 'number' && typeof value !== 'number') {
                        errors.push(`${field} must be a number`);
                    }
                    
                    // Length validation
                    if (rules.minLength && value.length < rules.minLength) {
                        errors.push(`${field} must be at least ${rules.minLength} characters`);
                    }
                    
                    if (rules.maxLength && value.length > rules.maxLength) {
                        errors.push(`${field} must be no more than ${rules.maxLength} characters`);
                    }
                    
                    // Custom validation
                    if (rules.custom && !rules.custom(value)) {
                        errors.push(`${field} is invalid`);
                    }
                    
                    // Sanitize string values
                    if (rules.type === 'string' && rules.sanitize !== false) {
                        req.body[field] = sanitizeString(value, rules.maxLength);
                    }
                }
            }
        }
        
        if (errors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors
            });
        }
        
        next();
    };
};

// Common validation schemas
export const schemas = {
    userRegistration: {
        body: {
            email: { required: true, type: 'email' },
            password: { required: true, type: 'string', minLength: 8, maxLength: 128 },
            tier_id: { type: 'number', custom: (val) => val >= 1 && val <= 10 }
        }
    },
    
    userLogin: {
        body: {
            email: { required: true, type: 'email' },
            password: { required: true, type: 'string', minLength: 1, maxLength: 128 }
        }
    },
    
    adminCreate: {
        body: {
            name: { required: true, type: 'string', maxLength: 100 },
            email: { required: true, type: 'email' },
            permissions: { type: 'array' }
        }
    }
};
```

### 5. WebSocket Connection Limits

**Current Issue**: No global connection limits

**Implementation**:

```javascript
// src/websocket/WebSocketServer.js - Add connection management
export class WSServer {
    constructor(server) {
        this.wss = new WebSocketServer({
            server,
            path: '/ws'
        });

        // CRITICAL: Add connection tracking and limits
        this.clients = new Map();
        this.subscriptions = new Map();
        this.userConnections = new Map();
        
        // Global connection limits
        this.maxGlobalConnections = parseInt(process.env.WS_MAX_GLOBAL_CONNECTIONS) || 10000;
        this.maxUserConnections = parseInt(process.env.WS_MAX_USER_CONNECTIONS) || 10;
        
        // Connection cleanup
        this.connectionCleanupInterval = setInterval(() => {
            this.cleanupStaleConnections();
        }, 60000); // Every minute
        
        this.setupEventHandlers();
        this.setupRedisSubscriptions();
        this.startHeartbeat();

        console.log('✅ WebSocket server initialized');
    }
    
    // CRITICAL: Check connection limits before accepting
    checkConnectionLimits(userId) {
        // Check global limit
        if (this.clients.size >= this.maxGlobalConnections) {
            return { 
                allowed: false, 
                reason: 'Global connection limit exceeded',
                limit: this.maxGlobalConnections 
            };
        }
        
        // Check per-user limit
        const userConnections = this.userConnections.get(userId);
        if (userConnections && userConnections.size >= this.maxUserConnections) {
            return { 
                allowed: false, 
                reason: 'User connection limit exceeded',
                limit: this.maxUserConnections 
            };
        }
        
        return { allowed: true };
    }
    
    // CRITICAL: Cleanup stale connections
    cleanupStaleConnections() {
        const now = Date.now();
        const staleTimeout = 5 * 60 * 1000; // 5 minutes
        
        for (const [connectionId, clientInfo] of this.clients.entries()) {
            if (now - clientInfo.lastActivity > staleTimeout) {
                console.log(`🧹 Cleaning up stale connection: ${connectionId}`);
                this.handleDisconnection(connectionId);
            }
        }
    }
    
    // Update connection handler to check limits
    setupEventHandlers() {
        this.wss.on('connection', async (ws, req) => {
            const connectionId = uuidv4();
            const sessionId = uuidv4();

            // Authenticate user
            const authResult = await this.authenticateConnection(req);
            if (!authResult) {
                ws.close(1008, 'Authentication failed');
                return;
            }

            if (authResult.error) {
                if (authResult.error === 'connection_limit_exceeded') {
                    ws.close(1013, `Connection limit exceeded: ${authResult.limit} max connections`);
                } else {
                    ws.close(1008, 'Authentication failed');
                }
                return;
            }

            const user = authResult;
            
            // CRITICAL: Check connection limits
            const limitCheck = this.checkConnectionLimits(user.id);
            if (!limitCheck.allowed) {
                ws.close(1013, `${limitCheck.reason}: ${limitCheck.limit} max connections`);
                return;
            }

            // Continue with connection setup...
            // ... rest of existing code
        });
    }
}
```

**Environment Variables to Add**:
```bash
# .env
WS_MAX_GLOBAL_CONNECTIONS=10000
WS_MAX_USER_CONNECTIONS=10
```

## 📋 Implementation Checklist

- [ ] Update database connection pool configuration
- [ ] Implement Redis-backed rate limiting
- [ ] Enable proper security headers
- [ ] Add input validation middleware
- [ ] Implement WebSocket connection limits
- [ ] Add connection pool monitoring
- [ ] Test all changes in development
- [ ] Update environment variables
- [ ] Deploy to staging for testing
- [ ] Monitor performance improvements

## 🔧 Testing Commands

```bash
# Test database connections under load
pnpm run test:database-load

# Test rate limiting
pnpm run test:rate-limiting:full

# Test WebSocket connection limits
pnpm run test:websocket-limits

# Test security headers
curl -I https://your-api.com/health
```

## 📊 Monitoring

After implementing these fixes, monitor:
- Database connection pool usage
- Redis memory usage
- WebSocket connection counts
- Rate limiting effectiveness
- Response times under load

These critical fixes will immediately improve the API's ability to handle high concurrent loads while maintaining security standards.
