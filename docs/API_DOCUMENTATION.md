# StalkAPI Documentation

## 📚 Interactive Documentation

**StalkAPI now includes beautiful, self-hosted interactive documentation powered by Scalar!**

### Access Documentation

```bash
# Start the server
pnpm start

# Open documentation in browser
pnpm run docs:open

# Or visit directly
http://localhost:3001/docs
```

**Documentation Features:**
- 🎯 **Interactive API Explorer** - Test endpoints directly in the browser
- 📖 **Complete API Reference** - All endpoints, parameters, and responses
- 🔧 **Built-in Authentication** - Test with your API keys
- 📱 **Responsive Design** - Works on desktop and mobile
- ⚡ **Real-time Updates** - No caching issues, updates instantly
- 🎨 **Beautiful UI** - Modern, professional interface

**Documentation Endpoints:**
- `GET /docs` - Interactive documentation interface
- `GET /docs/openapi.yaml` - OpenAPI specification (YAML)
- `GET /docs/openapi.json` - OpenAPI specification (JSON)
- `GET /docs/info` - Documentation metadata

## Overview

StalkAPI is a professional KOL (Key Opinion Leader) trading data API that provides real-time access to cryptocurrency trading activity from verified KOL traders. The platform offers both real-time WebSocket streaming and historical data access through REST APIs, with built-in credit management and multi-tier access control.

## Features

- **Real-time KOL Trading Stream**: Live WebSocket feed of KOL trading activity (2 credits per message)
- **Historical KOL Data API**: REST endpoint for the last 100 KOL transactions (3 credits per request)
- **Credit-based Usage Tracking**: Monitor and control API usage with a flexible credit system
- **Tier-based Access Control**: Multiple access tiers with different permissions and limits
- **WebSocket Streaming**: Real-time communication with subscribe/unsubscribe capabilities
- **API Key Authentication**: Secure authentication with API keys
- **Rate Limiting**: Intelligent rate limiting based on user tiers
- **Comprehensive Logging**: Detailed usage analytics and monitoring
- **Redis Pub/Sub Integration**: Distributed event handling and caching
- **Self-hosted Documentation**: Beautiful Scalar-powered interactive docs

## Quick Start

### 1. Environment Setup

Copy `.env.example` to `.env` and configure your settings:

```bash
cp .env.example .env
```

### 2. Database Setup

Run the migration script to set up your PostgreSQL database:

```bash
pnpm run db:migrate
```

### 3. Start the Server

```bash
# Development
pnpm run dev

# Production
pnpm start
```

### 4. Access Documentation

```bash
# Open interactive documentation
pnpm run docs:open
```

## Authentication

StalkAPI uses **API Key authentication** for all endpoints and WebSocket connections.

### API Key Authentication

Include the API key in the X-API-Key header:

```
X-API-Key: YOUR_API_KEY
```

### Admin Authentication

Admin endpoints require an admin API key:

```
X-Admin-API-Key: YOUR_ADMIN_API_KEY
```

**Note:** Registration and login endpoints are now admin-only and require admin API keys. Regular users receive API keys through the admin interface.

## Access Tiers

| Tier | Credits/Month | Requests/Min | WebSocket Connections | Price | Status |
|------|---------------|--------------|----------------------|-------|--------|
| Free | 1,000 | 10 | 1 | $0.00 | Disabled* |
| Basic | 10,000 | 60 | 3 | $9.99 | Enabled |
| Premium | 100,000 | 300 | 10 | $49.99 | Enabled |
| Enterprise | Unlimited | 1,000 | 50 | $199.99 | Enabled |

*The free tier is currently disabled but can be enabled through admin controls.

## API Endpoints

### Authentication Endpoints

#### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword",
  "tier_id": 1
}
```

#### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

### Core Endpoints

#### Get Token Price (Basic+)
```http
POST /api/v1/core/token-price
X-API-Key: YOUR_API_KEY
Content-Type: application/json

{
  "tokenAddress": "So11111111111111111111111111111111111111112"
}
```
**Credits:** 3 per request

Get current price and market data for Solana tokens from GeckoTerminal. Supports single token address or array of up to 30 token addresses.

**Request Body:**
- `tokenAddress` (string|array): Single token address or array of token addresses (max 30)

**Example Request (Single Token):**
```json
{
  "tokenAddress": "So11111111111111111111111111111111111111112"
}
```

**Example Request (Multiple Tokens):**
```json
{
  "tokenAddress": [
    "So11111111111111111111111111111111111111112",
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
  ]
}
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "tokenAddress": "So11111111111111111111111111111111111111112",
      "price": 159.25,
      "market_cap": 75000000000,
      "24h_volume": 2500000000,
      "24h_price_change_percentage": 2.5,
      "total_reserve_usd": 125000000
    }
  ],
  "credits_consumed": 3,
  "message": "Token price retrieved successfully"
}
```

#### Get Token Metadata (Basic+)
```http
POST /api/v1/core/token-metadata
X-API-Key: YOUR_API_KEY
Content-Type: application/json

{
  "tokenAddress": [
    "4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump",
    "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm"
  ]
}
```

**Credits:** 3 per request

Get token metadata including name, symbol, decimals, and logo URL. Features intelligent 3-tier caching system (Redis → Database → GeckoTerminal API) for optimal performance. Supports up to 30 token addresses per request. Background cache updates ensure minimal response time.

**Request Body:**
- `tokenAddress` (string|array): Single token address or array of token addresses (max 30)

**Example Request (Single Token):**
```json
{
  "tokenAddress": ["4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump"]
}
```

**Example Request (Multiple Tokens):**
```json
{
  "tokenAddress": [
    "4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump",
    "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm"
  ]
}
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "tokenAddress": "4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump",
      "name": "DADDY TATE",
      "symbol": "DADDY",
      "decimals": 6,
      "logo": "https://coin-images.coingecko.com/coins/images/38684/large/DADDY_TATE.png"
    },
    {
      "tokenAddress": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm",
      "name": "Example Token",
      "symbol": "EXAMPLE",
      "decimals": 9,
      "logo": "https://example.com/token-logo.png"
    }
  ],
  "credits_consumed": 3,
  "message": "Token metadata retrieved successfully",
  "cache_info": {
    "note": "Data served from cache/database when available for optimal performance"
  }
}
```

### API Endpoints

#### Status Check (Public)
```http
GET /api/v1/status
```

#### Demo Endpoint (All Tiers)
```http
GET /api/v1/demo
X-API-Key: YOUR_API_KEY
```
**Credits:** 1 per request

#### Data Endpoint (Basic+)
```http
GET /api/v1/data?limit=10&offset=0
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

#### Analytics Endpoint (Premium+)
```http
GET /api/v1/analytics?timeframe=24h
X-API-Key: YOUR_API_KEY
```
**Credits:** 5 per request

#### Submit Data
```http
POST /api/v1/submit
X-API-Key: YOUR_API_KEY
Content-Type: application/json

{
  "data": {
    "key": "value"
  },
  "metadata": {
    "source": "client"
  }
}
```
**Credits:** 3 per request

#### Batch Processing (Enterprise)
```http
POST /api/v1/batch
X-API-Key: YOUR_API_KEY
Content-Type: application/json

{
  "items": [
    {"id": 1, "data": "value1"},
    {"id": 2, "data": "value2"}
  ]
}
```
**Credits:** 10 per request

#### Search
```http
GET /api/v1/search?q=query&limit=10&category=tech
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

#### KOL Feed History (Basic+)
```http
GET /api/v1/kol-feed/history?limit=50&offset=0
X-API-Key: YOUR_API_KEY
```
**Credits:** 3 per request

Returns the last 100 KOL trading transactions with pagination support.

### Smart Money Endpoints (Basic+)

The Smart Money API provides insights into trading patterns of verified smart money traders over the last 15 days. All endpoints require Basic tier or higher and consume 2 credits per request.

#### Most Bought Tokens
```http
GET /api/v1/smart-money/daily-trends/most-bought-tokens
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

Returns the top 10 most bought tokens by smart money traders per day over the last 15 days.

#### Most Sold Tokens
```http
GET /api/v1/smart-money/daily-trends/most-sold-tokens
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

Returns the top 10 most sold tokens by smart money traders per day over the last 15 days.

#### Daily SOL Flows
```http
GET /api/v1/smart-money/daily-trends/daily-flows-sol
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

Returns daily SOL flow data (buy_volume, sell_volume, net_volume) for the last 15 days.

#### Daily Meme Flows
```http
GET /api/v1/smart-money/daily-trends/daily-flows-meme
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

Returns daily memecoin flow data (buy_volume, sell_volume, net_volume) for the last 15 days.

#### Top Tokens 24h
```http
GET /api/v1/smart-money/top-tokens/24h
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

Returns top performing tokens by smart money traders over the last 24 hours.

#### Top Tokens 3d
```http
GET /api/v1/smart-money/top-tokens/3d
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

Returns top performing tokens by smart money traders over the last 3 days.

#### Top Tokens 7d
```http
GET /api/v1/smart-money/top-tokens/7d
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

Returns top performing tokens by smart money traders over the last 7 days.

#### Bottom Tokens 24h
```http
GET /api/v1/smart-money/bottom-tokens/24h
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

Returns worst performing tokens by smart money traders over the last 24 hours.

#### Bottom Tokens 3d
```http
GET /api/v1/smart-money/bottom-tokens/3d
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

Returns worst performing tokens by smart money traders over the last 3 days.

#### Bottom Tokens 7d
```http
GET /api/v1/smart-money/bottom-tokens/7d
X-API-Key: YOUR_API_KEY
```
**Credits:** 2 per request

Returns worst performing tokens by smart money traders over the last 7 days.

**Example Response (Most Bought Tokens):**
```json
{
  "success": true,
  "data": [
    {
      "date": "2025-06-06T00:00:00.000Z",
      "token_address": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
      "buy_volume": 186862.69698474216,
      "sell_volume": 61808.12228895664,
      "net_volume": 125054.57469578552,
      "rank_type": "top",
      "rank": 1,
      "top_buyer": {
        "wallet": "GnAHnGRFXrU9cSyYMdvjdUc6BzSj4e6qP6Py79BKZgUB",
        "amount": 167805.8567037489
      },
      "top_seller": {
        "wallet": "CubRkB3zzp1ibiiRpKW8vjR7afm5XSSJkg7XKtDX1gBC",
        "amount": 26636.232022063738
      },
      "volume_change": -94.87622732035607,
      "rank_change": 0,
      "symbol": "Fartcoin",
      "name": "Fartcoin",
      "decimals": 6,
      "logo": "https://ipfs.io/ipfs/QmQr3Fz4h1etNsF7oLGMRHiCzhB5y9a7GjyodnF7zLHK1g"
    }
  ],
  "credits_consumed": 2,
  "message": "Most bought tokens retrieved successfully"
}
```

**Example Response (Most Sold Tokens):**
```json
{
  "success": true,
  "data": [
    {
      "date": "2025-06-06T00:00:00.000Z",
      "token_address": "ENfpbQUM5xAnNP8ecyEQGFJ6KwbuPjMwv7ZjR29cDuAb",
      "buy_volume": 0,
      "sell_volume": 32171.998188827623,
      "net_volume": -32171.998188827623,
      "rank_type": "bottom",
      "rank": 1,
      "top_buyer": null,
      "top_seller": {
        "wallet": "4STJA1sGVcN3husxLqtoA9oCqSJeQjoLb9WYRjbDvQCD",
        "amount": 32171.998188827623
      },
      "volume_change": -79.98920558834294,
      "rank_change": 22,
      "symbol": "GOONC",
      "name": "gooncoin",
      "decimals": 9,
      "logo": "https://ipfs.io/ipfs/bafkreiebjj52eznuxsmc6djnxz3n6dbuhyt44xv42xqlk3kbaoudog64ji"
    }
  ],
  "credits_consumed": 2,
  "message": "Most sold tokens retrieved successfully"
}
```

**Example Response (Flow Endpoints):**
```json
{
  "success": true,
  "data": [
    {
      "date": "2025-06-06T00:00:00.000Z",
      "buy_volume": 5000000,
      "sell_volume": 4500000,
      "net_volume": 500000
    }
  ],
  "credits_consumed": 2,
  "message": "Daily SOL flows retrieved successfully"
}
```

**Example Response (Timeframe Token Endpoints):**
```json
{
  "success": true,
  "data": [
    {
      "timeframe": "7d",
      "token_address": "YXUpFaULqhrLJS79JmFtAsNZQ2JDTnPemmdVZEFpump",
      "buy_volume": 12759.542570171594,
      "sell_volume": 66125.2727827169,
      "net_volume": -53365.730212545306,
      "rank_type": "bottom",
      "rank": 25,
      "biggest_buyer": {
        "wallet": "8rm7k5YqfmSKjDG5ZpXDRoPnRWmCELyF2xQunuTriQ2T",
        "amount": 12759.542570171594
      },
      "biggest_seller": {
        "wallet": "GorNqtHP4Zsd4HrcYTH2VYwU9FxefJZqaAEbzobnq2r1",
        "amount": 16500.37829474972
      },
      "symbol": null,
      "name": null,
      "decimals": null,
      "logo": null
    }
  ],
  "credits_consumed": 2,
  "message": "Bottom tokens 7d retrieved successfully"
}
```



## Credit System

The API uses a credit-based usage tracking system where each API call consumes a specific number of credits based on the endpoint's complexity and resource requirements.

### How Credits Work

**Credit Consumption:**
- Each API endpoint has a predefined credit cost
- Credits are consumed **before** the API call is processed
- If insufficient credits, the request is rejected with a 402 error
- Credits are deducted atomically using PostgreSQL functions

**Credit Allocation:**
- Credits are allocated based on the user's access tier
- Monthly credit limits are enforced per tier
- Unlimited tiers use -1 to indicate no limit

### Credit Costs by Endpoint

| Endpoint | Credit Cost | Description |
|----------|-------------|-------------|
| `GET /api/v1/demo` | 1 | Basic demo endpoint |
| `GET /api/v1/data` | 2 | Data retrieval endpoint |
| `GET /api/v1/analytics` | 5 | Analytics processing |
| `GET /api/v1/search` | 2 | Search functionality |
| `POST /api/v1/submit` | 3 | Data submission endpoint |
| `POST /api/v1/core/token-price` | 3 | Token price and market data |
| `POST /api/v1/core/token-metadata` | 3 | Token metadata with intelligent caching |
| `GET /api/v1/kol-feed/history` | 3 | KOL trading history |
| `GET /api/v1/smart-money/daily-trends/most-bought-tokens` | 2 | Smart money most bought tokens |
| `GET /api/v1/smart-money/daily-trends/most-sold-tokens` | 2 | Smart money most sold tokens |
| `GET /api/v1/smart-money/daily-trends/daily-flows-sol` | 2 | Smart money daily SOL flows |
| `GET /api/v1/smart-money/daily-trends/daily-flows-meme` | 2 | Smart money daily meme flows |
| `GET /api/v1/smart-money/top-tokens/24h` | 2 | Smart money top tokens 24h |
| `GET /api/v1/smart-money/top-tokens/3d` | 2 | Smart money top tokens 3d |
| `GET /api/v1/smart-money/top-tokens/7d` | 2 | Smart money top tokens 7d |
| `GET /api/v1/smart-money/bottom-tokens/24h` | 2 | Smart money bottom tokens 24h |
| `GET /api/v1/smart-money/bottom-tokens/3d` | 2 | Smart money bottom tokens 3d |
| `GET /api/v1/smart-money/bottom-tokens/7d` | 2 | Smart money bottom tokens 7d |
| `POST /api/v1/batch` | 10 | Batch processing |
| WebSocket connections | 0 | No credit cost for connections |
| **WebSocket stream messages** | **Variable** | **Credit cost per message received** |

### WebSocket Stream Credit Costs

| Stream | Credits per Message | Description |
|--------|-------------------|-------------|
| `kol-feed` | **2** | **Real-time KOL trading activity** |

**Note:** WebSocket stream credits are deducted **per message received**, not per connection. Users with insufficient credits will receive credit warning messages instead of stream data.

### Credit Management

#### Setting Credit Costs

Credit costs are defined **directly in each route definition** using middleware functions in `src/routes/api.js`:

```javascript
// Example route with credit cost
router.get('/demo',
    verifyToken,
    checkEndpointAccess('/api/v1/demo'),
    checkCredits(1),        // Check user has 1 credit before processing
    consumeCredits(1),      // Consume 1 credit after successful response
    async (req, res) => {
        // Route handler logic...
    }
);

// To change credit costs, modify the numbers in the middleware:
checkCredits(2),        // Changed from 1 to 2
consumeCredits(2),      // Changed from 1 to 2
```

#### Database Functions

The system uses PostgreSQL functions for atomic credit operations:

```sql
-- Consume credits atomically
SELECT consume_credits(
    user_id,           -- User UUID
    credits_to_consume, -- Number of credits
    endpoint,          -- API endpoint
    method,            -- HTTP method
    ip_address,        -- User IP
    user_agent,        -- User agent
    request_id,        -- Unique request ID
    tier_id,           -- User's tier ID
    max_credits,       -- Monthly credit limit
    current_month      -- Current month for tracking
);
```

#### Tier-Based Credit Limits

| Tier | Monthly Credits | Behavior |
|------|----------------|----------|
| Free | 1,000 | Hard limit, requests blocked when exceeded |
| Basic | 10,000 | Hard limit, requests blocked when exceeded |
| Premium | 100,000 | Hard limit, requests blocked when exceeded |
| Enterprise | Unlimited (-1) | No credit consumption tracking |

### Credit Tracking

#### User Credit Information

Users can check their credit status via admin endpoints.

**Response:**
```json
{
  "user": {
    "credits_remaining": 9950,
    "credits_used_this_month": 50,
    "total_credits_purchased": 0,
    "max_credits_per_month": 10000,
    "tier_name": "basic"
  },
  "usage_summary": {
    "current_month": "2024-01",
    "credits_used": 50,
    "credits_remaining": 9950,
    "percentage_used": 0.5
  }
}
```

#### Credit Consumption Logging

All credit consumption is logged in the `api_usage_logs` table:

```sql
-- View recent credit usage
SELECT
    endpoint,
    method,
    credits_consumed,
    ip_address,
    created_at
FROM api_usage_logs
WHERE user_id = 'your-user-id'
ORDER BY created_at DESC
LIMIT 10;
```

### Credit Responses

#### Successful Request
```json
{
  "success": true,
  "data": { ... },
  "credits_remaining": 9949,
  "credits_used": 1
}
```

#### Insufficient Credits
```json
{
  "error": "Insufficient credits",
  "code": "INSUFFICIENT_CREDITS",
  "credits_remaining": 0,
  "credits_required": 5,
  "message": "You need 5 credits but only have 0 remaining"
}
```

#### Credit Limit Exceeded
```json
{
  "error": "Monthly credit limit exceeded",
  "code": "CREDIT_LIMIT_EXCEEDED",
  "credits_used_this_month": 10000,
  "max_credits_per_month": 10000,
  "message": "You have used all 10000 credits for this month"
}
```

### Admin Credit Management

Admins can manage user credits through the admin API:

#### View User Credits
```http
GET /admin/users/{userId}/credits
X-Admin-API-Key: YOUR_ADMIN_API_KEY
```

#### Add Credits to User
```http
POST /admin/users/{userId}/credits
X-Admin-API-Key: YOUR_ADMIN_API_KEY
Content-Type: application/json

{
  "credits_to_add": 1000,
  "reason": "Promotional bonus"
}
```

#### Reset Monthly Credits
```http
POST /admin/users/{userId}/credits/reset
X-Admin-API-Key: YOUR_ADMIN_API_KEY
```

#### Clear User Cache (After Database Changes)
```bash
# Clear all user caches (use after changing access_tiers.allowed_endpoints)
pnpm clear-cache

# Clear specific user cache by email
pnpm clear-cache <EMAIL>

# Clear specific user cache by API key
pnpm clear-cache sk_test_123...

# Show help and usage examples
pnpm clear-cache --help
```

**When to use:**
- After directly modifying `access_tiers.allowed_endpoints` in database
- After updating `users.tier_id` directly in database
- After changing access tier configurations
- When users report stale permissions or access issues

### Credit System Configuration

#### Environment Variables

```bash
# Credit system settings (optional)
CREDIT_TRACKING_ENABLED=true
CREDIT_GRACE_PERIOD=false
CREDIT_WARNING_THRESHOLD=100
```

#### Database Configuration

The credit system uses several database tables:

1. **users** - Stores current credit balances
2. **access_tiers** - Defines credit limits per tier
3. **api_usage_logs** - Tracks all credit consumption
4. **credit_transactions** - Records credit purchases/adjustments

#### Customizing Credit Costs

To modify credit costs for endpoints:

1. **Update route definitions** (`src/routes/api.js`):
```javascript
// Change from 1 to 2 credits for demo endpoint
router.get('/demo',
    verifyToken,
    checkEndpointAccess('/api/v1/demo'),
    checkCredits(2),        // Changed from 1 to 2
    consumeCredits(2),      // Changed from 1 to 2
    async (req, res) => {
        // Route handler logic...
    }
);
```

2. **Update documentation** to reflect new costs

3. **Restart the application** to apply changes

#### Monthly Credit Reset

Credits reset automatically on the 1st of each month via a database function:

```sql
-- Manual monthly reset (admin only)
SELECT reset_monthly_credits();
```

### Best Practices

1. **Monitor Usage**: Regularly check credit consumption patterns
2. **Set Alerts**: Implement alerts when users approach credit limits
3. **Tier Upgrades**: Provide clear upgrade paths for users hitting limits
4. **Cost Optimization**: Adjust credit costs based on actual resource usage
5. **Grace Period**: Consider implementing a grace period for slight overages

### Troubleshooting

#### Common Issues

1. **Credits not deducting**: Check if credit tracking is enabled
2. **Negative credits**: Database constraint prevents this
3. **Wrong credit costs**: Verify endpoint mapping in middleware
4. **Monthly reset issues**: Check database function execution

#### Debug Commands

```sql
-- Check user credit status
SELECT credits_remaining, credits_used_this_month, max_credits_per_month
FROM users u
JOIN access_tiers at ON u.tier_id = at.id
WHERE u.email = '<EMAIL>';

-- View recent credit transactions
SELECT * FROM api_usage_logs
WHERE user_id = 'user-uuid'
ORDER BY created_at DESC
LIMIT 20;
```

## WebSocket API

### Connection

Connect to the WebSocket server using your API key:

```javascript
// Development
const ws = new WebSocket('ws://localhost:3000/ws?apiKey=YOUR_API_KEY');

// Production
const ws = new WebSocket('wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY');
```

### Message Format

All WebSocket messages use JSON format:

```json
{
  "type": "message_type",
  "payload": {
    "key": "value"
  }
}
```

### Subscribe to Stream

```json
{
  "type": "subscribe",
  "payload": {
    "stream": "demo-stream"
  }
}
```

### Unsubscribe from Stream

```json
{
  "type": "unsubscribe",
  "payload": {
    "stream": "demo-stream"
  }
}
```

### Ping/Pong

```json
{
  "type": "ping"
}
```

### Available Streams

| Stream | Description | Required Tier | Credits/Message |
|--------|-------------|---------------|-----------------|
| **kol-feed** | **Real-time KOL trading activity** | **Basic+** | **2** |

#### KOL Feed Stream

The KOL feed provides real-time trading activity from Key Opinion Leaders (KOLs) in the cryptocurrency space. This stream delivers live transaction data including:

- **KOL trader information** (name, avatar, social profiles)
- **Token swap details** (buy/sell transactions)
- **Transaction amounts** and USD values
- **Token metadata** (symbols, names, logos, contract addresses)
- **Privacy handling** (private wallets show "private" instead of addresses)

**Example KOL Feed Message:**
```json
{
  "type": "stream_data",
  "stream": "kol-feed",
  "data": {
    "timestamp": 1748919450434,
    "type": "buy",
    "kol_label": "TraderSZ",
    "wallet": "********************************************",
    "kol_avatar": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/trader_avatar.jpg",
    "token_in": {
      "symbol": "SOL",
      "name": "Wrapped SOL",
      "logo": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",
      "amount": 13.0,
      "amount_string": "13.0",
      "amount_usd": 2095.16,
      "price": 161.17,
      "mint": "So11111111111111111111111111111111111111112"
    },
    "token_out": {
      "symbol": "USDUC",
      "name": "unstable coin",
      "logo": "https://example.com/usduc-logo.png",
      "amount": 294116.82,
      "amount_string": "294.12K",
      "amount_usd": 2041.57,
      "price": 0.006941,
      "mint": "CB9dDufT3ZuQXqqSfa1c5kY935TEreyBw9XJXxHKpump"
    },
    "signature": "5uTz1FZkUiZpe418qEUJ5a9w7bsnbhDNw7N4XouW9ufsTxBBQCE1FYqeipzjpcXqwWrH7Nji32hccBwso4NbErTr",
    "socials": [
      {
        "type": "x",
        "handle": "trader1sz",
        "followers": 658700
      }
    ]
  },
  "timestamp": 1748919450434
}

## WebSocket Management Endpoints

### Get WebSocket Info
```http
GET /ws-api/info
X-API-Key: YOUR_API_KEY
```

### List Available Streams
```http
GET /ws-api/streams
X-API-Key: YOUR_API_KEY
```

### Get Active Sessions
```http
GET /ws-api/sessions
X-API-Key: YOUR_API_KEY
```

### Disconnect Session
```http
DELETE /ws-api/sessions/{sessionId}
X-API-Key: YOUR_API_KEY
```

### WebSocket Statistics
```http
GET /ws-api/stats?days=7
X-API-Key: YOUR_API_KEY
```

### Stream Credit Management

#### Get Stream Credit Information
```http
GET /ws-api/credits
X-API-Key: YOUR_API_KEY
```

**Response:**
```json
{
  "success": true,
  "credit_info": {
    "credits_enabled": true,
    "user_credits": {
      "remaining": 9941,
      "used_this_month": 59,
      "tier": "basic"
    },
    "stream_costs": {
      "demo-stream": 1,
      "data-stream": 2,
      "kol-feed": 2,
      "analytics-stream": 5,
      "enterprise-stream": 1
    }
  }
}
```

#### Get Specific Stream Credit Info
```http
GET /ws-api/credits?stream=kol-feed
X-API-Key: YOUR_API_KEY
```

**Response:**
```json
{
  "success": true,
  "credit_info": {
    "credits_enabled": true,
    "user_credits": {
      "remaining": 9941,
      "used_this_month": 59,
      "tier": "basic"
    },
    "stream_cost": 2,
    "can_afford": true,
    "stream_stats": {
      "total_messages": 156,
      "total_credits_consumed": 312,
      "unique_users": 8
    }
  }
}
```

#### Get Stream Credit Usage Statistics
```http
GET /ws-api/credits/stats?days=7&stream=kol-feed
X-API-Key: YOUR_API_KEY
```

**Response:**
```json
{
  "success": true,
  "credit_stats": {
    "period_days": 7,
    "stream_filter": "kol-feed",
    "daily_usage": [
      {
        "date": "2024-01-15T00:00:00.000Z",
        "stream": "kol-feed",
        "credits_consumed": 24,
        "message_count": 12
      }
    ],
    "summary": {
      "total_credits": 156,
      "total_messages": 78,
      "avg_credits_per_day": 22
    }
  }
}
```

### Stream Credit Warnings

When users don't have sufficient credits for stream messages, they receive a warning instead of the stream data:

```json
{
  "type": "credit_warning",
  "stream": "kol-feed",
  "message": "Insufficient credits to receive stream data",
  "required_credits": 2,
  "timestamp": 1748919450434
}
```

## Error Handling

### HTTP Status Codes

- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `402` - Payment Required (Insufficient Credits)
- `403` - Forbidden (Insufficient Tier)
- `404` - Not Found
- `429` - Too Many Requests (Rate Limited)
- `500` - Internal Server Error

### Error Response Format

```json
{
  "error": "Error message",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/v1/endpoint",
  "method": "GET"
}
```

## Rate Limiting

Rate limits are applied based on user tiers:

- **Free**: 10 requests per 15 minutes
- **Basic**: 60 requests per 15 minutes  
- **Premium**: 300 requests per 15 minutes
- **Enterprise**: 1000 requests per 15 minutes

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

## Credit System

### Credit Consumption

Credits are consumed automatically when making API calls. The number of credits consumed depends on the endpoint:

- Simple endpoints: 1-2 credits
- Complex endpoints: 3-5 credits
- Batch operations: 10+ credits

### Credit Status

Check your credit status via admin endpoints.

Response includes:
```json
{
  "credits": {
    "credits_remaining": 950,
    "credits_used_this_month": 50,
    "max_credits_per_month": 1000,
    "tier_name": "free",
    "unlimited": false
  }
}
```

## Caching

The API uses Redis for caching to improve performance:

- User data: 5 minutes
- Analytics data: 5 minutes  
- Search results: 2 minutes
- Stream definitions: 10 minutes

Cache headers indicate if data was served from cache:

```json
{
  "data": {...},
  "cached": true
}
```

## Health Check

Monitor API health:

```http
GET /health
```

Response:
```json
{
  "status": "healthy",
  "services": {
    "database": "healthy",
    "redis": "healthy"
  },
  "uptime": 3600,
  "memory": {...},
  "version": "1.0.0"
}
```
