# Kafka Streams Guide

## Overview

The Kafka Streams integration provides real-time access to high-frequency trading data from multiple sources including Jupiter AMM swaps, Pump.fun AMM swaps, and Jupiter DCA orders. This enterprise-tier feature delivers live market data with minimal latency for advanced trading applications.

## Available Streams

### 1. Jupiter AMM Swaps (`jupiter-amm-swaps`)
- **Description**: Real-time Jupiter AMM swap transactions
- **Tier Required**: Enterprise
- **Credits per Message**: 0 (included with Enterprise tier)
- **Data Rate**: High frequency (1000+ messages/minute)
- **History Retention**: Last 1000 messages

### 2. Pump.fun AMM Swaps (`pumpfun-amm-swaps`)
- **Description**: Real-time Pump.fun AMM swap transactions
- **Tier Required**: Enterprise
- **Credits per Message**: 0 (included with Enterprise tier)
- **Data Rate**: High frequency (500+ messages/minute)
- **History Retention**: Last 1000 messages

### 3. Jupiter DCA Orders (`jupiter-dca-orders`)
- **Description**: Real-time Jupiter DCA (Dollar Cost Averaging) order data
- **Tier Required**: Enterprise
- **Credits per Message**: 0 (included with Enterprise tier)
- **Data Rate**: Medium frequency (100+ messages/minute)
- **History Retention**: Last 1000 messages

## Configuration

### Environment Variables

```bash
# Required for Kafka Streams
KAFKA_CLIENT_ID=stalkapi-consumer
KAFKA_BROKERS=your-kafka-broker:port

# SSL Configuration
KAFKA_CERT="-----BEGIN CERTIFICATE-----
...your kafka certificate...
-----END CERTIFICATE-----"

KAFKA_USER_CERT="-----BEGIN CERTIFICATE-----
...your user certificate...
-----END CERTIFICATE-----"

KAFKA_USER_KEY="-----BEGIN PRIVATE KEY-----
...your private key...
-----END PRIVATE KEY-----"
```

### Database Setup

The Kafka streams are configured in the database with Enterprise tier access:

```sql
-- Kafka stream definitions (already configured)
SELECT stream_name, description, required_tier_id, credits_per_message 
FROM stream_definitions 
WHERE stream_name IN ('jupiter-amm-swaps', 'pumpfun-amm-swaps', 'jupiter-dca-orders');
```

## Usage

### WebSocket Connection

Connect to the WebSocket server and subscribe to Kafka streams:

```javascript
const ws = new WebSocket('wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY');

ws.onopen = () => {
    // Subscribe to Jupiter AMM swaps
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: {
            stream: 'jupiter-amm-swaps'
        }
    }));
    
    // Subscribe to Pump.fun AMM swaps
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: {
            stream: 'pumpfun-amm-swaps'
        }
    }));
    
    // Subscribe to Jupiter DCA orders
    ws.send(JSON.stringify({
        type: 'subscribe',
        payload: {
            stream: 'jupiter-dca-orders'
        }
    }));
};

ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    
    if (message.type === 'stream_data') {
        switch(message.stream) {
            case 'jupiter-amm-swaps':
                console.log('Jupiter AMM Swap:', message.data);
                break;
            case 'pumpfun-amm-swaps':
                console.log('Pump.fun AMM Swap:', message.data);
                break;
            case 'jupiter-dca-orders':
                console.log('Jupiter DCA Order:', message.data);
                break;
        }
    }
    
    if (message.type === 'error') {
        console.log('Error:', message.error);
    }
};
```

### Historical Data API

Access the last 1000 transactions for each stream via REST API:

```bash
# Get Jupiter AMM swaps history
curl -H "X-API-Key: YOUR_API_KEY" \
  "https://data.stalkapi.com/api/v1/jupiter-amm-swaps/history?limit=100"

# Get Pump.fun AMM swaps history
curl -H "X-API-Key: YOUR_API_KEY" \
  "https://data.stalkapi.com/api/v1/pumpfun-amm-swaps/history?limit=100"

# Get Jupiter DCA orders history
curl -H "X-API-Key: YOUR_API_KEY" \
  "https://data.stalkapi.com/api/v1/jupiter-dca-orders/history?limit=100"

# With pagination (up to 1000 items per request)
curl -H "X-API-Key: YOUR_API_KEY" \
  "https://data.stalkapi.com/api/v1/jupiter-amm-swaps/history?limit=500&offset=100"
```

## Data Format

### Stream Message Structure

```json
{
  "type": "stream_data",
  "stream": "jupiter-amm-swaps",
  "data": {
    "timestamp": 1748913955272,
    "source": "jupiter-amm-swaps",
    "type": "jupiter_amm_swap",
    // Raw Kafka message data
    "...": "..."
  },
  "timestamp": 1748913955272
}
```

### Historical API Response

```json
{
  "success": true,
  "data": [
    {
      "timestamp": 1748913955272,
      "source": "jupiter-amm-swaps",
      "type": "jupiter_amm_swap",
      // Raw event data
      "...": "..."
    }
  ],
  "pagination": {
    "limit": 100,
    "offset": 0,
    "total": 1000,
    "returned": 100
  },
  "credits_consumed": 0,
  "message": "Jupiter AMM swaps history retrieved successfully"
}
```

## Access Control

### Tier Requirements
- **Enterprise Tier**: Full access to all Kafka streams
- **Premium/Basic/Free Tiers**: No access to Kafka streams

### Credit Costs
- **WebSocket Streaming**: 0 credits per message (included with Enterprise)
- **History API**: 0 credits per request (included with Enterprise)
- **Rate Limits**: Enterprise tier limits apply

## Error Handling

### Common Error Responses

```json
// Insufficient tier access
{
  "error": "Access denied - Enterprise tier required for Kafka streams",
  "required_tier": "enterprise"
}

// Note: History API is free for Enterprise users
// This error should not occur for valid Enterprise users

// Stream not available
{
  "type": "error",
  "error": "Stream not available or access denied",
  "stream": "jupiter-amm-swaps"
}
```

## Performance Considerations

### Message Rates
- **Jupiter AMM**: Very high frequency, expect 1000+ messages/minute
- **Pump.fun AMM**: High frequency, expect 500+ messages/minute  
- **Jupiter DCA**: Medium frequency, expect 100+ messages/minute

### Recommendations
- Use efficient JSON parsing for high-frequency streams
- Implement message queuing for processing bursts
- Consider subscribing to specific streams based on your use case
- Monitor your WebSocket connection for stability

## Integration Examples

### Python Example

```python
import asyncio
import websockets
import json

async def kafka_streams_client():
    uri = "wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY"
    
    async with websockets.connect(uri) as websocket:
        print("Connected to StalkAPI Kafka Streams")
        
        # Subscribe to all Kafka streams
        streams = ['jupiter-amm-swaps', 'pumpfun-amm-swaps', 'jupiter-dca-orders']
        for stream in streams:
            await websocket.send(json.dumps({
                "type": "subscribe",
                "payload": {"stream": stream}
            }))
        
        async for message in websocket:
            data = json.loads(message)
            
            if data["type"] == "stream_data":
                print(f"[{data['stream']}] New event: {data['data']['type']}")
            
            elif data["type"] == "error":
                print(f"Error: {data['error']}")

# Run the client
asyncio.run(kafka_streams_client())
```

This Kafka streams integration provides enterprise-grade access to real-time DeFi market data with comprehensive historical access and robust error handling.
