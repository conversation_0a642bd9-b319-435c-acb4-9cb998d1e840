# Enhanced Credit System - Fair Billing Guide

## Overview

The StalkAPI implements a **fair and transparent credit system** that ensures users are only charged for successful API calls. The enhanced system automatically handles credit consumption and provides accurate reporting in all response scenarios.

## 🎯 Core Principles

### **Fair Billing**
- **Credits consumed ONLY on successful responses (2xx status codes)**
- **No charges for server errors (5xx)**
- **No charges for client errors (4xx)**
- **No charges for authentication failures**
- **No charges for malformed requests**

### **Transparent Reporting**
- All responses include accurate `credits_consumed` information
- Error responses clearly indicate no credits were charged
- Credit notes explain billing decisions

## Credit Consumption Rules

### ✅ **Credits ARE Consumed For:**
- **200 OK**: Successful data retrieval
- **201 Created**: Successful resource creation
- **202 Accepted**: Request accepted for processing

### ❌ **Credits ARE NOT Consumed For:**
- **400 Bad Request**: Invalid request format, missing parameters
- **401 Unauthorized**: Authentication failures, invalid API keys
- **403 Forbidden**: Insufficient permissions, tier restrictions
- **404 Not Found**: Resource not found, endpoint not found
- **429 Too Many Requests**: Rate limiting
- **500 Internal Server Error**: Server-side errors
- **502 Bad Gateway**: Upstream service errors
- **503 Service Unavailable**: Temporary service issues

## Response Examples

### Successful Response (Credits Consumed)
```json
{
  "success": true,
  "data": {
    "tokenAddress": "4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump",
    "name": "DADDY TATE",
    "symbol": "DADDY"
  },
  "credits_consumed": 3,
  "message": "Token metadata retrieved successfully"
}
```

### Error Response (No Credits Consumed)
```json
{
  "success": false,
  "error": "Invalid JSON format in request body",
  "code": "INVALID_JSON",
  "credits_consumed": 0,
  "credit_note": "No credits charged for server errors",
  "timestamp": "2025-01-30T12:34:56.789Z"
}
```

## Implementation Details

### Enhanced Credit Middleware

The system uses intelligent middleware that:

1. **Tracks Credit Intent**: Stores intended credit consumption in `req.creditsToConsume`
2. **Intercepts Responses**: Monitors response status codes
3. **Makes Fair Decisions**: Only consumes credits for successful responses
4. **Provides Transparency**: Updates response with accurate credit information

```javascript
// Automatic credit handling
router.post('/endpoint',
  checkCredits(3),      // Verify user has enough credits
  consumeCredits(3),    // Set up credit consumption
  async (req, res) => {
    try {
      const data = await fetchData();
      res.json({ data }); // Credits consumed automatically
    } catch (error) {
      res.status(500).json({ error }); // No credits consumed
    }
  }
);
```

## Real-World Scenarios

### 1. Invalid JSON Request
**Request:**
```bash
curl -X POST /api/v1/core/token-metadata \
  -H "Authorization: Bearer your_token" \
  -d '{"invalid": json}'
```

**Response:**
```json
{
  "success": false,
  "error": "Invalid JSON format in request body",
  "code": "INVALID_JSON",
  "credits_consumed": 0,
  "hint": "Please check your request body format"
}
```

### 2. Authentication Failure
**Request:**
```bash
curl -X POST /api/v1/core/token-metadata \
  -H "Authorization: Bearer invalid_token" \
  -d '{"tokenAddress": ["valid_address"]}'
```

**Response:**
```json
{
  "success": false,
  "error": "Authentication token invalid",
  "code": "UNAUTHORIZED",
  "credits_consumed": 0,
  "hint": "Please check your authentication credentials"
}
```

### 3. Server Error
**Request:**
```bash
curl -X POST /api/v1/core/token-metadata \
  -H "Authorization: Bearer your_token" \
  -d '{"tokenAddress": ["valid_address"]}'
```

**Response (if server error occurs):**
```json
{
  "success": false,
  "error": "Failed to retrieve token metadata",
  "code": "INTERNAL_ERROR",
  "credits_consumed": 0,
  "credit_note": "No credits charged for server errors"
}
```

## Endpoint Credit Costs

| Endpoint | Credits | Fair Billing Applied |
|----------|---------|---------------------|
| `/core/token-price` | 3 | ✅ Only on success |
| `/core/token-metadata` | 3 | ✅ Only on success |
| `/smart-money/*` | 2 | ✅ Only on success |
| `/kol-feed/*` | 3 | ✅ Only on success |

## Testing the System

### Run Comprehensive Tests
```bash
# Test credit consumption scenarios
pnpm test:credit-reversal

# Test error handling
pnpm test:error-handling
```

### Manual Testing Examples
```bash
# Test successful request (should consume credits)
curl -X POST http://localhost:3001/api/v1/core/token-metadata \
  -H "Authorization: Bearer valid_token" \
  -H "Content-Type: application/json" \
  -d '{"tokenAddress": ["4Cnk9EPnW5ixfLZatCPJjDB1PUtcRpVVgTQukm9epump"]}'

# Test invalid JSON (should NOT consume credits)
curl -X POST http://localhost:3001/api/v1/core/token-metadata \
  -H "Authorization: Bearer valid_token" \
  -H "Content-Type: application/json" \
  -d '{"invalid": json}'

# Test authentication failure (should NOT consume credits)
curl -X POST http://localhost:3001/api/v1/core/token-metadata \
  -H "Authorization: Bearer invalid_token" \
  -H "Content-Type: application/json" \
  -d '{"tokenAddress": ["valid_address"]}'
```

## Benefits for Users

### 🎯 **Fair Billing**
- Only pay for successful API calls
- No charges for server issues
- No charges for your mistakes (invalid requests)

### 🔍 **Transparency**
- Clear credit consumption reporting
- Detailed error explanations
- Helpful hints for fixing issues

### 🛡️ **Protection**
- Automatic credit protection on errors
- No surprise charges
- Consistent billing across all endpoints

## Benefits for Developers

### 🚀 **Easy Implementation**
- Middleware handles all credit logic
- No manual credit management needed
- Automatic response updates

### 🔧 **Consistent Behavior**
- Same credit logic across all endpoints
- Standardized error responses
- Predictable billing behavior

### 📊 **Better Analytics**
- Accurate credit consumption data
- Clear success/failure metrics
- Fair usage tracking

## Migration from Old System

The enhanced system is **backward compatible** and automatically provides:

1. **Improved Accuracy**: Credit consumption now matches actual success/failure
2. **Better UX**: Users see accurate credit information in all responses
3. **Fair Billing**: No more charges for server errors or client mistakes
4. **Enhanced Transparency**: Clear explanations of credit decisions

## Security Features

### **Credit Fraud Prevention**
- Server-side credit consumption only
- Tamper-proof credit tracking
- Comprehensive audit logging

### **Automatic Protection**
- Credits protected on all error scenarios
- No manual intervention required
- Consistent protection across all endpoints

The enhanced credit system ensures **fair billing**, **transparency**, and **excellent user experience** while maintaining **security** and **ease of implementation**! 🎉
