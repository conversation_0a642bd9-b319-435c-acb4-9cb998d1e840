# SolanaTracker WebSocket Subscription Reference

## 🎯 Quick Start

### Connection
```javascript
const ws = new WebSocket('ws://localhost:3001/ws?apiKey=YOUR_API_KEY');
```

### Basic Subscription Format
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "stream-name"
  }
}
```

## 📋 All Available Streams

### ✅ Basic Streams (No Parameters)

#### 1. Latest Tokens (`tokens-launched`)
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "tokens-launched"
  }
}
```
- **SolanaTracker Room**: `latest`
- **Frequency**: High (expect data within 60s)
- **Description**: Latest tokens and pools launched on Solana

#### 2. Tokens Graduating (`tokens-graduating`)

**Basic Format (All Graduating Tokens):**
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "tokens-graduating"
  }
}
```

**Parameterized Format (Specific Market Cap Threshold):**
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "tokens-graduating",
    "parameters": {
      "marketCapThreshold": 175
    }
  }
}
```
- **SolanaTracker Room**: `graduating` or `graduating:sol:175`
- **Frequency**: Medium (expect data within 3min)
- **Description**: Tokens approaching graduation threshold
- **Parameters**: Optional `marketCapThreshold` (SOL amount)

#### 3. Tokens Graduated (`tokens-graduated`)
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "tokens-graduated"
  }
}
```
- **SolanaTracker Room**: `graduated`
- **Frequency**: Low (may timeout - normal)
- **Description**: Tokens that have graduated from bonding curve

### 🔧 Parameterized Streams

**Note**: These streams use the same subscription format but are configured with predefined test addresses internally.

#### 4. Pool Changes (`pool-changes`)
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "pool-changes"
  }
}
```
- **SolanaTracker Room**: `pool`
- **Frequency**: High
- **Description**: Real-time pool liquidity and price changes

#### 5. Token Transactions (`token-transactions`)
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "token-transactions"
  }
}
```
- **SolanaTracker Room**: `transaction`
- **Frequency**: Medium
- **Description**: Real-time token transaction data

#### 6. Price Updates (`price-updates`)
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "price-updates"
  }
}
```
- **SolanaTracker Room**: `price`
- **Frequency**: High
- **Description**: Token price updates and market data

#### 7. Wallet Transactions (`wallet-transactions`)
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "wallet-transactions"
  }
}
```
- **SolanaTracker Room**: `wallet`
- **Frequency**: Low
- **Description**: Wallet-specific transaction monitoring

#### 8. Token Metadata (`token-metadata`)
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "token-metadata"
  }
}
```
- **SolanaTracker Room**: `metadata`
- **Frequency**: Low
- **Description**: Token metadata updates and changes

#### 9. Token Holders (`token-holders`)
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "token-holders"
  }
}
```
- **SolanaTracker Room**: `holders`
- **Frequency**: Low
- **Description**: Token holder distribution and changes

#### 10. Token Changes (`token-changes`)
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "token-changes"
  }
}
```
- **SolanaTracker Room**: `token`
- **Frequency**: Low
- **Description**: General token state and property changes

## 📨 Response Messages

### Connection Established
```json
{
  "type": "connected",
  "connectionId": "unique-connection-id",
  "timestamp": 1703123456789
}
```

### Subscription Confirmed
```json
{
  "type": "subscribed",
  "stream": "tokens-graduating",
  "message": "Successfully subscribed to tokens-graduating",
  "timestamp": 1703123456789
}
```

### Stream Data Received
```json
{
  "type": "stream_data",
  "stream": "tokens-graduating",
  "data": {
    "token": {
      "name": "AINTERN",
      "symbol": "INTERN",
      "mint": "TokenAddress...",
      "description": "AI social media intern token"
    },
    "bondingCurveProgress": 85.2,
    "marketCap": 175000
  },
  "timestamp": 1703123456789
}
```

### Error Response
```json
{
  "type": "error",
  "error": "Access denied to stream: tokens-launched",
  "timestamp": 1703123456789
}
```

## 🔄 Unsubscription

```json
{
  "type": "unsubscribe",
  "payload": {
    "stream": "tokens-graduating"
  }
}
```

### Unsubscription Confirmed
```json
{
  "type": "unsubscribed",
  "stream": "tokens-graduating",
  "message": "Successfully unsubscribed from tokens-graduating",
  "timestamp": 1703123456789
}
```

## ❌ Common Errors

### 1. Invalid Stream Name
```json
{
  "type": "error",
  "error": "Stream name is required",
  "timestamp": 1703123456789
}
```

### 2. Access Denied
```json
{
  "type": "error",
  "error": "Access denied to stream: tokens-launched",
  "timestamp": 1703123456789
}
```
**Solution**: Ensure user has Premium+ tier (tier 3+)

### 3. Invalid Subscription Format
**❌ Wrong:**
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "tokens-graduating:sol:100"
  }
}
```

**✅ Correct:**
```json
{
  "type": "subscribe",
  "payload": {
    "stream": "tokens-graduating"
  }
}
```

## 🎯 Access Requirements

- **Required Tier**: Premium+ (tier 3 or higher)
- **Credit Cost**: 0 credits per message
- **Authentication**: Valid API key required
- **Connection Limit**: Based on user tier

## 📊 Testing Commands

```bash
# Check server status
node tests/check_server_status.js

# Test all streams
node tests/comprehensive_solana_tracker_test.js

# Verify configuration
node tests/verify_stream_subscription_handler.js
```

## 🔗 Related Documentation

- [Complete SolanaTracker Guide](./SOLANA_TRACKER_GUIDE.md)
- [WebSocket Guide](./WEBSOCKET_GUIDE.md)
- [Credit System Guide](./CREDIT_SYSTEM_GUIDE.md)
