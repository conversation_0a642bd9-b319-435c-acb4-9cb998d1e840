# Error Handling Guide

## Overview

The StalkAPI implements a comprehensive error handling system that sanitizes error responses to prevent sensitive information exposure while providing helpful feedback to users.

## Security Features

### 🔒 **Sensitive Information Protection**

The error handler automatically removes:
- **Stack traces** in production
- **File paths** and directory structures
- **Node modules** references
- **Internal server details**
- **Database connection strings**
- **Authentication tokens** in error messages

### 🛡️ **Error Sanitization**

Common error patterns are automatically sanitized:

| Original Error | Sanitized Response |
|----------------|-------------------|
| `Unexpected token ']', ...` | `Invalid JSON format in request body` |
| `SyntaxError: Unexpected end of JSON input` | `Incomplete JSON in request body` |
| `ECONNREFUSED 127.0.0.1:5432` | `Service temporarily unavailable` |
| `JWT malformed at verify (...)` | `Authentication token invalid` |

## Error Response Format

### Standard Error Response
```json
{
  "success": false,
  "error": "Invalid JSON format in request body",
  "code": "BAD_REQUEST",
  "hint": "Please check your request body format and ensure it contains valid JSON",
  "timestamp": "2025-01-30T12:34:56.789Z",
  "path": "/api/v1/core/token-metadata",
  "method": "POST"
}
```

### Error Codes

| HTTP Status | Code | Description |
|-------------|------|-------------|
| 400 | `BAD_REQUEST` | Invalid request format or data |
| 400 | `INVALID_JSON` | JSON parsing error |
| 401 | `UNAUTHORIZED` | Authentication required or invalid |
| 403 | `FORBIDDEN` | Insufficient permissions |
| 404 | `NOT_FOUND` | Endpoint or resource not found |
| 429 | `RATE_LIMITED` | Too many requests |
| 500 | `INTERNAL_ERROR` | Server error |

## Common Error Scenarios

### 1. JSON Parsing Errors

**Invalid JSON:**
```bash
curl -X POST http://localhost:3001/api/v1/core/token-metadata \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{"tokenAddress": ["invalid",]}'
```

**Response:**
```json
{
  "success": false,
  "error": "Invalid JSON format in request body",
  "code": "INVALID_JSON",
  "hint": "Please check your request body format and ensure it contains valid JSON",
  "timestamp": "2025-01-30T12:34:56.789Z",
  "path": "/api/v1/core/token-metadata",
  "method": "POST"
}
```

### 2. Authentication Errors

**Missing Token:**
```json
{
  "success": false,
  "error": "Authentication token invalid",
  "code": "UNAUTHORIZED",
  "hint": "Please check your authentication credentials and try again",
  "timestamp": "2025-01-30T12:34:56.789Z",
  "path": "/api/v1/core/token-metadata",
  "method": "POST"
}
```

### 3. Not Found Errors

**Invalid Endpoint:**
```json
{
  "success": false,
  "error": "Endpoint not found",
  "code": "NOT_FOUND",
  "hint": "Please check the URL and HTTP method",
  "path": "/api/v1/invalid-endpoint",
  "method": "GET",
  "timestamp": "2025-01-30T12:34:56.789Z",
  "available_endpoints": {
    "authentication": "/auth",
    "api": "/api/v1",
    "websocket_info": "/ws-api",
    "health": "/health",
    "documentation": "/docs"
  }
}
```

### 4. Rate Limiting Errors

**Too Many Requests:**
```json
{
  "success": false,
  "error": "Too many requests",
  "code": "RATE_LIMITED",
  "hint": "Please wait before making another request",
  "limit": 100,
  "windowMs": 60000,
  "retryAfter": 60,
  "timestamp": "2025-01-30T12:34:56.789Z"
}
```

## Implementation Details

### Middleware Stack

1. **JSON Error Handler** - Catches JSON parsing errors
2. **Route Handlers** - Process business logic
3. **404 Handler** - Handles unknown endpoints
4. **Global Error Handler** - Catches all other errors

### Error Handler Functions

#### `sanitizeErrorMessage(message)`
Removes sensitive information from error messages.

#### `getErrorStatusCode(error)`
Determines appropriate HTTP status code based on error type.

#### `createErrorResponse(error, req)`
Creates standardized error response with helpful hints.

## Development vs Production

### Development Mode
- Full error details logged to console
- Stack traces available in logs
- Detailed debugging information

### Production Mode
- Sanitized error logging
- No stack traces in responses
- Generic error messages for security

## Testing Error Handling

Run the error handling test suite:

```bash
pnpm test:error-handling
```

This tests:
- JSON parsing errors
- Authentication errors
- 404 errors
- Stack trace sanitization
- File path exposure prevention

## Best Practices

### For Developers

1. **Use Specific Error Types**
   ```javascript
   throw new Error('User not found'); // Good
   throw new Error(error.stack); // Bad - exposes stack
   ```

2. **Validate Input Early**
   ```javascript
   if (!tokenAddress) {
     return res.status(400).json({
       error: 'Token address is required'
     });
   }
   ```

3. **Log Errors Appropriately**
   ```javascript
   console.error('Database error:', error.message); // Good
   console.error('Error:', error); // Bad in production
   ```

### For API Users

1. **Check Error Codes**
   - Use the `code` field for programmatic error handling
   - Don't rely on error message text

2. **Follow Hints**
   - The `hint` field provides actionable guidance
   - Use it to improve your requests

3. **Handle Rate Limits**
   - Respect `retryAfter` values
   - Implement exponential backoff

## Security Considerations

### What's Protected
- ✅ Stack traces
- ✅ File paths
- ✅ Database connection details
- ✅ Internal server information
- ✅ Node modules references

### What's Exposed
- ✅ HTTP status codes
- ✅ Request path and method
- ✅ Timestamp
- ✅ Generic error descriptions
- ✅ Helpful hints for fixing issues

## Monitoring and Alerting

### Error Logging
All errors are logged with:
- Sanitized error message
- Request path and method
- User information (if available)
- Timestamp

### Metrics to Monitor
- Error rate by endpoint
- Authentication failure rate
- JSON parsing error frequency
- 404 error patterns

## Troubleshooting

### Common Issues

1. **"Invalid JSON format"**
   - Check JSON syntax
   - Ensure proper Content-Type header
   - Validate request body structure

2. **"Authentication token invalid"**
   - Verify API key format
   - Check token expiration
   - Ensure proper Authorization header

3. **"Service temporarily unavailable"**
   - Database connection issues
   - Redis connection problems
   - Check service health endpoint

### Debug Mode

Enable detailed error logging in development:

```bash
NODE_ENV=development node app.js
```

This provides full error details in console logs while keeping responses sanitized.
